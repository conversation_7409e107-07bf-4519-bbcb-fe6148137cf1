#!/usr/bin/env python3
"""
Test MPC method existence
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_mpc_method():
    """Test if _run_mpc_security_monitor method exists"""
    try:
        from bybit_bot.main import BybitTradingBotSystem
        
        # Create instance
        system = BybitTradingBotSystem()
        
        # Check if method exists
        if hasattr(system, '_run_mpc_security_monitor'):
            print("SUCCESS: _run_mpc_security_monitor method exists")
            print(f"Method type: {type(getattr(system, '_run_mpc_security_monitor'))}")
            return True
        else:
            print("ERROR: _run_mpc_security_monitor method NOT found")
            
            # List all methods starting with _run
            methods = [attr for attr in dir(system) if attr.startswith('_run')]
            print(f"Available _run methods: {methods}")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_mpc_method()
