#!/usr/bin/env python3
"""
Tier 1 Elastic Weight Consolidation (EWC)
Prevents catastrophic forgetting of profitable trading strategies
"""

import numpy as np
import asyncio
import logging
import sqlite3
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timezone
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class StrategyImportance:
    """Importance weights for strategy parameters"""
    strategy_name: str
    parameter_importance: Dict[str, float]  # Fisher Information Matrix diagonal
    parameter_values: Dict[str, float]      # Optimal parameter values
    performance_score: float                # Strategy performance score
    trade_count: int                       # Number of trades used to compute importance
    last_updated: datetime

@dataclass
class ConsolidationState:
    """EWC consolidation state"""
    consolidated_strategies: Dict[str, StrategyImportance]
    global_importance: Dict[str, float]     # Global parameter importance
    consolidation_strength: float          # Lambda parameter for EWC
    forgetting_threshold: float            # Threshold for detecting forgetting

class Tier1ElasticWeightConsolidation:
    """
    Elastic Weight Consolidation for preventing catastrophic forgetting
    Maintains memory of profitable strategies while allowing adaptation
    """
    
    def __init__(self, config: Any, db_path: str = "bybit_trading_bot.db"):
        self.config = config
        self.db_path = db_path
        
        # EWC parameters
        self.consolidation_strength = 1000.0  # Lambda parameter
        self.forgetting_threshold = 0.1       # Performance drop threshold
        self.importance_decay = 0.95          # Decay factor for importance weights
        
        # Consolidation state
        self.consolidation_state = ConsolidationState(
            consolidated_strategies={},
            global_importance={},
            consolidation_strength=self.consolidation_strength,
            forgetting_threshold=self.forgetting_threshold
        )
        
        # Performance tracking
        self.strategy_performance_history: Dict[str, List[float]] = defaultdict(list)
        self.consolidation_count = 0
        self.forgetting_prevention_count = 0
        
        # Parameter tracking for Fisher Information computation
        self.parameter_gradients: Dict[str, List[Dict[str, float]]] = defaultdict(list)
        self.parameter_history: Dict[str, List[Dict[str, float]]] = defaultdict(list)
        
        logger.info("Tier 1 Elastic Weight Consolidation initialized")
    
    async def initialize(self):
        """Initialize EWC system"""
        try:
            # Create database tables
            await self._create_tables()
            
            # Load existing consolidation state
            await self._load_consolidation_state()
            
            logger.info("Elastic Weight Consolidation initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize EWC: {e}")
            return False
    
    async def _create_tables(self):
        """Create database tables for EWC"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Strategy importance table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategy_importance (
                    strategy_name TEXT PRIMARY KEY,
                    parameter_importance TEXT,
                    parameter_values TEXT,
                    performance_score REAL,
                    trade_count INTEGER,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Global importance table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS global_parameter_importance (
                    parameter_name TEXT PRIMARY KEY,
                    importance_weight REAL,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Consolidation events table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS consolidation_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT,
                    event_type TEXT,
                    performance_before REAL,
                    performance_after REAL,
                    importance_change REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to create EWC tables: {e}")
            raise
    
    async def _load_consolidation_state(self):
        """Load consolidation state from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Load strategy importance
            cursor.execute("SELECT * FROM strategy_importance")
            rows = cursor.fetchall()
            
            for row in rows:
                strategy_importance = StrategyImportance(
                    strategy_name=row[0],
                    parameter_importance=json.loads(row[1]),
                    parameter_values=json.loads(row[2]),
                    performance_score=row[3],
                    trade_count=row[4],
                    last_updated=datetime.fromisoformat(row[5])
                )
                self.consolidation_state.consolidated_strategies[row[0]] = strategy_importance
            
            # Load global importance
            cursor.execute("SELECT * FROM global_parameter_importance")
            rows = cursor.fetchall()
            
            for row in rows:
                self.consolidation_state.global_importance[row[0]] = row[1]
            
            conn.close()
            logger.info(f"Loaded {len(self.consolidation_state.consolidated_strategies)} consolidated strategies")
            
        except Exception as e:
            logger.error(f"Failed to load consolidation state: {e}")
    
    async def consolidate_strategy(self, strategy_name: str, strategy_parameters: Dict[str, float], 
                                 performance_score: float, trade_count: int) -> bool:
        """
        Consolidate a successful strategy to prevent forgetting
        """
        try:
            # Calculate Fisher Information Matrix (diagonal approximation)
            fisher_information = await self._calculate_fisher_information(strategy_name, strategy_parameters)
            
            # Create or update strategy importance
            strategy_importance = StrategyImportance(
                strategy_name=strategy_name,
                parameter_importance=fisher_information,
                parameter_values=strategy_parameters.copy(),
                performance_score=performance_score,
                trade_count=trade_count,
                last_updated=datetime.now(timezone.utc)
            )
            
            # Store in consolidation state
            self.consolidation_state.consolidated_strategies[strategy_name] = strategy_importance
            
            # Update global importance weights
            await self._update_global_importance(fisher_information)
            
            # Save to database
            await self._save_strategy_importance(strategy_importance)
            
            # Log consolidation event
            await self._log_consolidation_event(strategy_name, "CONSOLIDATE", 0.0, performance_score, 
                                              np.mean(list(fisher_information.values())))
            
            self.consolidation_count += 1
            logger.info(f"Consolidated strategy '{strategy_name}' with performance {performance_score:.4f}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to consolidate strategy {strategy_name}: {e}")
            return False
    
    async def _calculate_fisher_information(self, strategy_name: str, parameters: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate Fisher Information Matrix (diagonal approximation)
        Measures parameter importance based on gradient variance
        """
        fisher_info = {}
        
        # Get parameter gradients history for this strategy
        gradients_history = self.parameter_gradients.get(strategy_name, [])
        
        if len(gradients_history) < 2:
            # Default importance for new strategies
            for param_name in parameters.keys():
                fisher_info[param_name] = 1.0
        else:
            # Calculate variance of gradients (Fisher Information approximation)
            for param_name in parameters.keys():
                gradients = [grad.get(param_name, 0.0) for grad in gradients_history]
                if len(gradients) > 1:
                    variance = np.var(gradients)
                    fisher_info[param_name] = max(variance, 0.01)  # Minimum importance
                else:
                    fisher_info[param_name] = 1.0
        
        return fisher_info
    
    async def _update_global_importance(self, new_importance: Dict[str, float]):
        """Update global parameter importance weights"""
        for param_name, importance in new_importance.items():
            if param_name in self.consolidation_state.global_importance:
                # Exponential moving average
                current_importance = self.consolidation_state.global_importance[param_name]
                updated_importance = 0.9 * current_importance + 0.1 * importance
                self.consolidation_state.global_importance[param_name] = updated_importance
            else:
                self.consolidation_state.global_importance[param_name] = importance
        
        # Save to database
        await self._save_global_importance()
    
    async def _save_strategy_importance(self, strategy_importance: StrategyImportance):
        """Save strategy importance to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO strategy_importance 
                (strategy_name, parameter_importance, parameter_values, performance_score, trade_count)
                VALUES (?, ?, ?, ?, ?)
            """, (
                strategy_importance.strategy_name,
                json.dumps(strategy_importance.parameter_importance),
                json.dumps(strategy_importance.parameter_values),
                strategy_importance.performance_score,
                strategy_importance.trade_count
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to save strategy importance: {e}")
    
    async def _save_global_importance(self):
        """Save global importance weights to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for param_name, importance in self.consolidation_state.global_importance.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO global_parameter_importance 
                    (parameter_name, importance_weight)
                    VALUES (?, ?)
                """, (param_name, importance))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to save global importance: {e}")
    
    async def apply_ewc_regularization(self, strategy_name: str, current_parameters: Dict[str, float], 
                                     proposed_parameters: Dict[str, float]) -> Dict[str, float]:
        """
        Apply EWC regularization to prevent forgetting
        Returns regularized parameters
        """
        try:
            if strategy_name not in self.consolidation_state.consolidated_strategies:
                # No consolidation for this strategy yet
                return proposed_parameters.copy()
            
            strategy_importance = self.consolidation_state.consolidated_strategies[strategy_name]
            regularized_parameters = {}
            
            for param_name, proposed_value in proposed_parameters.items():
                if param_name in strategy_importance.parameter_importance:
                    # Get importance weight and consolidated value
                    importance = strategy_importance.parameter_importance[param_name]
                    consolidated_value = strategy_importance.parameter_values[param_name]
                    
                    # Apply EWC regularization
                    # New_param = proposed_param - lambda * importance * (proposed_param - consolidated_param)
                    regularization_term = self.consolidation_strength * importance * (proposed_value - consolidated_value)
                    regularized_value = proposed_value - (regularization_term * 0.01)  # Scale down for stability
                    
                    regularized_parameters[param_name] = regularized_value
                else:
                    # No consolidation for this parameter
                    regularized_parameters[param_name] = proposed_value
            
            # Check for potential forgetting
            forgetting_detected = await self._detect_forgetting(strategy_name, current_parameters, regularized_parameters)
            
            if forgetting_detected:
                self.forgetting_prevention_count += 1
                logger.warning(f"Forgetting detected for strategy '{strategy_name}', applying stronger regularization")
                
                # Apply stronger regularization
                for param_name in regularized_parameters.keys():
                    if param_name in strategy_importance.parameter_importance:
                        consolidated_value = strategy_importance.parameter_values[param_name]
                        # Blend more heavily with consolidated value
                        regularized_parameters[param_name] = 0.7 * consolidated_value + 0.3 * regularized_parameters[param_name]
            
            return regularized_parameters
            
        except Exception as e:
            logger.error(f"Failed to apply EWC regularization: {e}")
            return proposed_parameters.copy()
    
    async def _detect_forgetting(self, strategy_name: str, current_params: Dict[str, float], 
                               new_params: Dict[str, float]) -> bool:
        """Detect potential catastrophic forgetting"""
        try:
            if strategy_name not in self.consolidation_state.consolidated_strategies:
                return False
            
            strategy_importance = self.consolidation_state.consolidated_strategies[strategy_name]
            
            # Calculate parameter drift weighted by importance
            total_drift = 0.0
            total_importance = 0.0
            
            for param_name, new_value in new_params.items():
                if param_name in strategy_importance.parameter_importance:
                    consolidated_value = strategy_importance.parameter_values[param_name]
                    importance = strategy_importance.parameter_importance[param_name]
                    
                    drift = abs(new_value - consolidated_value) / max(abs(consolidated_value), 1e-6)
                    weighted_drift = drift * importance
                    
                    total_drift += weighted_drift
                    total_importance += importance
            
            if total_importance > 0:
                avg_drift = total_drift / total_importance
                return avg_drift > self.forgetting_threshold
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to detect forgetting: {e}")
            return False
    
    async def record_parameter_gradients(self, strategy_name: str, gradients: Dict[str, float]):
        """Record parameter gradients for Fisher Information calculation"""
        try:
            self.parameter_gradients[strategy_name].append(gradients.copy())
            
            # Keep only recent gradients (memory efficiency)
            max_history = 100
            if len(self.parameter_gradients[strategy_name]) > max_history:
                self.parameter_gradients[strategy_name] = self.parameter_gradients[strategy_name][-max_history:]
            
        except Exception as e:
            logger.error(f"Failed to record gradients: {e}")
    
    async def record_parameter_history(self, strategy_name: str, parameters: Dict[str, float]):
        """Record parameter history for analysis"""
        try:
            self.parameter_history[strategy_name].append(parameters.copy())
            
            # Keep only recent history
            max_history = 100
            if len(self.parameter_history[strategy_name]) > max_history:
                self.parameter_history[strategy_name] = self.parameter_history[strategy_name][-max_history:]
            
        except Exception as e:
            logger.error(f"Failed to record parameter history: {e}")
    
    async def _log_consolidation_event(self, strategy_name: str, event_type: str, 
                                     performance_before: float, performance_after: float, 
                                     importance_change: float):
        """Log consolidation events for analysis"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO consolidation_events 
                (strategy_name, event_type, performance_before, performance_after, importance_change)
                VALUES (?, ?, ?, ?, ?)
            """, (strategy_name, event_type, performance_before, performance_after, importance_change))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to log consolidation event: {e}")
    
    async def get_consolidation_stats(self) -> Dict[str, Any]:
        """Get consolidation statistics"""
        return {
            'consolidation_count': self.consolidation_count,
            'forgetting_prevention_count': self.forgetting_prevention_count,
            'consolidated_strategies': len(self.consolidation_state.consolidated_strategies),
            'global_parameters_tracked': len(self.consolidation_state.global_importance),
            'consolidation_strength': self.consolidation_strength,
            'forgetting_threshold': self.forgetting_threshold,
            'strategy_names': list(self.consolidation_state.consolidated_strategies.keys())
        }
    
    async def get_strategy_importance(self, strategy_name: str) -> Optional[StrategyImportance]:
        """Get importance weights for a specific strategy"""
        return self.consolidation_state.consolidated_strategies.get(strategy_name)
    
    async def update_consolidation_strength(self, new_strength: float):
        """Update consolidation strength (lambda parameter)"""
        self.consolidation_strength = new_strength
        self.consolidation_state.consolidation_strength = new_strength
        logger.info(f"Updated consolidation strength to {new_strength}")
