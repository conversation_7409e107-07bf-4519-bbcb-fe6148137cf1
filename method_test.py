#!/usr/bin/env python3
"""
Test for _run_mpc_security_monitor method existence
"""

import sys
sys.path.insert(0, '.')

print("TESTING MPC SECURITY MONITOR METHOD")
print("=" * 50)

try:
    # Import the main system
    from bybit_bot.main import BybitTradingBotSystem
    print("✓ Main system imported successfully")
    
    # Create system instance
    system = BybitTradingBotSystem()
    print("✓ System instance created")
    
    # Check if method exists
    if hasattr(system, '_run_mpc_security_monitor'):
        print("✓ _run_mpc_security_monitor method EXISTS")
        
        # Check if it's callable
        if callable(getattr(system, '_run_mpc_security_monitor')):
            print("✓ _run_mpc_security_monitor is CA<PERSON><PERSON><PERSON>")
        else:
            print("✗ _run_mpc_security_monitor is NOT callable")
    else:
        print("✗ _run_mpc_security_monitor method MISSING")
        
        # List all methods that contain 'mpc'
        mpc_methods = [method for method in dir(system) if 'mpc' in method.lower()]
        print(f"Available MPC-related methods: {mpc_methods}")
    
    # Check other critical methods
    critical_methods = [
        'start_all_engines', 'run', 'cleanup', 
        'initialize_components', 'initialize_all_systems'
    ]
    
    print("\nChecking other critical methods:")
    for method_name in critical_methods:
        if hasattr(system, method_name):
            print(f"✓ {method_name}")
        else:
            print(f"✗ {method_name} MISSING")
    
    print("\n✅ METHOD TESTING COMPLETE")
    
except Exception as e:
    print(f"✗ Error during testing: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
