#!/usr/bin/env python3
"""
Real-Time Model Adaptation System
Implements online learning with experience replay, elastic weight consolidation, and meta-learning

Features:
- Online learning with adaptive learning rates
- Experience replay buffer for continual learning
- Elastic Weight Consolidation (EWC) for catastrophic forgetting prevention
- Meta-learning for rapid adaptation to new market regimes
- Model ensemble with dynamic weighting
- Continuous performance monitoring and adaptation
"""

import asyncio
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
import time
import logging
from datetime import datetime
import copy
import pickle
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class AdaptiveLearningConfig:
    """Configuration for adaptive learning system"""
    model_dim: int = 128
    num_layers: int = 3
    learning_rate: float = 0.001
    meta_learning_rate: float = 0.01
    buffer_size: int = 10000
    batch_size: int = 64
    ewc_lambda: float = 1000.0
    adaptation_steps: int = 5
    ensemble_size: int = 5
    update_frequency: int = 10
    performance_window: int = 100
    regime_detection_threshold: float = 0.1

class ExperienceReplayBuffer:
    """Experience replay buffer for continual learning"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self.priorities = deque(maxlen=capacity)
        self.position = 0
        
    def push(self, experience: Tuple[np.ndarray, float], priority: float = 1.0):
        """Add experience to buffer"""
        self.buffer.append(experience)
        self.priorities.append(priority)
    
    def sample(self, batch_size: int) -> List[Tuple[np.ndarray, float]]:
        """Sample batch from buffer with priority sampling"""
        if len(self.buffer) < batch_size:
            return list(self.buffer)
        
        # Convert priorities to probabilities
        priorities = np.array(list(self.priorities))
        probabilities = priorities / np.sum(priorities)
        
        # Sample indices based on priorities
        indices = np.random.choice(len(self.buffer), size=batch_size, p=probabilities, replace=False)
        
        return [self.buffer[i] for i in indices]
    
    def update_priorities(self, indices: List[int], priorities: List[float]):
        """Update priorities for specific experiences"""
        for idx, priority in zip(indices, priorities):
            if 0 <= idx < len(self.priorities):
                self.priorities[idx] = priority
    
    def __len__(self):
        return len(self.buffer)

class AdaptiveNeuralNetwork(nn.Module):
    """Adaptive neural network with meta-learning capabilities"""
    
    def __init__(self, input_dim: int, config: AdaptiveLearningConfig):
        super().__init__()
        self.config = config
        self.input_dim = input_dim
        
        # Main network layers
        layers = []
        current_dim = input_dim
        
        for i in range(config.num_layers):
            layers.append(nn.Linear(current_dim, config.model_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(0.1))
            current_dim = config.model_dim
        
        # Output layer
        layers.append(nn.Linear(current_dim, 1))
        
        self.network = nn.Sequential(*layers)
        
        # Meta-learning parameters
        self.meta_parameters = {}
        for name, param in self.named_parameters():
            self.meta_parameters[name] = param.clone().detach()
        
        # EWC parameters
        self.fisher_information = {}
        self.optimal_parameters = {}
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass"""
        return self.network(x)
    
    def compute_fisher_information(self, data_loader):
        """Compute Fisher Information Matrix for EWC"""
        self.fisher_information = {}
        
        # Initialize Fisher information
        for name, param in self.named_parameters():
            self.fisher_information[name] = torch.zeros_like(param)
        
        # Compute Fisher information
        self.eval()
        for batch_x, batch_y in data_loader:
            self.zero_grad()
            output = self(batch_x)
            loss = F.mse_loss(output.squeeze(), batch_y)
            loss.backward()
            
            for name, param in self.named_parameters():
                if param.grad is not None:
                    self.fisher_information[name] += param.grad.data ** 2
        
        # Normalize by number of samples
        num_samples = len(data_loader.dataset)
        for name in self.fisher_information:
            self.fisher_information[name] /= num_samples
        
        # Store optimal parameters
        self.optimal_parameters = {}
        for name, param in self.named_parameters():
            self.optimal_parameters[name] = param.clone().detach()
    
    def ewc_loss(self) -> torch.Tensor:
        """Compute EWC regularization loss"""
        loss = 0
        for name, param in self.named_parameters():
            if name in self.fisher_information and name in self.optimal_parameters:
                loss += (self.fisher_information[name] * 
                        (param - self.optimal_parameters[name]) ** 2).sum()
        return loss
    
    def meta_update(self, meta_gradients: Dict[str, torch.Tensor], meta_lr: float):
        """Update meta-parameters using meta-gradients"""
        with torch.no_grad():
            for name, param in self.named_parameters():
                if name in meta_gradients:
                    self.meta_parameters[name] -= meta_lr * meta_gradients[name]
                    param.data = self.meta_parameters[name].clone()

class ModelEnsemble:
    """Ensemble of adaptive models with dynamic weighting"""
    
    def __init__(self, input_dim: int, config: AdaptiveLearningConfig):
        self.config = config
        self.models = []
        self.weights = []
        self.performance_history = []
        
        # Initialize ensemble models
        for i in range(config.ensemble_size):
            model = AdaptiveNeuralNetwork(input_dim, config)
            self.models.append(model)
            self.weights.append(1.0 / config.ensemble_size)
            self.performance_history.append(deque(maxlen=config.performance_window))
    
    def predict(self, x: torch.Tensor) -> torch.Tensor:
        """Make ensemble prediction"""
        predictions = []
        
        for model in self.models:
            model.eval()
            with torch.no_grad():
                pred = model(x)
                predictions.append(pred)
        
        # Weighted average
        weighted_pred = torch.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            weighted_pred += self.weights[i] * pred
        
        return weighted_pred
    
    def update_weights(self, individual_losses: List[float]):
        """Update ensemble weights based on performance"""
        # Store performance
        for i, loss in enumerate(individual_losses):
            self.performance_history[i].append(loss)
        
        # Calculate weights based on recent performance
        if all(len(hist) >= 10 for hist in self.performance_history):
            avg_losses = [np.mean(list(hist)[-10:]) for hist in self.performance_history]
            
            # Inverse performance weighting (lower loss = higher weight)
            inv_losses = [1.0 / (loss + 1e-8) for loss in avg_losses]
            total_inv = sum(inv_losses)
            
            self.weights = [inv_loss / total_inv for inv_loss in inv_losses]
    
    def get_best_model(self) -> AdaptiveNeuralNetwork:
        """Get the best performing model"""
        if not all(len(hist) >= 5 for hist in self.performance_history):
            return self.models[0]
        
        avg_losses = [np.mean(list(hist)[-5:]) for hist in self.performance_history]
        best_idx = np.argmin(avg_losses)
        return self.models[best_idx]

class MarketRegimeDetector:
    """Detects market regime changes for model adaptation"""
    
    def __init__(self, window_size: int = 50, threshold: float = 0.1):
        self.window_size = window_size
        self.threshold = threshold
        self.returns_history = deque(maxlen=window_size * 2)
        self.volatility_history = deque(maxlen=window_size)
        self.current_regime = 'normal'
        self.regime_changes = deque(maxlen=100)
        
    def update(self, return_value: float, timestamp: float):
        """Update with new return data"""
        self.returns_history.append(return_value)
        
        if len(self.returns_history) >= self.window_size:
            # Calculate rolling volatility
            recent_returns = list(self.returns_history)[-self.window_size:]
            volatility = np.std(recent_returns)
            self.volatility_history.append(volatility)
            
            # Detect regime change
            if len(self.volatility_history) >= 2:
                self._detect_regime_change(timestamp)
    
    def _detect_regime_change(self, timestamp: float):
        """Detect if market regime has changed"""
        if len(self.volatility_history) < 10:
            return
        
        current_vol = np.mean(list(self.volatility_history)[-5:])
        historical_vol = np.mean(list(self.volatility_history)[-20:-5])
        
        vol_change = abs(current_vol - historical_vol) / (historical_vol + 1e-8)
        
        new_regime = self.current_regime
        
        if vol_change > self.threshold:
            if current_vol > historical_vol * 1.5:
                new_regime = 'high_volatility'
            elif current_vol < historical_vol * 0.5:
                new_regime = 'low_volatility'
            else:
                new_regime = 'normal'
        
        if new_regime != self.current_regime:
            self.regime_changes.append({
                'timestamp': timestamp,
                'old_regime': self.current_regime,
                'new_regime': new_regime,
                'vol_change': vol_change
            })
            self.current_regime = new_regime
            logger.info(f"Market regime changed to: {new_regime}")
    
    def get_current_regime(self) -> str:
        """Get current market regime"""
        return self.current_regime
    
    def has_regime_changed(self, lookback_seconds: int = 300) -> bool:
        """Check if regime changed recently"""
        if not self.regime_changes:
            return False
        
        current_time = time.time()
        recent_changes = [change for change in self.regime_changes 
                         if current_time - change['timestamp'] <= lookback_seconds]
        
        return len(recent_changes) > 0

class AdaptiveLearningSystem:
    """Main adaptive learning system"""
    
    def __init__(self, input_dim: int, config: AdaptiveLearningConfig):
        self.config = config
        self.input_dim = input_dim
        
        # Initialize components
        self.ensemble = ModelEnsemble(input_dim, config)
        self.replay_buffer = ExperienceReplayBuffer(config.buffer_size)
        self.regime_detector = MarketRegimeDetector()
        
        # Optimizers
        self.optimizers = []
        for model in self.ensemble.models:
            optimizer = optim.Adam(model.parameters(), lr=config.learning_rate)
            self.optimizers.append(optimizer)
        
        # Meta-optimizer
        self.meta_optimizer = optim.Adam(
            [param for model in self.ensemble.models for param in model.parameters()],
            lr=config.meta_learning_rate
        )
        
        # Performance tracking
        self.training_losses = deque(maxlen=1000)
        self.prediction_errors = deque(maxlen=1000)
        self.adaptation_history = deque(maxlen=100)
        self.update_counter = 0
        
        # Scaling
        self.feature_scaler = None
        self.target_scaler = None
        
    async def update(self, features: np.ndarray, target: float, timestamp: float) -> Dict[str, Any]:
        """Update the adaptive learning system"""
        self.update_counter += 1
        
        # Detect regime changes
        if len(features) > 0:
            # Use first feature as proxy for returns
            return_proxy = features[0] if len(features) > 0 else 0.0
            self.regime_detector.update(return_proxy, timestamp)
        
        # Add to replay buffer
        priority = self._calculate_priority(features, target)
        self.replay_buffer.push((features, target), priority)
        
        # Perform updates
        update_result = {}
        
        if self.update_counter % self.config.update_frequency == 0:
            update_result = await self._perform_batch_update()
        
        # Check for regime change and adapt if necessary
        if self.regime_detector.has_regime_changed():
            adaptation_result = await self._adapt_to_regime_change()
            update_result.update(adaptation_result)
        
        return update_result
    
    async def predict(self, features: np.ndarray) -> Dict[str, Any]:
        """Make prediction using ensemble"""
        start_time = time.time()
        
        # Scale features if scaler is available
        if self.feature_scaler is not None:
            features_scaled = self.feature_scaler.transform(features.reshape(1, -1))[0]
        else:
            features_scaled = features
        
        # Convert to tensor
        x = torch.FloatTensor(features_scaled).unsqueeze(0)
        
        # Ensemble prediction
        ensemble_pred = self.ensemble.predict(x)
        
        # Individual model predictions for uncertainty estimation
        individual_preds = []
        for model in self.ensemble.models:
            model.eval()
            with torch.no_grad():
                pred = model(x)
                individual_preds.append(pred.item())
        
        # Calculate uncertainty
        uncertainty = np.std(individual_preds)
        
        prediction_time = time.time() - start_time
        
        result = {
            'prediction': ensemble_pred.item(),
            'uncertainty': uncertainty,
            'individual_predictions': individual_preds,
            'ensemble_weights': self.ensemble.weights.copy(),
            'current_regime': self.regime_detector.get_current_regime(),
            'prediction_time': prediction_time
        }
        
        return result
    
    async def _perform_batch_update(self) -> Dict[str, Any]:
        """Perform batch update using replay buffer"""
        if len(self.replay_buffer) < self.config.batch_size:
            return {'status': 'insufficient_data'}
        
        # Sample batch from replay buffer
        batch = self.replay_buffer.sample(self.config.batch_size)
        
        # Prepare data
        features_batch = np.array([exp[0] for exp in batch])
        targets_batch = np.array([exp[1] for exp in batch])
        
        # Scale data
        if self.feature_scaler is None:
            from sklearn.preprocessing import StandardScaler
            self.feature_scaler = StandardScaler()
            features_batch = self.feature_scaler.fit_transform(features_batch)
        else:
            features_batch = self.feature_scaler.transform(features_batch)
        
        # Convert to tensors
        x_batch = torch.FloatTensor(features_batch)
        y_batch = torch.FloatTensor(targets_batch)
        
        # Train each model in ensemble
        individual_losses = []
        
        for i, (model, optimizer) in enumerate(zip(self.ensemble.models, self.optimizers)):
            model.train()
            optimizer.zero_grad()
            
            # Forward pass
            predictions = model(x_batch).squeeze()
            
            # Compute loss
            mse_loss = F.mse_loss(predictions, y_batch)
            ewc_loss = model.ewc_loss()
            total_loss = mse_loss + self.config.ewc_lambda * ewc_loss
            
            # Backward pass
            total_loss.backward()
            optimizer.step()
            
            individual_losses.append(mse_loss.item())
        
        # Update ensemble weights
        self.ensemble.update_weights(individual_losses)
        
        # Track performance
        avg_loss = np.mean(individual_losses)
        self.training_losses.append(avg_loss)
        
        return {
            'status': 'updated',
            'avg_loss': avg_loss,
            'individual_losses': individual_losses,
            'ensemble_weights': self.ensemble.weights.copy()
        }
    
    async def _adapt_to_regime_change(self) -> Dict[str, Any]:
        """Adapt models to regime change using meta-learning"""
        logger.info("Adapting to regime change...")
        
        # Get recent data for adaptation
        if len(self.replay_buffer) < self.config.adaptation_steps:
            return {'status': 'insufficient_data_for_adaptation'}
        
        recent_batch = self.replay_buffer.sample(min(self.config.adaptation_steps, len(self.replay_buffer)))
        
        # Prepare adaptation data
        features_adapt = np.array([exp[0] for exp in recent_batch])
        targets_adapt = np.array([exp[1] for exp in recent_batch])
        
        if self.feature_scaler is not None:
            features_adapt = self.feature_scaler.transform(features_adapt)
        
        x_adapt = torch.FloatTensor(features_adapt)
        y_adapt = torch.FloatTensor(targets_adapt)
        
        # Perform meta-learning adaptation
        adaptation_losses = []
        
        for model in self.ensemble.models:
            # Save current parameters
            original_params = {}
            for name, param in model.named_parameters():
                original_params[name] = param.clone()
            
            # Fast adaptation
            for step in range(self.config.adaptation_steps):
                model.train()
                predictions = model(x_adapt).squeeze()
                loss = F.mse_loss(predictions, y_adapt)
                
                # Compute gradients
                grads = torch.autograd.grad(loss, model.parameters(), create_graph=True)
                
                # Update parameters
                with torch.no_grad():
                    for param, grad in zip(model.parameters(), grads):
                        param -= self.config.meta_learning_rate * grad
            
            # Evaluate adaptation
            model.eval()
            with torch.no_grad():
                adapted_predictions = model(x_adapt).squeeze()
                adaptation_loss = F.mse_loss(adapted_predictions, y_adapt).item()
                adaptation_losses.append(adaptation_loss)
            
            # Update Fisher information for EWC
            if len(recent_batch) >= 5:
                # Create simple data loader
                dataset = torch.utils.data.TensorDataset(x_adapt, y_adapt)
                data_loader = torch.utils.data.DataLoader(dataset, batch_size=len(dataset))
                model.compute_fisher_information(data_loader)
        
        self.adaptation_history.append({
            'timestamp': time.time(),
            'regime': self.regime_detector.get_current_regime(),
            'adaptation_losses': adaptation_losses
        })
        
        return {
            'status': 'adapted',
            'adaptation_losses': adaptation_losses,
            'new_regime': self.regime_detector.get_current_regime()
        }
    
    def _calculate_priority(self, features: np.ndarray, target: float) -> float:
        """Calculate priority for experience replay"""
        if len(self.prediction_errors) == 0:
            return 1.0
        
        # Use recent prediction error as priority
        recent_error = np.mean(list(self.prediction_errors)[-10:]) if len(self.prediction_errors) >= 10 else 1.0
        return max(0.1, recent_error)  # Minimum priority
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get system performance metrics"""
        metrics = {
            'training_samples': len(self.replay_buffer),
            'avg_training_loss': np.mean(self.training_losses) if self.training_losses else 0,
            'avg_prediction_error': np.mean(self.prediction_errors) if self.prediction_errors else 0,
            'current_regime': self.regime_detector.get_current_regime(),
            'regime_changes': len(self.regime_detector.regime_changes),
            'adaptations_performed': len(self.adaptation_history),
            'ensemble_weights': self.ensemble.weights.copy(),
            'update_counter': self.update_counter
        }
        
        # Recent performance
        if len(self.training_losses) >= 10:
            recent_losses = list(self.training_losses)[-10:]
            metrics['recent_avg_loss'] = np.mean(recent_losses)
            metrics['loss_trend'] = np.polyfit(range(len(recent_losses)), recent_losses, 1)[0]
        
        return metrics

# Example usage
async def main():
    """Example usage of adaptive learning system"""
    
    config = AdaptiveLearningConfig(
        model_dim=64,
        num_layers=2,
        learning_rate=0.001,
        buffer_size=1000,
        batch_size=32,
        ensemble_size=3,
        update_frequency=10
    )
    
    # Initialize system
    input_dim = 10
    system = AdaptiveLearningSystem(input_dim, config)
    
    print("Training Adaptive Learning System...")
    
    # Simulate training data
    for i in range(500):
        # Generate features and target
        features = np.random.randn(input_dim)
        
        # Simulate regime change at step 250
        if i < 250:
            target = np.sum(features[:5]) + np.random.normal(0, 0.1)
        else:
            target = np.sum(features[5:]) + np.random.normal(0, 0.1)
        
        # Update system
        update_result = await system.update(features, target, time.time())
        
        # Make prediction
        if i % 50 == 0:
            pred_result = await system.predict(features)
            print(f"Step {i}: Prediction = {pred_result['prediction']:.4f}, "
                  f"Target = {target:.4f}, Uncertainty = {pred_result['uncertainty']:.4f}")
            
            if 'status' in update_result:
                print(f"  Update: {update_result['status']}")
    
    # Final metrics
    metrics = system.get_performance_metrics()
    print(f"\nFinal Performance Metrics:")
    for key, value in metrics.items():
        if key not in ['ensemble_weights']:
            print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
