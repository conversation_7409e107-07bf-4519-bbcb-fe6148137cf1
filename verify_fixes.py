#!/usr/bin/env python3
"""
Verification script to confirm all fixes are working
"""
import sys
import os

def verify_main_py_structure():
    """Verify main.py has correct structure"""
    print("VERIFYING MAIN.PY STRUCTURE...")
    
    # Add current directory to path
    sys.path.append('.')
    
    try:
        # Test 1: Check if file compiles
        print("1. Testing Python compilation...")
        import py_compile
        py_compile.compile('bybit_bot/main.py', doraise=True)
        print("   ✓ main.py compiles successfully")
        
        # Test 2: Check if main class can be imported
        print("2. Testing main class import...")
        from bybit_bot.main import BybitTradingBotSystem
        print("   ✓ BybitTradingBotSystem imports successfully")
        
        # Test 3: Check if class can be instantiated
        print("3. Testing class instantiation...")
        bot = BybitTradingBotSystem()
        print("   ✓ BybitTradingBotSystem instantiates successfully")
        
        # Test 4: Check critical methods exist
        print("4. Testing critical methods...")
        required_methods = [
            'initialize_all_systems',
            'run',
            '_run_mpc_security_monitor',
            'cleanup'
        ]
        
        for method in required_methods:
            if hasattr(bot, method) and callable(getattr(bot, method)):
                print(f"   ✓ {method} method exists")
            else:
                print(f"   ✗ {method} method missing")
                return False
        
        # Test 5: Check for duplicate function definitions
        print("5. Checking for duplicate functions...")
        with open('bybit_bot/main.py', 'r') as f:
            content = f.read()
        
        # Count sync_main definitions
        sync_main_count = content.count('def sync_main(')
        if sync_main_count == 1:
            print("   ✓ Single sync_main function definition")
        else:
            print(f"   ✗ Found {sync_main_count} sync_main function definitions")
            return False
        
        # Test 6: Check for proper if __name__ == "__main__" block
        print("6. Checking main execution block...")
        if 'if __name__ == "__main__":' in content:
            main_count = content.count('if __name__ == "__main__":')
            if main_count == 1:
                print("   ✓ Single main execution block")
            else:
                print(f"   ✗ Found {main_count} main execution blocks")
                return False
        else:
            print("   ✗ No main execution block found")
            return False
        
        print("\nALL STRUCTURE TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"STRUCTURE TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_database_tables():
    """Verify database tables exist"""
    print("\nVERIFYING DATABASE TABLES...")
    
    try:
        import sqlite3
        from pathlib import Path
        
        db_path = Path("bybit_bot/bybit_trading_bot.db")
        if not db_path.exists():
            print("   ✗ Database file does not exist")
            return False
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check for required tables
        required_tables = [
            'strategy_memories',
            'trading_memories', 
            'recursive_loops',
            'anomaly_remediations'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        for table in required_tables:
            if table in existing_tables:
                print(f"   ✓ {table} table exists")
            else:
                print(f"   ✗ {table} table missing")
                conn.close()
                return False
        
        conn.close()
        print("   ✓ All required database tables exist")
        return True
        
    except Exception as e:
        print(f"DATABASE TEST FAILED: {e}")
        return False

def main():
    print("SYSTEM VERIFICATION - CHECKING ALL FIXES")
    print("=" * 50)
    
    # Test 1: Main.py structure
    structure_ok = verify_main_py_structure()
    
    # Test 2: Database tables
    database_ok = verify_database_tables()
    
    # Final result
    print("\n" + "=" * 50)
    if structure_ok and database_ok:
        print("✓ ALL VERIFICATION TESTS PASSED!")
        print("✓ System should now run without errors")
        return True
    else:
        print("✗ VERIFICATION FAILED!")
        if not structure_ok:
            print("  - Main.py structure issues detected")
        if not database_ok:
            print("  - Database issues detected")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
