#!/usr/bin/env python3
"""
RUN MAIN.PY WITH FULL MONITORING
Comprehensive monitoring of main.py execution with real-time output capture
"""

import subprocess
import sys
import os
import time
import threading
from datetime import datetime, timezone

print("🔍 RUNNING MAIN.PY WITH FULL MONITORING")
print("=" * 80)

class MainPyMonitor:
    def __init__(self):
        self.output_lines = []
        self.error_lines = []
        self.warning_lines = []
        self.info_lines = []
        self.process = None
        self.start_time = time.time()
        self.is_running = False
        
    def log_output(self, line, source="OUT"):
        """Log and categorize output"""
        timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S")
        formatted_line = f"[{timestamp}] {source}: {line}"
        
        self.output_lines.append(formatted_line)
        print(formatted_line)
        
        # Categorize the line
        line_lower = line.lower()
        
        if any(keyword in line_lower for keyword in ['error', 'exception', 'failed', 'critical', 'traceback']):
            self.error_lines.append(line)
            
        elif any(keyword in line_lower for keyword in ['warning', 'warn', 'deprecated']):
            self.warning_lines.append(line)
            
        elif any(keyword in line_lower for keyword in ['info', 'success', 'initialized', 'starting', 'loaded']):
            self.info_lines.append(line)
            
    def monitor_stdout(self):
        """Monitor stdout in real-time"""
        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    self.log_output(line.strip(), "OUT")
                if not self.is_running:
                    break
        except Exception as e:
            self.log_output(f"Stdout monitoring error: {e}", "ERR")
            
    def monitor_stderr(self):
        """Monitor stderr in real-time"""
        try:
            for line in iter(self.process.stderr.readline, ''):
                if line:
                    self.log_output(line.strip(), "ERR")
                if not self.is_running:
                    break
        except Exception as e:
            self.log_output(f"Stderr monitoring error: {e}", "ERR")
            
    def run_main_py(self, timeout_seconds=60):
        """Run main.py with comprehensive monitoring"""
        print(f"🚀 Starting main.py with {timeout_seconds}s timeout...")
        print(f"📁 Working directory: {os.getcwd()}")
        print(f"🐍 Python executable: {sys.executable}")
        print("-" * 80)
        
        try:
            # Change to correct directory
            os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
            
            # Start the process
            cmd = [sys.executable, "bybit_bot/main.py"]
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.is_running = True
            
            # Start monitoring threads
            stdout_thread = threading.Thread(target=self.monitor_stdout, daemon=True)
            stderr_thread = threading.Thread(target=self.monitor_stderr, daemon=True)
            
            stdout_thread.start()
            stderr_thread.start()
            
            # Wait for completion or timeout
            start_time = time.time()
            
            while self.process.poll() is None and (time.time() - start_time) < timeout_seconds:
                time.sleep(0.1)  # Check every 100ms
                
            if self.process.poll() is None:
                # Process is still running after timeout
                print(f"\n⏰ TIMEOUT after {timeout_seconds} seconds")
                print("🛑 Terminating process...")
                
                self.is_running = False
                self.process.terminate()
                
                # Wait for graceful termination
                try:
                    self.process.wait(timeout=5)
                    print("✅ Process terminated gracefully")
                except subprocess.TimeoutExpired:
                    print("🔪 Force killing process...")
                    self.process.kill()
                    
                return_code = -1
            else:
                # Process completed normally
                return_code = self.process.returncode
                self.is_running = False
                print(f"\n✅ Process completed with return code: {return_code}")
                
            # Wait for monitoring threads to finish
            stdout_thread.join(timeout=2)
            stderr_thread.join(timeout=2)
            
            return return_code
            
        except Exception as e:
            print(f"❌ Error running main.py: {e}")
            self.is_running = False
            return -2
            
    def analyze_results(self):
        """Analyze the captured output"""
        print("\n" + "=" * 80)
        print("📊 EXECUTION ANALYSIS")
        print("=" * 80)
        
        runtime = time.time() - self.start_time
        
        print(f"⏱️  Total runtime: {runtime:.2f} seconds")
        print(f"📝 Total output lines: {len(self.output_lines)}")
        print(f"ℹ️  Info lines: {len(self.info_lines)}")
        print(f"⚠️  Warning lines: {len(self.warning_lines)}")
        print(f"❌ Error lines: {len(self.error_lines)}")
        
        # Show key information
        if self.info_lines:
            print(f"\n✅ KEY INFORMATION (first 5):")
            for i, info in enumerate(self.info_lines[:5]):
                print(f"  {i+1}: {info}")
                
        # Show warnings
        if self.warning_lines:
            print(f"\n⚠️  WARNINGS:")
            for i, warning in enumerate(self.warning_lines):
                print(f"  {i+1}: {warning}")
                
        # Show errors
        if self.error_lines:
            print(f"\n❌ ERRORS:")
            for i, error in enumerate(self.error_lines):
                print(f"  {i+1}: {error}")
                
        # Pattern analysis
        print(f"\n🔍 PATTERN ANALYSIS:")
        
        # Check for successful startup
        startup_keywords = ['initialized', 'starting', 'loaded', 'connected', 'ready', 'success']
        startup_lines = []
        
        for line in self.output_lines:
            if any(keyword in line.lower() for keyword in startup_keywords):
                startup_lines.append(line)
                
        if startup_lines:
            print(f"✅ STARTUP INDICATORS FOUND ({len(startup_lines)}):")
            for line in startup_lines[:3]:  # Show first 3
                print(f"  • {line}")
        else:
            print(f"⚠️  NO CLEAR STARTUP INDICATORS FOUND")
            
        # Check for trading activity
        trading_keywords = ['trade', 'order', 'buy', 'sell', 'profit', 'balance', 'position']
        trading_lines = []
        
        for line in self.output_lines:
            if any(keyword in line.lower() for keyword in trading_keywords):
                trading_lines.append(line)
                
        if trading_lines:
            print(f"💰 TRADING ACTIVITY FOUND ({len(trading_lines)}):")
            for line in trading_lines[:2]:  # Show first 2
                print(f"  • {line}")
        else:
            print(f"ℹ️  NO TRADING ACTIVITY DETECTED (normal for short runs)")
            
    def generate_health_report(self):
        """Generate final health assessment"""
        print("\n" + "=" * 80)
        print("🏥 MAIN.PY HEALTH REPORT")
        print("=" * 80)
        
        # Count critical issues
        critical_errors = len([e for e in self.error_lines if any(keyword in e.lower() for keyword in ['critical', 'fatal', 'exception', 'traceback'])])
        import_errors = len([e for e in self.error_lines if any(keyword in e.lower() for keyword in ['import', 'module', 'no module'])])
        
        # Determine health status
        if critical_errors == 0 and import_errors == 0 and len(self.error_lines) == 0:
            health_status = "EXCELLENT"
            health_emoji = "🟢"
        elif critical_errors == 0 and import_errors == 0 and len(self.error_lines) < 3:
            health_status = "GOOD"
            health_emoji = "🟡"
        elif critical_errors == 0 and import_errors == 0:
            health_status = "FAIR"
            health_emoji = "🟠"
        else:
            health_status = "NEEDS ATTENTION"
            health_emoji = "🔴"
            
        print(f"{health_emoji} OVERALL HEALTH: {health_status}")
        print(f"❌ Critical errors: {critical_errors}")
        print(f"📦 Import errors: {import_errors}")
        print(f"⚠️  Total warnings: {len(self.warning_lines)}")
        print(f"ℹ️  Info messages: {len(self.info_lines)}")
        
        # Recommendations
        print(f"\n📋 RECOMMENDATIONS:")
        
        if health_status == "EXCELLENT":
            print("✅ Main.py is running perfectly")
            print("✅ No issues detected")
            print("✅ System ready for production use")
        elif health_status == "GOOD":
            print("✅ Main.py is running well with minor issues")
            print("• Monitor warnings for potential improvements")
        elif health_status == "FAIR":
            print("⚠️  Main.py has some issues but is functional")
            print("• Review error messages for optimization opportunities")
        else:
            print("🚨 Main.py has significant issues")
            print("• Address critical errors before production use")
            print("• Check import dependencies and configuration")
            
        return health_status in ["EXCELLENT", "GOOD"]

def main():
    """Main execution function"""
    monitor = MainPyMonitor()
    
    # Run main.py with monitoring
    return_code = monitor.run_main_py(timeout_seconds=45)
    
    # Analyze results
    monitor.analyze_results()
    success = monitor.generate_health_report()
    
    print(f"\n🎯 FINAL RESULT: {'SUCCESS' if success else 'NEEDS ATTENTION'}")
    print(f"🔢 Return code: {return_code}")
    
    return success

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ MAIN.PY MONITORING SUCCESSFUL")
        sys.exit(0)
    else:
        print("\n⚠️  MAIN.PY MONITORING COMPLETED WITH ISSUES")
        sys.exit(1)
