#!/usr/bin/env python3
"""
Real Profit/Loss Tracker - NO FAKE DATA
Tracks actual profit and losses from Bybit positions and updates database
"""

import asyncio
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class RealPnLTracker:
    """Real-time profit/loss tracker using actual Bybit data - NO FAKE DATA"""
    
    def __init__(self, bybit_client, db_path: str = "bybit_trading_bot.db"):
        self.bybit_client = bybit_client
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.tracking_active = False
        
    async def start_tracking(self):
        """Start real-time P&L tracking"""
        self.tracking_active = True
        self.logger.info("REAL P&L TRACKER: Started tracking actual profit/losses - NO FAKE DATA")
        
        while self.tracking_active:
            try:
                # Update P&L for all open positions
                await self._update_position_pnl()
                
                # Update P&L for recently closed positions
                await self._update_closed_position_pnl()
                
                # Wait before next update
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in P&L tracking: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def stop_tracking(self):
        """Stop P&L tracking"""
        self.tracking_active = False
        self.logger.info("REAL P&L TRACKER: Stopped tracking")
    
    async def _update_position_pnl(self):
        """Update P&L for open positions using REAL Bybit data"""
        try:
            # Get REAL positions from Bybit
            positions = await self.bybit_client.get_positions()
            
            if not positions:
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for position in positions:
                try:
                    symbol = position.get('symbol')
                    side = position.get('side')
                    size = float(position.get('size', 0))
                    entry_price = float(position.get('avgPrice', 0))
                    mark_price = float(position.get('markPrice', 0))
                    unrealized_pnl = float(position.get('unrealisedPnl', 0))
                    
                    if size == 0:  # No position
                        continue
                    
                    # Find corresponding trade in database
                    cursor.execute('''
                        SELECT id, order_id, trade_id FROM trades 
                        WHERE symbol = ? AND side = ? AND profit_loss = 0.0
                        ORDER BY timestamp DESC LIMIT 1
                    ''', (symbol, side))
                    
                    trade_record = cursor.fetchone()
                    if trade_record:
                        trade_id = trade_record[0]
                        
                        # Update with REAL unrealized P&L from Bybit
                        cursor.execute('''
                            UPDATE trades 
                            SET profit_loss = ?, market_conditions = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        ''', (unrealized_pnl, f"Open position: Entry ${entry_price:.2f}, Mark ${mark_price:.2f}", trade_id))
                        
                        self.logger.info(f"REAL P&L UPDATE: {symbol} {side} = ${unrealized_pnl:.4f} (unrealized)")
                
                except Exception as e:
                    self.logger.error(f"Error updating position P&L for {position}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error updating position P&L: {e}")
    
    async def _update_closed_position_pnl(self):
        """Update P&L for recently closed positions using REAL Bybit data"""
        try:
            # Get REAL execution history from Bybit
            executions = await self.bybit_client.get_order_history(limit=50)
            
            if not executions:
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for execution in executions:
                try:
                    symbol = execution.get('symbol')
                    side = execution.get('side')
                    exec_price = float(execution.get('execPrice', 0))
                    exec_qty = float(execution.get('execQty', 0))
                    exec_fee = float(execution.get('execFee', 0))
                    exec_time = execution.get('execTime')
                    order_id = execution.get('orderId')
                    
                    # Find corresponding trade
                    cursor.execute('''
                        SELECT id, price, quantity FROM trades 
                        WHERE (order_id = ? OR trade_id = ?) AND symbol = ?
                        ORDER BY timestamp DESC LIMIT 1
                    ''', (order_id, order_id, symbol))
                    
                    trade_record = cursor.fetchone()
                    if trade_record:
                        trade_id, entry_price, quantity = trade_record
                        
                        # Calculate REAL P&L
                        if side.lower() in ["buy", "long"]:
                            # Long position: profit when exit price > entry price
                            price_diff = exec_price - entry_price
                        else:
                            # Short position: profit when exit price < entry price
                            price_diff = entry_price - exec_price
                        
                        # Calculate net P&L (positive for profit, negative for loss)
                        gross_pnl = price_diff * exec_qty
                        net_pnl = gross_pnl - exec_fee
                        
                        # Update with REAL realized P&L
                        cursor.execute('''
                            UPDATE trades 
                            SET profit_loss = ?, commission = ?, 
                                market_conditions = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        ''', (net_pnl, exec_fee, f"Closed: Entry ${entry_price:.2f}, Exit ${exec_price:.2f}", trade_id))
                        
                        # Log REAL profit/loss
                        pnl_status = "PROFIT" if net_pnl > 0 else "LOSS"
                        self.logger.info(f"REAL {pnl_status}: {symbol} {side} = ${net_pnl:.4f} (realized)")
                        
                        # Update learning system with REAL P&L
                        await self._notify_learning_system(symbol, side, net_pnl, entry_price, exec_price)
                
                except Exception as e:
                    self.logger.error(f"Error processing execution {execution}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error updating closed position P&L: {e}")
    
    async def _notify_learning_system(self, symbol: str, side: str, pnl: float, entry_price: float, exit_price: float):
        """Notify learning system of REAL profit/loss - NO FAKE DATA"""
        try:
            # Store learning data for strategy improvement
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create learning memory from REAL trade outcome
            learning_data = {
                "symbol": symbol,
                "side": side,
                "entry_price": entry_price,
                "exit_price": exit_price,
                "profit_loss": pnl,
                "success": pnl > 0,
                "timestamp": datetime.now().isoformat()
            }
            
            # Store in trading memories for learning
            cursor.execute('''
                INSERT INTO trading_memories (
                    symbol, pattern_data, outcome, success, profit_loss, 
                    strategy, confidence, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol,
                str(learning_data),
                "PROFIT" if pnl > 0 else "LOSS",
                pnl > 0,
                pnl,
                "real_trading",
                0.9,  # High confidence since this is real data
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"LEARNING UPDATE: {symbol} outcome stored for strategy improvement")
            
        except Exception as e:
            self.logger.error(f"Error notifying learning system: {e}")
    
    async def get_daily_pnl(self) -> float:
        """Get total daily P&L from REAL trades - NO FAKE DATA"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT SUM(profit_loss) FROM trades 
                WHERE date(timestamp) = date('now')
                AND profit_loss IS NOT NULL
            ''')
            
            daily_pnl = cursor.fetchone()[0] or 0.0
            conn.close()
            
            return daily_pnl
            
        except Exception as e:
            self.logger.error(f"Error getting daily P&L: {e}")
            return 0.0
    
    async def get_total_pnl(self) -> float:
        """Get total P&L from REAL trades - NO FAKE DATA"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT SUM(profit_loss) FROM trades 
                WHERE profit_loss IS NOT NULL
            ''')
            
            total_pnl = cursor.fetchone()[0] or 0.0
            conn.close()
            
            return total_pnl
            
        except Exception as e:
            self.logger.error(f"Error getting total P&L: {e}")
            return 0.0
