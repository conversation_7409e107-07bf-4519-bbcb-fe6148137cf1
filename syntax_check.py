#!/usr/bin/env python3
"""
Syntax check for main.py - NO UNICODE
"""

import ast
import os
import sys

print("SYNTAX CHECK FOR MAIN.PY")
print("=" * 30)

# Change to correct directory
os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
print(f"Directory: {os.getcwd()}")

# Check if main.py exists
main_file = "bybit_bot/main.py"
if not os.path.exists(main_file):
    print(f"ERROR: {main_file} not found")
    sys.exit(1)

print(f"File exists: {main_file}")

# Read the file
try:
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    print(f"File size: {len(content)} characters")
    print(f"Lines: {len(content.splitlines())}")
except Exception as e:
    print(f"ERROR reading file: {e}")
    sys.exit(1)

# Check syntax using AST
print("\nChecking syntax with AST...")
try:
    tree = ast.parse(content, filename=main_file)
    print("SUCCESS: Syntax is valid")
    
    # Count some basic elements
    imports = 0
    functions = 0
    classes = 0
    
    for node in ast.walk(tree):
        if isinstance(node, (ast.Import, ast.ImportFrom)):
            imports += 1
        elif isinstance(node, ast.FunctionDef):
            functions += 1
        elif isinstance(node, ast.ClassDef):
            classes += 1
    
    print(f"  Imports: {imports}")
    print(f"  Functions: {functions}")
    print(f"  Classes: {classes}")
    
except SyntaxError as e:
    print(f"SYNTAX ERROR: {e}")
    print(f"  Line {e.lineno}: {e.text}")
    print(f"  Error: {e.msg}")
    sys.exit(1)
except Exception as e:
    print(f"ERROR parsing: {e}")
    sys.exit(1)

# Try to compile
print("\nTrying to compile...")
try:
    compile(content, main_file, 'exec')
    print("SUCCESS: Compilation successful")
except SyntaxError as e:
    print(f"COMPILATION ERROR: {e}")
    print(f"  Line {e.lineno}: {e.text}")
    print(f"  Error: {e.msg}")
    sys.exit(1)
except Exception as e:
    print(f"ERROR compiling: {e}")
    sys.exit(1)

print("\nSYNTAX CHECK PASSED")
print("main.py has valid Python syntax")

# Now try to actually run the syntax check
if __name__ == "__main__":
    # Execute the check
    pass
