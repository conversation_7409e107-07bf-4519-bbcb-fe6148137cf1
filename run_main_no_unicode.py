#!/usr/bin/env python3
"""
Run main.py with comprehensive monitoring - NO UNICODE
Plain ASCII only to avoid encoding issues
"""

import os
import sys
import subprocess
import time
import threading
from datetime import datetime
from pathlib import Path

def run_main_comprehensive():
    """Run main.py with comprehensive monitoring"""
    print("RUNNING MAIN.PY - COMPREHENSIVE MONITORING")
    print("=" * 60)
    
    # Setup environment
    target_dir = r'E:\The_real_deal_copy\Bybit_Bot\BOT'
    
    if not os.path.exists(target_dir):
        print(f"ERROR: Directory not found: {target_dir}")
        return False
    
    os.chdir(target_dir)
    print(f"Working directory: {os.getcwd()}")
    
    # Verify main.py exists
    main_py = Path("bybit_bot/main.py")
    if not main_py.exists():
        print(f"ERROR: main.py not found: {main_py}")
        return False
    
    print(f"Found main.py: {main_py}")
    
    # Step 1: Clean up backups first
    print("\nSTEP 1: CLEANING UP BACKUP FILES")
    print("-" * 40)
    
    try:
        cleanup_result = subprocess.run([
            sys.executable, 'massive_backup_cleanup.py'
        ], capture_output=True, text=True, timeout=60)
        
        if cleanup_result.returncode == 0:
            print("SUCCESS: Backup cleanup completed")
            if cleanup_result.stdout:
                print("Cleanup output:")
                for line in cleanup_result.stdout.split('\n')[:10]:
                    if line.strip():
                        print(f"  {line}")
        else:
            print("WARNING: Backup cleanup had issues")
            if cleanup_result.stderr:
                print(f"Cleanup errors: {cleanup_result.stderr}")
    except Exception as e:
        print(f"WARNING: Could not run backup cleanup: {e}")
    
    # Step 2: Test imports
    print("\nSTEP 2: TESTING IMPORTS")
    print("-" * 40)
    
    import_test_script = '''
import sys
import os
sys.path.insert(0, '.')

print("Starting import test...")

try:
    print("Testing basic imports...")
    import asyncio
    import logging
    from pathlib import Path
    print("SUCCESS: Basic imports OK")
    
    print("Testing websockets compatibility...")
    try:
        from websockets_compatibility import create_comprehensive_websockets_compatibility
        print("SUCCESS: websockets_compatibility found")
    except ImportError:
        print("WARNING: websockets_compatibility not found, using fallback")
    
    print("Testing bybit_bot package...")
    import bybit_bot
    print("SUCCESS: bybit_bot package OK")
    
    print("Testing core modules...")
    try:
        from bybit_bot.core.optimization_manager import OptimizationComponentsManager
        print("SUCCESS: optimization_manager OK")
    except ImportError as e:
        print(f"WARNING: optimization_manager import issue: {e}")
    
    try:
        from bybit_bot.exchange.bybit_client import BybitClient
        print("SUCCESS: bybit_client OK")
    except ImportError as e:
        print(f"WARNING: bybit_client import issue: {e}")
    
    print("IMPORT_TEST_COMPLETE")
    
except Exception as e:
    print(f"ERROR: Import test failed: {e}")
    import traceback
    traceback.print_exc()
'''
    
    try:
        import_result = subprocess.run([
            sys.executable, '-c', import_test_script
        ], capture_output=True, text=True, timeout=120, 
          env={**os.environ, 'PYTHONIOENCODING': 'utf-8'})
        
        print("Import test results:")
        if import_result.stdout:
            for line in import_result.stdout.split('\n'):
                if line.strip():
                    print(f"  {line}")
        
        if import_result.stderr:
            print("Import test errors:")
            for line in import_result.stderr.split('\n'):
                if line.strip():
                    print(f"  ERROR: {line}")
        
        import_success = "IMPORT_TEST_COMPLETE" in import_result.stdout
        print(f"Import test result: {'SUCCESS' if import_success else 'FAILED'}")
        
    except Exception as e:
        print(f"ERROR: Import test failed: {e}")
        import_success = False
    
    # Step 3: Run main.py
    print("\nSTEP 3: RUNNING MAIN.PY")
    print("-" * 40)
    
    try:
        print("Starting main.py execution...")
        
        # Set environment for proper encoding
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
        
        process = subprocess.Popen([
            sys.executable, 'bybit_bot/main.py', '--test'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
           text=True, bufsize=1, universal_newlines=True, env=env)
        
        # Monitor execution for 60 seconds
        start_time = time.time()
        output_lines = []
        error_lines = []
        warnings = []
        errors = []
        info_messages = []
        
        print("Monitoring execution (60 second timeout)...")
        
        while True:
            # Check if process finished
            if process.poll() is not None:
                print("Process completed")
                break
            
            # Check timeout
            elapsed = time.time() - start_time
            if elapsed > 60:
                print("Timeout reached - terminating process")
                process.terminate()
                break
            
            # Read output
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    output_lines.append(line)
                    print(f"OUT: {line}")
                    
                    # Categorize output
                    line_lower = line.lower()
                    if any(keyword in line_lower for keyword in ['error', 'failed', 'exception', 'traceback']):
                        errors.append(line)
                    elif any(keyword in line_lower for keyword in ['warning', 'warn']):
                        warnings.append(line)
                    else:
                        info_messages.append(line)
            except:
                pass
            
            # Read errors
            try:
                line = process.stderr.readline()
                if line:
                    line = line.strip()
                    error_lines.append(line)
                    print(f"ERR: {line}")
                    errors.append(line)
            except:
                pass
            
            time.sleep(0.1)
        
        # Get remaining output
        try:
            remaining_out, remaining_err = process.communicate(timeout=5)
            if remaining_out:
                output_lines.extend([line.strip() for line in remaining_out.split('\n') if line.strip()])
            if remaining_err:
                error_lines.extend([line.strip() for line in remaining_err.split('\n') if line.strip()])
        except:
            pass
        
        # Analysis
        print(f"\nEXECUTION ANALYSIS:")
        print(f"  Total output lines: {len(output_lines)}")
        print(f"  Total error lines: {len(error_lines)}")
        print(f"  Warnings detected: {len(warnings)}")
        print(f"  Errors detected: {len(errors)}")
        print(f"  Info messages: {len(info_messages)}")
        print(f"  Exit code: {process.returncode}")
        print(f"  Execution time: {elapsed:.1f} seconds")
        
        # Show critical errors
        if errors:
            print(f"\nCRITICAL ERRORS ({len(errors)}):")
            for error in errors[:5]:
                print(f"  - {error}")
            if len(errors) > 5:
                print(f"  ... and {len(errors) - 5} more errors")
        
        # Show warnings
        if warnings:
            print(f"\nWARNINGS ({len(warnings)}):")
            for warning in warnings[:3]:
                print(f"  - {warning}")
            if len(warnings) > 3:
                print(f"  ... and {len(warnings) - 3} more warnings")
        
        # Determine success
        execution_success = len(errors) == 0 or process.returncode == 0
        
        print(f"\nEXECUTION RESULT: {'SUCCESS' if execution_success else 'FAILED'}")
        
        return execution_success
        
    except Exception as e:
        print(f"ERROR: Main execution failed: {e}")
        return False

def main():
    """Main function"""
    print("COMPREHENSIVE MAIN.PY EXECUTION TEST")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    
    success = run_main_comprehensive()
    
    print(f"\nCompleted at: {datetime.now()}")
    print(f"FINAL RESULT: {'SUCCESS - MAIN.PY RUNS WITHOUT CRITICAL ERRORS' if success else 'FAILED - MAIN.PY HAS CRITICAL ISSUES'}")
    
    return success

if __name__ == "__main__":
    result = main()
    if result:
        print("\nSUCCESS: Main.py execution completed successfully")
    else:
        print("\nFAILED: Main.py execution has issues that need to be resolved")
