#!/usr/bin/env python3
"""
Comprehensive Error Detection - Find ALL errors in main.py
"""

import sys
import traceback
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_individual_import(module_path, class_name=None):
    """Test a single import and return detailed error info"""
    try:
        if class_name:
            exec(f"from {module_path} import {class_name}")
        else:
            exec(f"import {module_path}")
        return True, None
    except Exception as e:
        return False, str(e)

def analyze_main_imports():
    """Analyze all imports from main.py"""
    print("COMPREHENSIVE ERROR DETECTION")
    print("="*60)
    
    # All imports from main.py initialize_all_systems method
    imports_to_test = [
        # Core imports
        ("bybit_bot.core.config", "EnhancedBotConfig"),
        ("bybit_bot.database.connection", "DatabaseManager"),
        ("bybit_bot.exchange.enhanced_bybit_client", "EnhancedBybitClient"),
        
        # AI imports
        ("bybit_bot.ai.ai_folder_activation_manager", "activate_ai_folder"),
        ("bybit_bot.ai.ai_folder_activation_manager", "initialize_ai_activation_manager"),
        ("bybit_bot.core.enhanced_time_manager", "EnhancedTimeManager"),
        ("bybit_bot.agents.agent_orchestrator", "AgentOrchestrator"),
        
        # Performance imports
        ("bybit_bot.performance.scaling_engine", "PerformanceScalingSystem"),
        ("bybit_bot.ai.intelligent_ml_system", "IntelligentMLSystem"),
        
        # MPC imports
        ("bybit_bot.mcp.mpc_engine", "MPCCryptographicEngine"),
        ("bybit_bot.mcp.threshold_signatures", "ThresholdSignatureManager"),
        ("bybit_bot.mcp.bybit_mpc_wallet", "BybitMPCWallet"),
        ("bybit_bot.mcp.mpc_security_layer", "MPCSecurityLayer"),
        
        # Enhanced learning imports
        ("bybit_bot.ai.enhanced_multi_timeframe_learner", "EnhancedMultiTimeframeLearner"),
        ("bybit_bot.ai.tier1_integration_manager", "Tier1IntegrationManager"),
        ("bybit_bot.ai.advanced_data_integration_engine", "AdvancedDataIntegrationEngine"),
        ("bybit_bot.ai.adaptive_learning_engine", "AdaptiveLearningEngine"),
        
        # Optimization imports
        ("bybit_bot.core.optimization_manager", "OptimizationComponentsManager"),
        ("bybit_bot.core.optimization_manager", "OptimizationConfig"),
        
        # Additional components
        ("bybit_bot.core.performance_scaling_system", "PerformanceScalingSystem"),
        ("bybit_bot.core.cpu_efficiency_framework", "CPUEfficiencyFramework"),
        ("bybit_bot.agents.trading_agent", "TradingAgent"),
        ("bybit_bot.agents.research_agent", "ResearchAgent"),
        ("bybit_bot.agents.risk_agent", "RiskAgent"),
        ("bybit_bot.agents.learning_agent", "LearningAgent"),
    ]
    
    failed_imports = []
    successful_imports = []
    
    print("Testing all imports from main.py...")
    print("-" * 60)
    
    for module_path, class_name in imports_to_test:
        success, error = test_individual_import(module_path, class_name)
        
        if success:
            successful_imports.append((module_path, class_name))
            print(f"SUCCESS: {module_path}.{class_name}")
        else:
            failed_imports.append((module_path, class_name, error))
            print(f"ERROR: {module_path}.{class_name} - {error}")
    
    print("\n" + "="*60)
    print("IMPORT ANALYSIS RESULTS")
    print("="*60)
    
    print(f"SUCCESSFUL IMPORTS: {len(successful_imports)}")
    print(f"FAILED IMPORTS: {len(failed_imports)}")
    
    if failed_imports:
        print("\nFAILED IMPORTS DETAILS:")
        print("-" * 40)
        for i, (module_path, class_name, error) in enumerate(failed_imports, 1):
            print(f"{i}. {module_path}.{class_name}")
            print(f"   ERROR: {error}")
            print()
    
    return failed_imports

async def test_main_system_creation():
    """Test creating the main system"""
    print("\nTESTING MAIN SYSTEM CREATION")
    print("-" * 40)
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        print("SUCCESS: BybitTradingBotSystem import")
        
        system = BybitTradingBotSystem()
        print("SUCCESS: System instance created")
        
        # Test component initialization
        try:
            await system.initialize_components()
            print("SUCCESS: Component initialization completed")
        except Exception as e:
            print(f"ERROR: Component initialization failed: {e}")
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"ERROR: Main system creation failed: {e}")
        traceback.print_exc()
        return False

def check_file_existence():
    """Check if critical files exist"""
    print("\nCHECKING FILE EXISTENCE")
    print("-" * 40)
    
    critical_files = [
        "bybit_bot/core/config.py",
        "bybit_bot/database/connection.py",
        "bybit_bot/exchange/enhanced_bybit_client.py",
        "bybit_bot/ai/ai_folder_activation_manager.py",
        "bybit_bot/core/enhanced_time_manager.py",
        "bybit_bot/agents/agent_orchestrator.py",
        "bybit_bot/performance/scaling_engine.py",
        "bybit_bot/ai/intelligent_ml_system.py",
        "bybit_bot/mcp/mpc_engine.py",
        "bybit_bot/mcp/threshold_signatures.py",
        "bybit_bot/mcp/bybit_mpc_wallet.py",
        "bybit_bot/mcp/mpc_security_layer.py",
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in critical_files:
        full_path = Path(file_path)
        if full_path.exists():
            existing_files.append(file_path)
            print(f"EXISTS: {file_path}")
        else:
            missing_files.append(file_path)
            print(f"MISSING: {file_path}")
    
    print(f"\nFILE EXISTENCE SUMMARY:")
    print(f"EXISTING FILES: {len(existing_files)}")
    print(f"MISSING FILES: {len(missing_files)}")
    
    return missing_files

async def main():
    """Main error detection function"""
    print("STARTING COMPREHENSIVE ERROR DETECTION")
    print("="*80)
    
    # 1. Check file existence
    missing_files = check_file_existence()
    
    # 2. Test imports
    failed_imports = analyze_main_imports()
    
    # 3. Test main system creation
    system_creation_success = await test_main_system_creation()
    
    # 4. Summary
    print("\n" + "="*80)
    print("COMPREHENSIVE ERROR DETECTION SUMMARY")
    print("="*80)
    
    total_errors = len(missing_files) + len(failed_imports)
    
    print(f"MISSING FILES: {len(missing_files)}")
    print(f"FAILED IMPORTS: {len(failed_imports)}")
    print(f"SYSTEM CREATION: {'SUCCESS' if system_creation_success else 'FAILED'}")
    print(f"TOTAL ERRORS FOUND: {total_errors}")
    
    if total_errors == 0 and system_creation_success:
        print("\nSUCCESS: NO ERRORS FOUND - SYSTEM IS READY")
    else:
        print("\nERRORS DETECTED - NEED TO FIX:")
        if missing_files:
            print("- Missing files need to be created")
        if failed_imports:
            print("- Import errors need to be resolved")
        if not system_creation_success:
            print("- System creation issues need to be fixed")
    
    return total_errors == 0 and system_creation_success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        traceback.print_exc()
        sys.exit(1)
