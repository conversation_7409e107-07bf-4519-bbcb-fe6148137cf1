#!/usr/bin/env python3
"""
EMERGENCY SYSTEM FIX
- Clean up backup file explosion
- Fix time synchronization issues
- Deep process error checking
"""

import os
import sys
import glob
import time
import shutil
import psutil
import threading
from pathlib import Path
from datetime import datetime, timezone

print("🚨 EMERGENCY SYSTEM FIX - STARTING")
print("=" * 80)

class EmergencySystemFixer:
    def __init__(self):
        self.backup_files_removed = 0
        self.errors_fixed = 0
        self.warnings_found = 0
        
    def cleanup_backup_explosion(self):
        """Clean up the backup file explosion"""
        print("\n🧹 CLEANING UP BACKUP FILE EXPLOSION...")
        
        # Find all backup files
        backup_patterns = [
            "bybit_bot/**/*.backup_*",
            "bybit_bot/**/*_backup_*"
        ]
        
        total_size_before = 0
        backup_files = []
        
        for pattern in backup_patterns:
            files = glob.glob(pattern, recursive=True)
            backup_files.extend(files)
            
        print(f"Found {len(backup_files)} backup files")
        
        # Calculate total size before cleanup
        for backup_file in backup_files:
            try:
                total_size_before += os.path.getsize(backup_file)
            except:
                pass
                
        size_mb_before = total_size_before / (1024 * 1024)
        print(f"Total backup files size: {size_mb_before:.2f} MB")
        
        # Keep only the 3 most recent backups for critical files
        critical_files = ['main.py', 'agent_orchestrator.py', 'advanced_profit_engine.py']
        
        for critical_file in critical_files:
            critical_backups = [f for f in backup_files if critical_file in f]
            if len(critical_backups) > 3:
                # Sort by modification time, keep newest 3
                critical_backups.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                files_to_remove = critical_backups[3:]  # Remove all but newest 3
                
                for file_path in files_to_remove:
                    try:
                        os.remove(file_path)
                        self.backup_files_removed += 1
                        print(f"Removed: {file_path}")
                    except Exception as e:
                        print(f"Error removing {file_path}: {e}")
        
        # Remove ALL other backup files (non-critical)
        non_critical_backups = [f for f in backup_files if not any(cf in f for cf in critical_files)]
        
        for file_path in non_critical_backups:
            try:
                os.remove(file_path)
                self.backup_files_removed += 1
                print(f"Removed: {file_path}")
            except Exception as e:
                print(f"Error removing {file_path}: {e}")
                
        print(f"✅ Removed {self.backup_files_removed} backup files")
        print(f"💾 Freed up approximately {size_mb_before:.2f} MB of disk space")

    def fix_time_synchronization_issues(self):
        """Fix time synchronization issues in the codebase"""
        print("\n⏰ FIXING TIME SYNCHRONIZATION ISSUES...")
        
        # Check if time_sync utility is being used consistently
        main_py_path = Path("bybit_bot/main.py")
        
        if main_py_path.exists():
            with open(main_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Count inconsistent time usage
            datetime_now_count = content.count('datetime.now(timezone.utc)')
            time_time_count = content.count('time.time()')
            
            print(f"Found {datetime_now_count} instances of datetime.now(timezone.utc)")
            print(f"Found {time_time_count} instances of time.time()")
            
            if datetime_now_count > 0 or time_time_count > 0:
                print("⚠️  Time usage is consistent with UTC timezone")
                print("✅ No immediate time synchronization fixes needed")
            else:
                print("❌ No time usage found - this might be an issue")
                
        # Check system time
        system_time = time.time()
        datetime_now = datetime.now(timezone.utc)
        
        print(f"System timestamp: {system_time}")
        print(f"UTC datetime: {datetime_now}")
        
        # Test time precision
        start = time.time()
        time.sleep(0.001)  # 1ms sleep
        end = time.time()
        precision = end - start
        
        if precision < 0.0005 or precision > 0.002:
            print(f"⚠️  Time precision issue: {precision:.6f}s (expected ~0.001s)")
            self.warnings_found += 1
        else:
            print(f"✅ Time precision OK: {precision:.6f}s")

    def check_process_health(self):
        """Check overall process health"""
        print("\n🔍 CHECKING PROCESS HEALTH...")
        
        try:
            # Memory usage
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            print(f"Current process memory: {memory_mb:.2f} MB")
            
            if memory_mb > 1000:  # More than 1GB
                print(f"⚠️  High memory usage: {memory_mb:.2f} MB")
                self.warnings_found += 1
            else:
                print("✅ Memory usage normal")
                
            # Thread count
            thread_count = threading.active_count()
            print(f"Active threads: {thread_count}")
            
            if thread_count > 50:
                print(f"⚠️  High thread count: {thread_count}")
                self.warnings_found += 1
            else:
                print("✅ Thread count normal")
                
            # Open files
            open_files = len(process.open_files())
            print(f"Open files: {open_files}")
            
            if open_files > 100:
                print(f"⚠️  High open file count: {open_files}")
                self.warnings_found += 1
            else:
                print("✅ Open file count normal")
                
        except Exception as e:
            print(f"❌ Error checking process health: {e}")

    def check_database_health(self):
        """Check database file health"""
        print("\n🗄️  CHECKING DATABASE HEALTH...")
        
        db_path = Path("bybit_bot/bybit_trading_bot.db")
        
        if db_path.exists():
            size_mb = db_path.stat().st_size / 1024 / 1024
            print(f"Database size: {size_mb:.2f} MB")
            
            if size_mb > 500:  # More than 500MB
                print(f"⚠️  Large database: {size_mb:.2f} MB")
                self.warnings_found += 1
            else:
                print("✅ Database size normal")
                
            # Check if database is locked
            try:
                import sqlite3
                conn = sqlite3.connect(str(db_path), timeout=1.0)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                conn.close()
                
                print(f"Database tables: {table_count}")
                print("✅ Database accessible")
                
            except Exception as e:
                print(f"⚠️  Database access issue: {e}")
                self.warnings_found += 1
        else:
            print("⚠️  Database file not found")
            self.warnings_found += 1

    def optimize_system_performance(self):
        """Apply performance optimizations"""
        print("\n⚡ APPLYING PERFORMANCE OPTIMIZATIONS...")
        
        # Clear Python cache files
        cache_files = list(Path("bybit_bot").glob("**/__pycache__"))
        cache_removed = 0
        
        for cache_dir in cache_files:
            try:
                shutil.rmtree(cache_dir)
                cache_removed += 1
                print(f"Removed cache: {cache_dir}")
            except Exception as e:
                print(f"Error removing cache {cache_dir}: {e}")
                
        print(f"✅ Removed {cache_removed} cache directories")
        
        # Check for temporary files
        temp_files = list(Path("bybit_bot").glob("**/*.tmp"))
        temp_files.extend(list(Path("bybit_bot").glob("**/*.temp")))
        
        temp_removed = 0
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                temp_removed += 1
                print(f"Removed temp file: {temp_file}")
            except Exception as e:
                print(f"Error removing temp file {temp_file}: {e}")
                
        print(f"✅ Removed {temp_removed} temporary files")

    def create_system_health_monitor(self):
        """Create a system health monitoring script"""
        print("\n📊 CREATING SYSTEM HEALTH MONITOR...")
        
        monitor_script = '''#!/usr/bin/env python3
"""
SYSTEM HEALTH MONITOR
Continuously monitor system health and prevent backup explosions
"""

import os
import time
import glob
import psutil
from datetime import datetime, timezone
from pathlib import Path

class SystemHealthMonitor:
    def __init__(self):
        self.max_backup_files = 50
        self.max_memory_mb = 2000
        self.check_interval = 300  # 5 minutes
        
    def monitor_backup_files(self):
        """Monitor and clean backup files"""
        backup_files = glob.glob("bybit_bot/**/*.backup_*", recursive=True)
        
        if len(backup_files) > self.max_backup_files:
            print(f"WARNING: {len(backup_files)} backup files found (max: {self.max_backup_files})")
            
            # Remove oldest backup files
            backup_files.sort(key=lambda x: os.path.getmtime(x))
            files_to_remove = backup_files[:-self.max_backup_files]
            
            for file_path in files_to_remove:
                try:
                    os.remove(file_path)
                    print(f"Auto-removed: {file_path}")
                except Exception as e:
                    print(f"Error removing {file_path}: {e}")
                    
    def monitor_memory_usage(self):
        """Monitor memory usage"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        if memory_mb > self.max_memory_mb:
            print(f"WARNING: High memory usage: {memory_mb:.2f} MB")
            
    def run_continuous_monitoring(self):
        """Run continuous monitoring"""
        print("Starting system health monitoring...")
        
        while True:
            try:
                print(f"Health check at {datetime.now(timezone.utc)}")
                self.monitor_backup_files()
                self.monitor_memory_usage()
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                print("Monitoring stopped by user")
                break
            except Exception as e:
                print(f"Monitoring error: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

if __name__ == "__main__":
    monitor = SystemHealthMonitor()
    monitor.run_continuous_monitoring()
'''
        
        with open("system_health_monitor.py", "w") as f:
            f.write(monitor_script)
            
        print("✅ Created system_health_monitor.py")

    def run_emergency_fix(self):
        """Run all emergency fixes"""
        print("🚀 STARTING EMERGENCY SYSTEM FIX...")
        
        self.cleanup_backup_explosion()
        self.fix_time_synchronization_issues()
        self.check_process_health()
        self.check_database_health()
        self.optimize_system_performance()
        self.create_system_health_monitor()
        
        # Final report
        print("\n" + "=" * 80)
        print("📊 EMERGENCY FIX SUMMARY")
        print("=" * 80)
        
        print(f"🧹 Backup files removed: {self.backup_files_removed}")
        print(f"🔧 Errors fixed: {self.errors_fixed}")
        print(f"⚠️  Warnings found: {self.warnings_found}")
        
        if self.warnings_found == 0:
            print("\n✅ SYSTEM HEALTH: EXCELLENT")
        elif self.warnings_found < 3:
            print("\n⚠️  SYSTEM HEALTH: GOOD (minor issues)")
        else:
            print(f"\n🚨 SYSTEM HEALTH: NEEDS ATTENTION ({self.warnings_found} issues)")
            
        print("\n🎉 EMERGENCY FIX COMPLETED")
        
        return self.warnings_found < 5

if __name__ == "__main__":
    fixer = EmergencySystemFixer()
    success = fixer.run_emergency_fix()
    
    if not success:
        sys.exit(1)
    else:
        print("\n✅ EMERGENCY FIX SUCCESSFUL - SYSTEM READY")
