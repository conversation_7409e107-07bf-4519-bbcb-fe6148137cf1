"""
MPC Security Layer for Bybit Trading Bot
Implements comprehensive security protocols for Multi-Party Computation.
Provides attack prevention, secure communication, and audit logging.
"""

import asyncio
import hashlib
import hmac
import json
import logging
import secrets
import time
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple, Any, Union, Set
from datetime import datetime, timezone
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import ipaddress

logger = logging.getLogger(__name__)

@dataclass
class SecurityEvent:
    """Represents a security event"""
    event_id: str
    event_type: str
    severity: str
    source_party: Optional[int]
    description: str
    metadata: Dict[str, Any]
    timestamp: datetime

@dataclass
class CommunicationChannel:
    """Represents a secure communication channel"""
    channel_id: str
    party_a: int
    party_b: int
    encryption_key: bytes
    authentication_key: bytes
    created_at: datetime
    last_used: datetime
    message_count: int

@dataclass
class SecurityPolicy:
    """Represents security policy configuration"""
    max_failed_attempts: int
    session_timeout_minutes: int
    allowed_ip_ranges: List[str]
    required_encryption_level: str
    audit_retention_days: int
    rate_limit_per_minute: int

class MPCSecurityLayer:
    """
    Comprehensive MPC Security Layer
    Implements security protocols, attack prevention, and audit logging
    """
    
    def __init__(self, party_id: int, security_policy: Optional[SecurityPolicy] = None):
        """
        Initialize MPC Security Layer
        
        Args:
            party_id: This party's identifier
            security_policy: Security policy configuration
        """
        self.party_id = party_id
        self.security_policy = security_policy or self._default_security_policy()
        
        # Security state
        self.communication_channels: Dict[str, CommunicationChannel] = {}
        self.security_events: List[SecurityEvent] = []
        self.failed_attempts: Dict[int, int] = {}  # party_id -> attempt_count
        self.blocked_parties: Set[int] = set()
        self.rate_limits: Dict[int, List[float]] = {}  # party_id -> timestamps
        
        # Cryptographic components
        self.backend = default_backend()
        self.master_encryption_key: Optional[bytes] = None
        self.authentication_keys: Dict[int, bytes] = {}
        
        # Audit logging
        self.audit_log: List[Dict[str, Any]] = []
        self.is_initialized = False
        
        logger.info(f"MPC Security Layer initialized for party {party_id}")
    
    def _default_security_policy(self) -> SecurityPolicy:
        """Create default security policy"""
        return SecurityPolicy(
            max_failed_attempts=3,
            session_timeout_minutes=30,
            allowed_ip_ranges=['127.0.0.1/32', '10.0.0.0/8', '***********/16'],
            required_encryption_level='AES-256-GCM',
            audit_retention_days=90,
            rate_limit_per_minute=100
        )
    
    async def initialize(self) -> bool:
        """Initialize the security layer"""
        try:
            # Generate master encryption key
            self.master_encryption_key = secrets.token_bytes(32)
            
            # Initialize authentication keys for each party
            await self._initialize_authentication_keys()
            
            # Start security monitoring
            asyncio.create_task(self._security_monitor_loop())
            
            self.is_initialized = True
            await self._log_security_event('SECURITY_LAYER_INITIALIZED', 'INFO', 
                                         'Security layer successfully initialized')
            
            logger.info("MPC Security Layer successfully initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize MPC Security Layer: {e}")
            return False
    
    async def _initialize_authentication_keys(self):
        """Initialize authentication keys for secure communication"""
        # Generate authentication key for this party
        self.authentication_keys[self.party_id] = secrets.token_bytes(32)
        
        # In practice, exchange keys with other parties through secure channels
        logger.info("Authentication keys initialized")
    
    async def establish_secure_channel(self, other_party_id: int) -> str:
        """
        Establish secure communication channel with another party
        
        Args:
            other_party_id: ID of the other party
            
        Returns:
            str: Channel identifier
        """
        if not self.is_initialized:
            raise RuntimeError("Security layer not initialized")
        
        if other_party_id in self.blocked_parties:
            raise ValueError(f"Party {other_party_id} is blocked")
        
        try:
            channel_id = f"channel_{self.party_id}_{other_party_id}_{int(time.time())}"
            
            # Generate encryption and authentication keys for this channel
            encryption_key = secrets.token_bytes(32)
            authentication_key = secrets.token_bytes(32)
            
            channel = CommunicationChannel(
                channel_id=channel_id,
                party_a=self.party_id,
                party_b=other_party_id,
                encryption_key=encryption_key,
                authentication_key=authentication_key,
                created_at=datetime.now(timezone.utc),
                last_used=datetime.now(timezone.utc),
                message_count=0
            )
            
            self.communication_channels[channel_id] = channel
            
            await self._log_security_event('SECURE_CHANNEL_ESTABLISHED', 'INFO',
                                         f'Secure channel established with party {other_party_id}',
                                         {'channel_id': channel_id, 'other_party': other_party_id})
            
            logger.info(f"Established secure channel: {channel_id}")
            return channel_id
            
        except Exception as e:
            logger.error(f"Failed to establish secure channel: {e}")
            raise
    
    async def encrypt_message(self, channel_id: str, message: bytes) -> bytes:
        """
        Encrypt message for secure transmission
        
        Args:
            channel_id: Channel identifier
            message: Message to encrypt
            
        Returns:
            bytes: Encrypted message
        """
        if channel_id not in self.communication_channels:
            raise ValueError(f"Channel not found: {channel_id}")
        
        channel = self.communication_channels[channel_id]
        
        try:
            # Generate random IV
            iv = secrets.token_bytes(12)
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(channel.encryption_key),
                modes.GCM(iv),
                backend=self.backend
            )
            encryptor = cipher.encryptor()
            
            # Encrypt message
            ciphertext = encryptor.update(message) + encryptor.finalize()
            
            # Create authentication tag
            auth_data = iv + ciphertext + encryptor.tag
            auth_tag = hmac.new(
                channel.authentication_key,
                auth_data,
                hashlib.sha256
            ).digest()
            
            # Combine all components
            encrypted_message = iv + encryptor.tag + auth_tag + ciphertext
            
            # Update channel usage
            channel.last_used = datetime.now(timezone.utc)
            channel.message_count += 1
            
            return encrypted_message
            
        except Exception as e:
            logger.error(f"Failed to encrypt message: {e}")
            raise
    
    async def decrypt_message(self, channel_id: str, encrypted_message: bytes) -> bytes:
        """
        Decrypt received message
        
        Args:
            channel_id: Channel identifier
            encrypted_message: Encrypted message
            
        Returns:
            bytes: Decrypted message
        """
        if channel_id not in self.communication_channels:
            raise ValueError(f"Channel not found: {channel_id}")
        
        channel = self.communication_channels[channel_id]
        
        try:
            # Extract components
            iv = encrypted_message[:12]
            gcm_tag = encrypted_message[12:28]
            auth_tag = encrypted_message[28:60]
            ciphertext = encrypted_message[60:]
            
            # Verify authentication tag
            expected_auth_tag = hmac.new(
                channel.authentication_key,
                iv + ciphertext + gcm_tag,
                hashlib.sha256
            ).digest()
            
            if not hmac.compare_digest(auth_tag, expected_auth_tag):
                await self._log_security_event('AUTHENTICATION_FAILURE', 'HIGH',
                                             f'Authentication failure on channel {channel_id}')
                raise ValueError("Authentication verification failed")
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(channel.encryption_key),
                modes.GCM(iv, gcm_tag),
                backend=self.backend
            )
            decryptor = cipher.decryptor()
            
            # Decrypt message
            decrypted_message = decryptor.update(ciphertext) + decryptor.finalize()
            
            # Update channel usage
            channel.last_used = datetime.now(timezone.utc)
            
            return decrypted_message
            
        except Exception as e:
            logger.error(f"Failed to decrypt message: {e}")
            await self._log_security_event('DECRYPTION_FAILURE', 'HIGH',
                                         f'Decryption failure on channel {channel_id}')
            raise
    
    async def validate_party_request(self, party_id: int, request_type: str, 
                                   source_ip: Optional[str] = None) -> bool:
        """
        Validate request from another party
        
        Args:
            party_id: ID of requesting party
            request_type: Type of request
            source_ip: Source IP address
            
        Returns:
            bool: True if request is valid
        """
        try:
            # Check if party is blocked
            if party_id in self.blocked_parties:
                await self._log_security_event('BLOCKED_PARTY_REQUEST', 'MEDIUM',
                                             f'Request from blocked party {party_id}')
                return False
            
            # Check rate limiting
            if not await self._check_rate_limit(party_id):
                await self._log_security_event('RATE_LIMIT_EXCEEDED', 'MEDIUM',
                                             f'Rate limit exceeded for party {party_id}')
                return False
            
            # Check IP whitelist if provided
            if source_ip and not self._is_ip_allowed(source_ip):
                await self._log_security_event('UNAUTHORIZED_IP', 'HIGH',
                                             f'Request from unauthorized IP: {source_ip}')
                return False
            
            # Log successful validation
            await self._log_audit_event('REQUEST_VALIDATED', {
                'party_id': party_id,
                'request_type': request_type,
                'source_ip': source_ip
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to validate party request: {e}")
            return False
    
    async def _check_rate_limit(self, party_id: int) -> bool:
        """Check if party is within rate limits"""
        current_time = time.time()
        
        if party_id not in self.rate_limits:
            self.rate_limits[party_id] = []
        
        # Remove old timestamps (older than 1 minute)
        self.rate_limits[party_id] = [
            timestamp for timestamp in self.rate_limits[party_id]
            if current_time - timestamp < 60
        ]
        
        # Check if within limit
        if len(self.rate_limits[party_id]) >= self.security_policy.rate_limit_per_minute:
            return False
        
        # Add current timestamp
        self.rate_limits[party_id].append(current_time)
        return True
    
    def _is_ip_allowed(self, ip_address: str) -> bool:
        """Check if IP address is in allowed ranges"""
        try:
            ip = ipaddress.ip_address(ip_address)
            
            for allowed_range in self.security_policy.allowed_ip_ranges:
                if ip in ipaddress.ip_network(allowed_range):
                    return True
            
            return False
            
        except Exception:
            return False
    
    async def handle_security_violation(self, party_id: int, violation_type: str, 
                                      details: str):
        """
        Handle security violation from a party
        
        Args:
            party_id: ID of violating party
            violation_type: Type of violation
            details: Violation details
        """
        try:
            # Increment failed attempts
            self.failed_attempts[party_id] = self.failed_attempts.get(party_id, 0) + 1
            
            # Check if should block party
            if self.failed_attempts[party_id] >= self.security_policy.max_failed_attempts:
                self.blocked_parties.add(party_id)
                await self._log_security_event('PARTY_BLOCKED', 'HIGH',
                                             f'Party {party_id} blocked due to repeated violations')
            
            # Log security violation
            await self._log_security_event('SECURITY_VIOLATION', 'HIGH',
                                         f'Security violation from party {party_id}: {violation_type}',
                                         {'party_id': party_id, 'violation_type': violation_type, 'details': details})
            
            logger.warning(f"Security violation from party {party_id}: {violation_type}")
            
        except Exception as e:
            logger.error(f"Failed to handle security violation: {e}")
    
    async def _log_security_event(self, event_type: str, severity: str, 
                                description: str, metadata: Optional[Dict[str, Any]] = None):
        """Log security event"""
        event = SecurityEvent(
            event_id=f"sec_{int(time.time() * 1000)}_{secrets.token_hex(4)}",
            event_type=event_type,
            severity=severity,
            source_party=self.party_id,
            description=description,
            metadata=metadata or {},
            timestamp=datetime.now(timezone.utc)
        )
        
        self.security_events.append(event)
        
        # Keep only recent events
        if len(self.security_events) > 10000:
            self.security_events = self.security_events[-5000:]
    
    async def _log_audit_event(self, action: str, details: Dict[str, Any]):
        """Log audit event"""
        audit_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'party_id': self.party_id,
            'action': action,
            'details': details
        }
        
        self.audit_log.append(audit_entry)
        
        # Keep only recent audit entries
        if len(self.audit_log) > 50000:
            self.audit_log = self.audit_log[-25000:]
    
    async def _security_monitor_loop(self):
        """Background security monitoring loop"""
        while self.is_initialized:
            try:
                # Clean up expired channels
                await self._cleanup_expired_channels()
                
                # Reset failed attempts for unblocked parties
                await self._reset_failed_attempts()
                
                # Clean up old security events
                await self._cleanup_old_events()
                
                # Sleep for monitoring interval
                await asyncio.sleep(60)  # Monitor every minute
                
            except Exception as e:
                logger.error(f"Error in security monitor loop: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_expired_channels(self):
        """Clean up expired communication channels"""
        current_time = datetime.now(timezone.utc)
        timeout_minutes = self.security_policy.session_timeout_minutes
        
        expired_channels = []
        for channel_id, channel in self.communication_channels.items():
            if (current_time - channel.last_used).total_seconds() > timeout_minutes * 60:
                expired_channels.append(channel_id)
        
        for channel_id in expired_channels:
            del self.communication_channels[channel_id]
            await self._log_security_event('CHANNEL_EXPIRED', 'INFO',
                                         f'Communication channel expired: {channel_id}')
    
    async def _reset_failed_attempts(self):
        """Reset failed attempts for parties that haven't failed recently"""
        # Reset failed attempts every hour for unblocked parties
        current_time = time.time()
        if not hasattr(self, '_last_reset') or current_time - self._last_reset > 3600:
            for party_id in list(self.failed_attempts.keys()):
                if party_id not in self.blocked_parties:
                    self.failed_attempts[party_id] = 0
            self._last_reset = current_time
    
    async def _cleanup_old_events(self):
        """Clean up old security events and audit logs"""
        retention_days = self.security_policy.audit_retention_days
        cutoff_time = datetime.now(timezone.utc).replace(
            day=datetime.now(timezone.utc).day - retention_days
        )
        
        # Clean security events
        self.security_events = [
            event for event in self.security_events
            if event.timestamp > cutoff_time
        ]
        
        # Clean audit log
        self.audit_log = [
            entry for entry in self.audit_log
            if datetime.fromisoformat(entry['timestamp'].replace('Z', '+00:00')) > cutoff_time
        ]
    
    async def get_security_status(self) -> Dict[str, Any]:
        """Get current security status"""
        return {
            'party_id': self.party_id,
            'active_channels': len(self.communication_channels),
            'blocked_parties': list(self.blocked_parties),
            'recent_events': len([e for e in self.security_events 
                                if (datetime.now(timezone.utc) - e.timestamp).total_seconds() < 3600]),
            'security_policy': asdict(self.security_policy),
            'is_initialized': self.is_initialized
        }
    
    async def cleanup(self):
        """Cleanup security layer resources"""
        self.communication_channels.clear()
        self.security_events.clear()
        self.failed_attempts.clear()
        self.blocked_parties.clear()
        self.rate_limits.clear()
        self.authentication_keys.clear()
        self.audit_log.clear()
        self.is_initialized = False
        logger.info("MPC Security Layer cleaned up")
