#!/usr/bin/env python3
"""
Comprehensive Bybit API Data Collector - NO FAKE DATA
Collects ALL trading and account statistics from Bybit API for strategy learning
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import sqlite3

class ComprehensiveBybitDataCollector:
    """Collects ALL Bybit API data for strategy learning - NO FAKE DATA"""
    
    def __init__(self, bybit_client, db_path: str = "bybit_trading_bot.db"):
        self.bybit_client = bybit_client
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.collection_active = False
        
        # Initialize database tables for comprehensive data
        self._init_comprehensive_tables()
    
    def _init_comprehensive_tables(self):
        """Initialize database tables for comprehensive Bybit data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Account statistics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS account_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    total_equity REAL,
                    available_balance REAL,
                    used_margin REAL,
                    margin_ratio REAL,
                    unrealized_pnl REAL,
                    total_wallet_balance REAL,
                    account_mm_rate REAL,
                    account_im_rate REAL,
                    total_session_upl REAL,
                    total_session_rpl REAL,
                    coin VARCHAR(10),
                    raw_data TEXT
                )
            ''')
            
            # Position statistics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS position_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    symbol VARCHAR(20),
                    side VARCHAR(10),
                    size REAL,
                    position_value REAL,
                    entry_price REAL,
                    mark_price REAL,
                    liq_price REAL,
                    bust_price REAL,
                    leverage REAL,
                    position_margin REAL,
                    occ_closing_fee REAL,
                    realised_pnl REAL,
                    unrealised_pnl REAL,
                    cum_realised_pnl REAL,
                    position_mm REAL,
                    position_im REAL,
                    auto_add_margin INTEGER,
                    position_status VARCHAR(20),
                    adl_rank_indicator INTEGER,
                    raw_data TEXT
                )
            ''')
            
            # Execution history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS execution_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    symbol VARCHAR(20),
                    order_id VARCHAR(100),
                    exec_id VARCHAR(100),
                    side VARCHAR(10),
                    exec_qty REAL,
                    exec_price REAL,
                    exec_fee REAL,
                    exec_time BIGINT,
                    is_maker BOOLEAN,
                    fee_rate REAL,
                    trade_iv REAL,
                    mark_iv REAL,
                    mark_price REAL,
                    index_price REAL,
                    underlying_price REAL,
                    block_trade_id VARCHAR(100),
                    raw_data TEXT
                )
            ''')
            
            # Order history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS order_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    order_id VARCHAR(100),
                    order_link_id VARCHAR(100),
                    symbol VARCHAR(20),
                    side VARCHAR(10),
                    order_type VARCHAR(20),
                    qty REAL,
                    price REAL,
                    time_in_force VARCHAR(10),
                    order_status VARCHAR(20),
                    leaves_qty REAL,
                    leaves_value REAL,
                    cum_exec_qty REAL,
                    cum_exec_value REAL,
                    cum_exec_fee REAL,
                    avg_price REAL,
                    created_time BIGINT,
                    updated_time BIGINT,
                    reject_reason VARCHAR(100),
                    stop_order_type VARCHAR(20),
                    trigger_price REAL,
                    take_profit REAL,
                    stop_loss REAL,
                    tp_trigger_by VARCHAR(20),
                    sl_trigger_by VARCHAR(20),
                    reduce_only BOOLEAN,
                    close_on_trigger BOOLEAN,
                    raw_data TEXT
                )
            ''')
            
            # Market data statistics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    symbol VARCHAR(20),
                    last_price REAL,
                    index_price REAL,
                    mark_price REAL,
                    prev_price_24h REAL,
                    price_24h_pcnt REAL,
                    high_price_24h REAL,
                    low_price_24h REAL,
                    prev_price_1h REAL,
                    open_interest REAL,
                    open_interest_value REAL,
                    turnover_24h REAL,
                    volume_24h REAL,
                    funding_rate REAL,
                    next_funding_time BIGINT,
                    predicted_funding_rate REAL,
                    basis REAL,
                    delivery_fee_rate REAL,
                    delivery_time BIGINT,
                    ask1_size REAL,
                    bid1_size REAL,
                    ask1_price REAL,
                    bid1_price REAL,
                    raw_data TEXT
                )
            ''')
            
            # Trading performance analytics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    period_start DATETIME,
                    period_end DATETIME,
                    total_trades INTEGER,
                    winning_trades INTEGER,
                    losing_trades INTEGER,
                    win_rate REAL,
                    total_pnl REAL,
                    total_fees REAL,
                    avg_win REAL,
                    avg_loss REAL,
                    largest_win REAL,
                    largest_loss REAL,
                    profit_factor REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    avg_trade_duration REAL,
                    total_volume REAL,
                    symbols_traded TEXT,
                    raw_data TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            self.logger.info("Comprehensive Bybit data tables initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing comprehensive tables: {e}")
    
    async def start_comprehensive_collection(self):
        """Start comprehensive data collection from ALL Bybit APIs"""
        self.collection_active = True
        self.logger.info("COMPREHENSIVE BYBIT DATA COLLECTION: Started - NO FAKE DATA")
        
        while self.collection_active:
            try:
                # Collect all data types in parallel
                await asyncio.gather(
                    self._collect_account_statistics(),
                    self._collect_position_statistics(),
                    self._collect_execution_history(),
                    self._collect_order_history(),
                    self._collect_market_statistics(),
                    self._analyze_trading_performance(),
                    return_exceptions=True
                )
                
                # Wait before next collection cycle
                await asyncio.sleep(60)  # Collect every minute
                
            except Exception as e:
                self.logger.error(f"Error in comprehensive data collection: {e}")
                await asyncio.sleep(120)  # Wait longer on error
    
    async def stop_collection(self):
        """Stop comprehensive data collection"""
        self.collection_active = False
        self.logger.info("Comprehensive Bybit data collection stopped")
    
    async def _collect_account_statistics(self):
        """Collect REAL account statistics from Bybit API"""
        try:
            # Get wallet balance
            wallet_data = await self.bybit_client.get_wallet_balance()
            
            if not wallet_data:
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for coin_data in wallet_data.get('list', []):
                try:
                    coin = coin_data.get('coin', '')
                    total_equity = float(coin_data.get('equity', 0))
                    available_balance = float(coin_data.get('availableToWithdraw', 0))
                    used_margin = float(coin_data.get('usdValue', 0)) - available_balance
                    unrealized_pnl = float(coin_data.get('unrealisedPnl', 0))
                    total_wallet_balance = float(coin_data.get('walletBalance', 0))
                    
                    cursor.execute('''
                        INSERT INTO account_statistics (
                            total_equity, available_balance, used_margin, 
                            unrealized_pnl, total_wallet_balance, coin, raw_data
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        total_equity, available_balance, used_margin,
                        unrealized_pnl, total_wallet_balance, coin, str(coin_data)
                    ))
                    
                    self.logger.info(f"ACCOUNT DATA: {coin} Equity=${total_equity:.2f} Available=${available_balance:.2f}")
                    
                except Exception as e:
                    self.logger.error(f"Error processing account data for {coin_data}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error collecting account statistics: {e}")
    
    async def _collect_position_statistics(self):
        """Collect REAL position statistics from Bybit API"""
        try:
            positions = await self.bybit_client.get_positions()
            
            if not positions:
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for position in positions:
                try:
                    symbol = position.get('symbol', '')
                    side = position.get('side', '')
                    size = float(position.get('size', 0))
                    
                    if size == 0:  # Skip empty positions
                        continue
                    
                    entry_price = float(position.get('avgPrice', 0))
                    mark_price = float(position.get('markPrice', 0))
                    liq_price = float(position.get('liqPrice', 0))
                    leverage = float(position.get('leverage', 0))
                    unrealised_pnl = float(position.get('unrealisedPnl', 0))
                    cum_realised_pnl = float(position.get('cumRealisedPnl', 0))
                    position_value = float(position.get('positionValue', 0))
                    
                    cursor.execute('''
                        INSERT INTO position_statistics (
                            symbol, side, size, position_value, entry_price, mark_price,
                            liq_price, leverage, unrealised_pnl, cum_realised_pnl, raw_data
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        symbol, side, size, position_value, entry_price, mark_price,
                        liq_price, leverage, unrealised_pnl, cum_realised_pnl, str(position)
                    ))
                    
                    self.logger.info(f"POSITION DATA: {symbol} {side} {size} @ ${entry_price:.2f} PnL=${unrealised_pnl:.2f}")
                    
                except Exception as e:
                    self.logger.error(f"Error processing position data for {position}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error collecting position statistics: {e}")

    async def _collect_execution_history(self):
        """Collect REAL execution history from Bybit API"""
        try:
            executions = await self.bybit_client.get_order_history(limit=100)

            if not executions:
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for execution in executions:
                try:
                    symbol = execution.get('symbol', '')
                    order_id = execution.get('orderId', '')
                    exec_id = execution.get('execId', '')
                    side = execution.get('side', '')
                    exec_qty = float(execution.get('execQty', 0))
                    exec_price = float(execution.get('execPrice', 0))
                    exec_fee = float(execution.get('execFee', 0))
                    exec_time = int(execution.get('execTime', 0))
                    is_maker = execution.get('isMaker', False)
                    fee_rate = float(execution.get('feeRate', 0))
                    mark_price = float(execution.get('markPrice', 0))

                    # Check if execution already exists
                    cursor.execute('SELECT id FROM execution_history WHERE exec_id = ?', (exec_id,))
                    if cursor.fetchone():
                        continue  # Skip duplicates

                    cursor.execute('''
                        INSERT INTO execution_history (
                            symbol, order_id, exec_id, side, exec_qty, exec_price,
                            exec_fee, exec_time, is_maker, fee_rate, mark_price, raw_data
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        symbol, order_id, exec_id, side, exec_qty, exec_price,
                        exec_fee, exec_time, is_maker, fee_rate, mark_price, str(execution)
                    ))

                    self.logger.info(f"EXECUTION DATA: {symbol} {side} {exec_qty} @ ${exec_price:.2f} Fee=${exec_fee:.4f}")

                except Exception as e:
                    self.logger.error(f"Error processing execution data for {execution}: {e}")
                    continue

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error collecting execution history: {e}")

    async def _collect_order_history(self):
        """Collect REAL order history from Bybit API"""
        try:
            orders = await self.bybit_client.get_order_history(limit=100)

            if not orders:
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for order in orders:
                try:
                    order_id = order.get('orderId', '')
                    symbol = order.get('symbol', '')
                    side = order.get('side', '')
                    order_type = order.get('orderType', '')
                    qty = float(order.get('qty', 0))
                    price = float(order.get('price', 0))
                    order_status = order.get('orderStatus', '')
                    cum_exec_qty = float(order.get('cumExecQty', 0))
                    cum_exec_value = float(order.get('cumExecValue', 0))
                    cum_exec_fee = float(order.get('cumExecFee', 0))
                    avg_price = float(order.get('avgPrice', 0))
                    created_time = int(order.get('createdTime', 0))
                    updated_time = int(order.get('updatedTime', 0))

                    # Check if order already exists
                    cursor.execute('SELECT id FROM order_history WHERE order_id = ?', (order_id,))
                    if cursor.fetchone():
                        continue  # Skip duplicates

                    cursor.execute('''
                        INSERT INTO order_history (
                            order_id, symbol, side, order_type, qty, price, order_status,
                            cum_exec_qty, cum_exec_value, cum_exec_fee, avg_price,
                            created_time, updated_time, raw_data
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        order_id, symbol, side, order_type, qty, price, order_status,
                        cum_exec_qty, cum_exec_value, cum_exec_fee, avg_price,
                        created_time, updated_time, str(order)
                    ))

                    self.logger.info(f"ORDER DATA: {symbol} {side} {order_type} {qty} @ ${price:.2f} Status={order_status}")

                except Exception as e:
                    self.logger.error(f"Error processing order data for {order}: {e}")
                    continue

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error collecting order history: {e}")

    async def _collect_market_statistics(self):
        """Collect REAL market statistics from Bybit API"""
        try:
            symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOGEUSDT']

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for symbol in symbols:
                try:
                    # Get ticker data
                    ticker = await self.bybit_client.get_ticker(symbol)
                    if not ticker:
                        continue

                    last_price = float(ticker.get('lastPrice', 0))
                    index_price = float(ticker.get('indexPrice', 0))
                    mark_price = float(ticker.get('markPrice', 0))
                    prev_price_24h = float(ticker.get('prevPrice24h', 0))
                    price_24h_pcnt = float(ticker.get('price24hPcnt', 0))
                    high_price_24h = float(ticker.get('highPrice24h', 0))
                    low_price_24h = float(ticker.get('lowPrice24h', 0))
                    volume_24h = float(ticker.get('volume24h', 0))
                    turnover_24h = float(ticker.get('turnover24h', 0))
                    open_interest = float(ticker.get('openInterest', 0))
                    funding_rate = float(ticker.get('fundingRate', 0))
                    next_funding_time = int(ticker.get('nextFundingTime', 0))

                    cursor.execute('''
                        INSERT INTO market_statistics (
                            symbol, last_price, index_price, mark_price, prev_price_24h,
                            price_24h_pcnt, high_price_24h, low_price_24h, volume_24h,
                            turnover_24h, open_interest, funding_rate, next_funding_time, raw_data
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        symbol, last_price, index_price, mark_price, prev_price_24h,
                        price_24h_pcnt, high_price_24h, low_price_24h, volume_24h,
                        turnover_24h, open_interest, funding_rate, next_funding_time, str(ticker)
                    ))

                    self.logger.info(f"MARKET DATA: {symbol} ${last_price:.2f} 24h={price_24h_pcnt:.2f}% Vol={volume_24h:.0f}")

                except Exception as e:
                    self.logger.error(f"Error processing market data for {symbol}: {e}")
                    continue

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error collecting market statistics: {e}")

    async def _analyze_trading_performance(self):
        """Analyze REAL trading performance from collected data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Analyze last 24 hours
            cursor.execute('''
                SELECT
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN profit_loss > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(CASE WHEN profit_loss < 0 THEN 1 ELSE 0 END) as losing_trades,
                    SUM(profit_loss) as total_pnl,
                    AVG(CASE WHEN profit_loss > 0 THEN profit_loss END) as avg_win,
                    AVG(CASE WHEN profit_loss < 0 THEN profit_loss END) as avg_loss,
                    MAX(profit_loss) as largest_win,
                    MIN(profit_loss) as largest_loss,
                    SUM(commission) as total_fees,
                    GROUP_CONCAT(DISTINCT symbol) as symbols_traded
                FROM trades
                WHERE timestamp > datetime('now', '-24 hours')
                AND profit_loss IS NOT NULL
            ''')

            result = cursor.fetchone()
            if result and result[0] > 0:  # If we have trades
                total_trades, winning_trades, losing_trades, total_pnl, avg_win, avg_loss, largest_win, largest_loss, total_fees, symbols_traded = result

                win_rate = (winning_trades / total_trades) if total_trades > 0 else 0
                profit_factor = (avg_win * winning_trades / abs(avg_loss * losing_trades)) if avg_loss and losing_trades > 0 else 0

                # Store analytics
                cursor.execute('''
                    INSERT INTO trading_analytics (
                        period_start, period_end, total_trades, winning_trades, losing_trades,
                        win_rate, total_pnl, total_fees, avg_win, avg_loss, largest_win,
                        largest_loss, profit_factor, symbols_traded
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    datetime.now() - timedelta(hours=24), datetime.now(),
                    total_trades, winning_trades, losing_trades, win_rate,
                    total_pnl, total_fees, avg_win, avg_loss, largest_win,
                    largest_loss, profit_factor, symbols_traded
                ))

                self.logger.info(f"PERFORMANCE ANALYTICS: {total_trades} trades, {win_rate:.1%} win rate, ${total_pnl:.2f} PnL")

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error analyzing trading performance: {e}")

    async def get_comprehensive_learning_data(self) -> Dict[str, Any]:
        """Get comprehensive learning data for strategy improvement - NO FAKE DATA"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            learning_data = {}

            # Get recent account performance
            cursor.execute('''
                SELECT total_equity, available_balance, unrealized_pnl
                FROM account_statistics
                ORDER BY timestamp DESC LIMIT 1
            ''')
            account_data = cursor.fetchone()
            if account_data:
                learning_data['account'] = {
                    'total_equity': account_data[0],
                    'available_balance': account_data[1],
                    'unrealized_pnl': account_data[2]
                }

            # Get recent trading performance
            cursor.execute('''
                SELECT win_rate, total_pnl, profit_factor, total_trades
                FROM trading_analytics
                ORDER BY timestamp DESC LIMIT 1
            ''')
            performance_data = cursor.fetchone()
            if performance_data:
                learning_data['performance'] = {
                    'win_rate': performance_data[0],
                    'total_pnl': performance_data[1],
                    'profit_factor': performance_data[2],
                    'total_trades': performance_data[3]
                }

            # Get market conditions
            cursor.execute('''
                SELECT symbol, price_24h_pcnt, volume_24h, funding_rate
                FROM market_statistics
                WHERE timestamp > datetime('now', '-1 hour')
                ORDER BY timestamp DESC
            ''')
            market_data = cursor.fetchall()
            learning_data['market_conditions'] = [
                {
                    'symbol': row[0],
                    'price_change_24h': row[1],
                    'volume_24h': row[2],
                    'funding_rate': row[3]
                }
                for row in market_data
            ]

            conn.close()
            return learning_data

        except Exception as e:
            self.logger.error(f"Error getting comprehensive learning data: {e}")
            return {}
