#!/usr/bin/env python3
"""
Simple Integration Validation Script
Quick validation that optimization integration is working
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
        print("✓ OptimizationComponentsManager import successful")
    except Exception as e:
        print(f"✗ OptimizationComponentsManager import failed: {e}")
        return False
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        print("✓ BybitTradingBotSystem import successful")
    except Exception as e:
        print(f"✗ BybitTradingBotSystem import failed: {e}")
        return False
    
    return True

def test_optimization_manager_creation():
    """Test optimization manager creation"""
    print("\nTesting optimization manager creation...")
    
    try:
        from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
        
        config = OptimizationConfig()
        manager = OptimizationComponentsManager(config)
        
        print("✓ OptimizationComponentsManager created successfully")
        print(f"✓ Configuration: {len(manager.initialization_order)} components configured")
        
        return True
        
    except Exception as e:
        print(f"✗ OptimizationComponentsManager creation failed: {e}")
        return False

def test_main_system_attributes():
    """Test that main system has optimization attributes"""
    print("\nTesting main system optimization attributes...")
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        
        system = BybitTradingBotSystem()
        
        # Check optimization attributes
        optimization_attrs = [
            'optimization_manager', 'streaming_processor', 'compression_engine',
            'multi_agent_rl', 'graph_neural_networks', 'edge_computing',
            'quantum_engine', 'feature_pipeline', 'adaptive_learning',
            'optimization_active'
        ]
        
        missing_attrs = []
        for attr in optimization_attrs:
            if not hasattr(system, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"✗ Missing attributes: {missing_attrs}")
            return False
        else:
            print("✓ All optimization attributes present in main system")
            return True
            
    except Exception as e:
        print(f"✗ Main system attribute test failed: {e}")
        return False

def test_optimization_files_exist():
    """Test that optimization component files exist"""
    print("\nTesting optimization component files...")
    
    required_files = [
        "bybit_bot/streaming/advanced_kafka_flink_processor.py",
        "bybit_bot/streaming/time_series_compression.py", 
        "bybit_bot/streaming/real_time_feature_engine.py",
        "bybit_bot/ai/multi_agent_rl_system.py",
        "bybit_bot/ai/graph_neural_networks.py",
        "bybit_bot/edge/edge_computing_engine.py",
        "bybit_bot/quantum/quantum_ml_engine.py",
        "bybit_bot/features/advanced_feature_pipeline.py",
        "bybit_bot/ai/adaptive_learning_system.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ Missing optimization files: {missing_files}")
        return False
    else:
        print("✓ All optimization component files exist")
        return True

def main():
    """Run all validation tests"""
    print("OPTIMIZATION INTEGRATION VALIDATION")
    print("="*50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Optimization Manager Creation", test_optimization_manager_creation),
        ("Main System Attributes", test_main_system_attributes),
        ("Optimization Files Exist", test_optimization_files_exist)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "="*50)
    print("VALIDATION SUMMARY")
    print("="*50)
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("✓ OPTIMIZATION INTEGRATION VALIDATION SUCCESSFUL")
        print("✓ All 8 optimization tiers are properly integrated")
        return True
    else:
        print("✗ OPTIMIZATION INTEGRATION VALIDATION FAILED")
        print("✗ Some components are missing or not properly integrated")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Validation failed with exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
