#!/usr/bin/env python3
"""
Comprehensive Error Testing for main.py
Tests syntax, imports, runtime errors, and integration issues
"""

import sys
import os
import ast
import traceback
import asyncio
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_file_syntax():
    """Test main.py for syntax errors"""
    print("=" * 60)
    print("TESTING SYNTAX ERRORS")
    print("=" * 60)
    
    try:
        main_py_path = Path("bybit_bot/main.py")
        
        if not main_py_path.exists():
            print("✗ ERROR: main.py file not found")
            return False
        
        print("✓ main.py file found")
        
        # Read the file
        with open(main_py_path, 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        print(f"✓ File read successfully ({len(source_code):,} characters)")
        print(f"✓ File has {source_code.count(chr(10))} lines")
        
        # Parse for syntax errors
        try:
            tree = ast.parse(source_code, filename=str(main_py_path))
            print("✓ AST parsing successful")
            print("✓ NO SYNTAX ERRORS FOUND")
            
            # Count different node types
            classes = sum(1 for node in ast.walk(tree) if isinstance(node, ast.ClassDef))
            functions = sum(1 for node in ast.walk(tree) if isinstance(node, ast.FunctionDef))
            async_functions = sum(1 for node in ast.walk(tree) if isinstance(node, ast.AsyncFunctionDef))
            imports = sum(1 for node in ast.walk(tree) if isinstance(node, (ast.Import, ast.ImportFrom)))
            
            print(f"✓ Code structure: {classes} classes, {functions} functions, {async_functions} async functions, {imports} imports")
            
            return True
            
        except SyntaxError as e:
            print(f"✗ SYNTAX ERROR FOUND:")
            print(f"  File: {e.filename}")
            print(f"  Line {e.lineno}: {e.text}")
            print(f"  Error: {e.msg}")
            if e.offset:
                print(f"  Position: {' ' * (e.offset - 1)}^")
            return False
            
    except Exception as e:
        print(f"✗ Error during syntax check: {e}")
        traceback.print_exc()
        return False

def test_critical_imports():
    """Test critical imports"""
    print("\n" + "=" * 60)
    print("TESTING CRITICAL IMPORTS")
    print("=" * 60)
    
    import_tests = [
        ("Optimization Manager", "bybit_bot.core.optimization_manager", ["OptimizationComponentsManager", "OptimizationConfig"]),
        ("Main System", "bybit_bot.main", ["BybitTradingBotSystem"]),
        ("Enhanced Config", "bybit_bot.core.config", ["EnhancedBotConfig"]),
        ("Database Manager", "bybit_bot.database.connection", ["DatabaseManager"]),
        ("Enhanced Bybit Client", "bybit_bot.exchange.enhanced_bybit_client", ["EnhancedBybitClient"])
    ]
    
    success_count = 0
    
    for test_name, module_path, class_names in import_tests:
        try:
            print(f"\nTesting {test_name}...")
            module = __import__(module_path, fromlist=class_names)
            
            # Check if all classes exist
            missing_classes = []
            for class_name in class_names:
                if hasattr(module, class_name):
                    print(f"  ✓ {class_name}")
                else:
                    print(f"  ✗ {class_name} MISSING")
                    missing_classes.append(class_name)
            
            if missing_classes:
                print(f"  ⚠ Missing classes: {missing_classes}")
            else:
                print(f"  ✓ {test_name}: All classes available")
                success_count += 1
                
        except ImportError as e:
            print(f"  ✗ {test_name}: Import failed")
            print(f"    Error: {e}")
        except Exception as e:
            print(f"  ✗ {test_name}: Unexpected error")
            print(f"    Error: {e}")
    
    print(f"\nImport Summary: {success_count}/{len(import_tests)} modules imported successfully")
    return success_count >= len(import_tests) // 2  # At least 50% success

def test_optimization_components():
    """Test optimization component imports"""
    print("\n" + "=" * 60)
    print("TESTING OPTIMIZATION COMPONENTS")
    print("=" * 60)
    
    optimization_tests = [
        ("Streaming Processor", "bybit_bot.streaming.advanced_kafka_flink_processor", "AdvancedStreamProcessor"),
        ("Compression Engine", "bybit_bot.streaming.time_series_compression", "AdaptiveTimeSeriesCompressor"),
        ("Multi-Agent RL", "bybit_bot.ai.multi_agent_rl_system", "MultiAgentTradingSystem"),
        ("Graph Neural Networks", "bybit_bot.ai.graph_neural_networks", "MarketGraphAnalyzer"),
        ("Edge Computing", "bybit_bot.edge.edge_computing_engine", "EdgeComputeOrchestrator"),
        ("Quantum Engine", "bybit_bot.quantum.quantum_ml_engine", "QuantumTradingEngine"),
        ("Feature Pipeline", "bybit_bot.features.advanced_feature_pipeline", "AdvancedFeaturePipeline"),
        ("Adaptive Learning", "bybit_bot.ai.adaptive_learning_system", "AdaptiveLearningSystem")
    ]
    
    available_count = 0
    
    for tier_name, module_path, class_name in optimization_tests:
        try:
            print(f"\nTesting {tier_name}...")
            module = __import__(module_path, fromlist=[class_name])
            
            if hasattr(module, class_name):
                cls = getattr(module, class_name)
                print(f"  ✓ {class_name} available")
                print(f"  ✓ Class type: {type(cls)}")
                available_count += 1
            else:
                print(f"  ✗ {class_name} not found in module")
                
        except ImportError as e:
            print(f"  ⚠ {tier_name}: Module not available")
            print(f"    Error: {str(e)[:100]}...")
        except Exception as e:
            print(f"  ✗ {tier_name}: Unexpected error")
            print(f"    Error: {str(e)[:100]}...")
    
    print(f"\nOptimization Components Available: {available_count}/{len(optimization_tests)}")
    return available_count >= len(optimization_tests) // 2  # At least 50% available

async def test_system_creation():
    """Test system creation and basic functionality"""
    print("\n" + "=" * 60)
    print("TESTING SYSTEM CREATION")
    print("=" * 60)
    
    try:
        print("1. Importing main system...")
        from bybit_bot.main import BybitTradingBotSystem
        print("   ✓ Main system imported successfully")
        
        print("2. Creating system instance...")
        system = BybitTradingBotSystem()
        print("   ✓ System instance created successfully")
        
        print("3. Checking optimization attributes...")
        optimization_attrs = [
            'optimization_manager', 'optimization_active', 'streaming_processor',
            'compression_engine', 'multi_agent_rl', 'graph_neural_networks',
            'edge_computing', 'quantum_engine', 'feature_pipeline', 'adaptive_learning'
        ]
        
        present_attrs = []
        missing_attrs = []
        
        for attr in optimization_attrs:
            if hasattr(system, attr):
                present_attrs.append(attr)
                print(f"   ✓ {attr}")
            else:
                missing_attrs.append(attr)
                print(f"   ✗ {attr} MISSING")
        
        print(f"\n   Attribute Summary: {len(present_attrs)}/{len(optimization_attrs)} optimization attributes present")
        
        if missing_attrs:
            print(f"   Missing: {missing_attrs}")
            return False
        
        print("4. Testing optimization manager creation...")
        try:
            from bybit_bot.core.optimization_manager import OptimizationConfig, OptimizationComponentsManager
            
            config = OptimizationConfig()
            manager = OptimizationComponentsManager(config)
            print("   ✓ Optimization manager created successfully")
            
            # Test manager attributes
            required_attrs = ['config', 'components', 'component_status', 'initialization_order']
            for attr in required_attrs:
                if hasattr(manager, attr):
                    print(f"   ✓ Manager has {attr}")
                else:
                    print(f"   ✗ Manager missing {attr}")
                    return False
            
        except Exception as e:
            print(f"   ✗ Optimization manager creation failed: {e}")
            return False
        
        print("5. Testing basic method existence...")
        critical_methods = [
            'initialize_components', 'initialize_all_systems', 'run', 'cleanup'
        ]
        
        for method_name in critical_methods:
            if hasattr(system, method_name) and callable(getattr(system, method_name)):
                print(f"   ✓ {method_name} method exists")
            else:
                print(f"   ✗ {method_name} method missing or not callable")
                return False
        
        print("\n✓ SYSTEM CREATION TEST PASSED")
        return True
        
    except Exception as e:
        print(f"\n✗ SYSTEM CREATION TEST FAILED: {e}")
        traceback.print_exc()
        return False

async def run_all_tests():
    """Run all error tests"""
    print("COMPREHENSIVE ERROR TESTING FOR MAIN.PY")
    print("=" * 80)
    print("Testing syntax, imports, system creation, and optimization integration")
    print("=" * 80)
    
    tests = [
        ("File Syntax", test_file_syntax),
        ("Critical Imports", test_critical_imports),
        ("Optimization Components", test_optimization_components),
        ("System Creation", test_system_creation)
    ]
    
    results = []
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name.upper()} {'='*20}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append(result)
            status = "PASS" if result else "FAIL"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            print(f"\n{test_name}: FAIL (Exception: {e})")
            traceback.print_exc()
            results.append(False)
    
    total_time = time.time() - start_time
    
    # Final summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 80)
    print("ERROR TESTING SUMMARY")
    print("=" * 80)
    
    for i, (test_name, _) in enumerate(tests):
        status = "PASS" if results[i] else "FAIL"
        print(f"{status:4} | {test_name}")
    
    print("-" * 80)
    print(f"TOTAL: {passed}/{total} tests passed ({passed/total:.1%})")
    print(f"TIME:  {total_time:.1f} seconds")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED - NO ERRORS FOUND! 🎉")
        print("✅ main.py is syntactically correct")
        print("✅ All critical imports work")
        print("✅ Optimization components are available")
        print("✅ System creation works properly")
        print("✅ MAIN.PY IS READY FOR EXECUTION!")
        return True
    else:
        print(f"\n❌ {total-passed} ERROR(S) FOUND")
        print("❌ Review and fix errors before running main.py")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        print(f"\nExit code: {0 if success else 1}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nError testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error testing failed: {e}")
        traceback.print_exc()
        sys.exit(1)
