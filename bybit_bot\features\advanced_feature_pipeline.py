#!/usr/bin/env python3
"""
Advanced Feature Engineering Pipeline
Implements 200+ technical indicators, alternative data integration, and dynamic feature selection

Features:
- 200+ technical indicators (TA-Lib, custom implementations)
- Alternative data integration (sentiment, news, social media)
- Dynamic feature selection using mutual information
- Real-time feature computation and caching
- Feature importance tracking and ranking
- Automated feature discovery and generation
"""

import asyncio
import numpy as np
import pandas as pd
try:
    import talib
except ImportError:
    print("WARNING: TA-Lib not installed. Using fallback implementations.")
    talib = None
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
import time
import logging
from datetime import datetime, timedelta
from sklearn.feature_selection import mutual_info_regression, SelectKBest, RFE
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.decomposition import PCA, FastICA
from sklearn.ensemble import RandomForestRegressor
import scipy.stats as stats
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class FeaturePipelineConfig:
    """Configuration for feature engineering pipeline"""
    max_features: int = 200
    feature_selection_method: str = 'mutual_info'  # 'mutual_info', 'rfe', 'importance'
    selection_threshold: float = 0.01
    cache_size: int = 10000
    update_frequency: int = 100  # Update feature importance every N samples
    enable_alternative_data: bool = True
    enable_dynamic_selection: bool = True
    scaling_method: str = 'robust'  # 'standard', 'robust', 'minmax'

class TechnicalIndicatorEngine:
    """Comprehensive technical indicator computation engine"""
    
    def __init__(self, max_history: int = 5000):
        self.max_history = max_history
        self.price_data = {
            'open': deque(maxlen=max_history),
            'high': deque(maxlen=max_history),
            'low': deque(maxlen=max_history),
            'close': deque(maxlen=max_history),
            'volume': deque(maxlen=max_history)
        }
        
        # Indicator cache
        self.indicator_cache = {}
        self.cache_timestamps = {}
        
    def update_data(self, ohlcv: Dict[str, float], timestamp: float):
        """Update OHLCV data"""
        for key, value in ohlcv.items():
            if key in self.price_data:
                self.price_data[key].append(value)
        
        # Clear cache for recalculation
        self.indicator_cache.clear()
        self.cache_timestamps.clear()
    
    def get_all_indicators(self) -> Dict[str, float]:
        """Calculate all available technical indicators"""
        if len(self.price_data['close']) < 50:
            return {}
        
        indicators = {}
        
        # Convert to numpy arrays
        open_prices = np.array(list(self.price_data['open']))
        high_prices = np.array(list(self.price_data['high']))
        low_prices = np.array(list(self.price_data['low']))
        close_prices = np.array(list(self.price_data['close']))
        volumes = np.array(list(self.price_data['volume']))
        
        # Moving Averages
        indicators.update(self._calculate_moving_averages(close_prices))
        
        # Momentum Indicators
        indicators.update(self._calculate_momentum_indicators(high_prices, low_prices, close_prices))
        
        # Volatility Indicators
        indicators.update(self._calculate_volatility_indicators(high_prices, low_prices, close_prices))
        
        # Volume Indicators
        indicators.update(self._calculate_volume_indicators(high_prices, low_prices, close_prices, volumes))
        
        # Price Pattern Indicators
        indicators.update(self._calculate_pattern_indicators(open_prices, high_prices, low_prices, close_prices))
        
        # Statistical Indicators
        indicators.update(self._calculate_statistical_indicators(close_prices))
        
        # Custom Indicators
        indicators.update(self._calculate_custom_indicators(high_prices, low_prices, close_prices, volumes))
        
        return indicators
    
    def _calculate_moving_averages(self, close_prices: np.ndarray) -> Dict[str, float]:
        """Calculate various moving averages"""
        indicators = {}
        periods = [5, 10, 20, 50, 100, 200]
        
        for period in periods:
            if len(close_prices) >= period:
                # Simple Moving Average
                sma = talib.SMA(close_prices, timeperiod=period)
                indicators[f'sma_{period}'] = sma[-1] if not np.isnan(sma[-1]) else 0.0
                
                # Exponential Moving Average
                ema = talib.EMA(close_prices, timeperiod=period)
                indicators[f'ema_{period}'] = ema[-1] if not np.isnan(ema[-1]) else 0.0
                
                # Weighted Moving Average
                wma = talib.WMA(close_prices, timeperiod=period)
                indicators[f'wma_{period}'] = wma[-1] if not np.isnan(wma[-1]) else 0.0
                
                # Double Exponential Moving Average
                dema = talib.DEMA(close_prices, timeperiod=period)
                indicators[f'dema_{period}'] = dema[-1] if not np.isnan(dema[-1]) else 0.0
                
                # Triple Exponential Moving Average
                tema = talib.TEMA(close_prices, timeperiod=period)
                indicators[f'tema_{period}'] = tema[-1] if not np.isnan(tema[-1]) else 0.0
                
                # Triangular Moving Average
                trima = talib.TRIMA(close_prices, timeperiod=period)
                indicators[f'trima_{period}'] = trima[-1] if not np.isnan(trima[-1]) else 0.0
        
        return indicators
    
    def _calculate_momentum_indicators(self, high_prices: np.ndarray, low_prices: np.ndarray, 
                                     close_prices: np.ndarray) -> Dict[str, float]:
        """Calculate momentum indicators"""
        indicators = {}
        
        # RSI
        for period in [7, 14, 21]:
            rsi = talib.RSI(close_prices, timeperiod=period)
            indicators[f'rsi_{period}'] = rsi[-1] if not np.isnan(rsi[-1]) else 50.0
        
        # MACD
        macd, macd_signal, macd_hist = talib.MACD(close_prices)
        indicators['macd'] = macd[-1] if not np.isnan(macd[-1]) else 0.0
        indicators['macd_signal'] = macd_signal[-1] if not np.isnan(macd_signal[-1]) else 0.0
        indicators['macd_histogram'] = macd_hist[-1] if not np.isnan(macd_hist[-1]) else 0.0
        
        # Stochastic
        slowk, slowd = talib.STOCH(high_prices, low_prices, close_prices)
        indicators['stoch_k'] = slowk[-1] if not np.isnan(slowk[-1]) else 50.0
        indicators['stoch_d'] = slowd[-1] if not np.isnan(slowd[-1]) else 50.0
        
        # Williams %R
        willr = talib.WILLR(high_prices, low_prices, close_prices)
        indicators['williams_r'] = willr[-1] if not np.isnan(willr[-1]) else -50.0
        
        # Rate of Change
        for period in [5, 10, 20]:
            roc = talib.ROC(close_prices, timeperiod=period)
            indicators[f'roc_{period}'] = roc[-1] if not np.isnan(roc[-1]) else 0.0
        
        # Momentum
        for period in [5, 10, 20]:
            mom = talib.MOM(close_prices, timeperiod=period)
            indicators[f'momentum_{period}'] = mom[-1] if not np.isnan(mom[-1]) else 0.0
        
        # Commodity Channel Index
        cci = talib.CCI(high_prices, low_prices, close_prices)
        indicators['cci'] = cci[-1] if not np.isnan(cci[-1]) else 0.0
        
        # Relative Strength Index variations
        rsi_sma = talib.RSI(talib.SMA(close_prices, timeperiod=5), timeperiod=14)
        indicators['rsi_sma'] = rsi_sma[-1] if not np.isnan(rsi_sma[-1]) else 50.0
        
        return indicators
    
    def _calculate_volatility_indicators(self, high_prices: np.ndarray, low_prices: np.ndarray, 
                                       close_prices: np.ndarray) -> Dict[str, float]:
        """Calculate volatility indicators"""
        indicators = {}
        
        # Average True Range
        for period in [7, 14, 21]:
            atr = talib.ATR(high_prices, low_prices, close_prices, timeperiod=period)
            indicators[f'atr_{period}'] = atr[-1] if not np.isnan(atr[-1]) else 0.0
        
        # Bollinger Bands
        for period in [10, 20, 50]:
            upper, middle, lower = talib.BBANDS(close_prices, timeperiod=period)
            indicators[f'bb_upper_{period}'] = upper[-1] if not np.isnan(upper[-1]) else close_prices[-1]
            indicators[f'bb_middle_{period}'] = middle[-1] if not np.isnan(middle[-1]) else close_prices[-1]
            indicators[f'bb_lower_{period}'] = lower[-1] if not np.isnan(lower[-1]) else close_prices[-1]
            
            # Bollinger Band Width
            bb_width = (upper[-1] - lower[-1]) / middle[-1] if not np.isnan(upper[-1]) and middle[-1] != 0 else 0.0
            indicators[f'bb_width_{period}'] = bb_width
            
            # Bollinger Band Position
            bb_position = (close_prices[-1] - lower[-1]) / (upper[-1] - lower[-1]) if (upper[-1] - lower[-1]) != 0 else 0.5
            indicators[f'bb_position_{period}'] = bb_position
        
        # Donchian Channels
        for period in [10, 20, 50]:
            if len(close_prices) >= period:
                dc_upper = np.max(high_prices[-period:])
                dc_lower = np.min(low_prices[-period:])
                dc_middle = (dc_upper + dc_lower) / 2
                
                indicators[f'dc_upper_{period}'] = dc_upper
                indicators[f'dc_lower_{period}'] = dc_lower
                indicators[f'dc_middle_{period}'] = dc_middle
                indicators[f'dc_position_{period}'] = (close_prices[-1] - dc_lower) / (dc_upper - dc_lower) if (dc_upper - dc_lower) != 0 else 0.5
        
        # Keltner Channels
        for period in [10, 20]:
            kc_middle = talib.EMA(close_prices, timeperiod=period)
            atr_kc = talib.ATR(high_prices, low_prices, close_prices, timeperiod=period)
            kc_upper = kc_middle + 2 * atr_kc
            kc_lower = kc_middle - 2 * atr_kc
            
            indicators[f'kc_upper_{period}'] = kc_upper[-1] if not np.isnan(kc_upper[-1]) else close_prices[-1]
            indicators[f'kc_lower_{period}'] = kc_lower[-1] if not np.isnan(kc_lower[-1]) else close_prices[-1]
        
        return indicators
    
    def _calculate_volume_indicators(self, high_prices: np.ndarray, low_prices: np.ndarray, 
                                   close_prices: np.ndarray, volumes: np.ndarray) -> Dict[str, float]:
        """Calculate volume indicators"""
        indicators = {}
        
        # On Balance Volume
        obv = talib.OBV(close_prices, volumes)
        indicators['obv'] = obv[-1] if not np.isnan(obv[-1]) else 0.0
        
        # Volume Rate of Change
        for period in [5, 10, 20]:
            vol_roc = talib.ROC(volumes, timeperiod=period)
            indicators[f'volume_roc_{period}'] = vol_roc[-1] if not np.isnan(vol_roc[-1]) else 0.0
        
        # Accumulation/Distribution Line
        ad = talib.AD(high_prices, low_prices, close_prices, volumes)
        indicators['ad_line'] = ad[-1] if not np.isnan(ad[-1]) else 0.0
        
        # Chaikin Money Flow
        for period in [10, 20]:
            cmf = talib.ADOSC(high_prices, low_prices, close_prices, volumes, fastperiod=3, slowperiod=period)
            indicators[f'cmf_{period}'] = cmf[-1] if not np.isnan(cmf[-1]) else 0.0
        
        # Volume Weighted Average Price (approximation)
        if len(close_prices) >= 20:
            vwap = np.sum(close_prices[-20:] * volumes[-20:]) / np.sum(volumes[-20:]) if np.sum(volumes[-20:]) > 0 else close_prices[-1]
            indicators['vwap_20'] = vwap
        
        # Volume Moving Averages
        for period in [5, 10, 20]:
            vol_sma = talib.SMA(volumes, timeperiod=period)
            indicators[f'volume_sma_{period}'] = vol_sma[-1] if not np.isnan(vol_sma[-1]) else volumes[-1]
            
            # Volume ratio
            vol_ratio = volumes[-1] / vol_sma[-1] if not np.isnan(vol_sma[-1]) and vol_sma[-1] > 0 else 1.0
            indicators[f'volume_ratio_{period}'] = vol_ratio
        
        return indicators
    
    def _calculate_pattern_indicators(self, open_prices: np.ndarray, high_prices: np.ndarray, 
                                    low_prices: np.ndarray, close_prices: np.ndarray) -> Dict[str, float]:
        """Calculate candlestick pattern indicators"""
        indicators = {}
        
        # Candlestick patterns
        patterns = [
            ('doji', talib.CDLDOJI),
            ('hammer', talib.CDLHAMMER),
            ('hanging_man', talib.CDLHANGINGMAN),
            ('shooting_star', talib.CDLSHOOTINGSTAR),
            ('engulfing', talib.CDLENGULFING),
            ('harami', talib.CDLHARAMI),
            ('dark_cloud', talib.CDLDARKCLOUDCOVER),
            ('piercing', talib.CDLPIERCING),
            ('morning_star', talib.CDLMORNINGSTAR),
            ('evening_star', talib.CDLEVENINGSTAR)
        ]
        
        for pattern_name, pattern_func in patterns:
            try:
                pattern_result = pattern_func(open_prices, high_prices, low_prices, close_prices)
                indicators[f'pattern_{pattern_name}'] = float(pattern_result[-1]) if not np.isnan(pattern_result[-1]) else 0.0
            except:
                indicators[f'pattern_{pattern_name}'] = 0.0
        
        return indicators
    
    def _calculate_statistical_indicators(self, close_prices: np.ndarray) -> Dict[str, float]:
        """Calculate statistical indicators"""
        indicators = {}
        
        # Returns and volatility
        if len(close_prices) >= 2:
            returns = np.diff(np.log(close_prices))
            
            for period in [5, 10, 20, 50]:
                if len(returns) >= period:
                    period_returns = returns[-period:]
                    
                    # Statistical moments
                    indicators[f'return_mean_{period}'] = np.mean(period_returns)
                    indicators[f'return_std_{period}'] = np.std(period_returns)
                    indicators[f'return_skew_{period}'] = stats.skew(period_returns)
                    indicators[f'return_kurtosis_{period}'] = stats.kurtosis(period_returns)
                    
                    # Risk metrics
                    indicators[f'var_95_{period}'] = np.percentile(period_returns, 5)
                    indicators[f'var_99_{period}'] = np.percentile(period_returns, 1)
                    
                    # Sharpe ratio approximation
                    sharpe = np.mean(period_returns) / np.std(period_returns) if np.std(period_returns) > 0 else 0
                    indicators[f'sharpe_{period}'] = sharpe
        
        # Price levels
        for period in [5, 10, 20, 50]:
            if len(close_prices) >= period:
                period_prices = close_prices[-period:]
                
                # Percentile positions
                indicators[f'price_percentile_{period}'] = stats.percentileofscore(period_prices, close_prices[-1]) / 100
                
                # Z-score
                z_score = (close_prices[-1] - np.mean(period_prices)) / np.std(period_prices) if np.std(period_prices) > 0 else 0
                indicators[f'z_score_{period}'] = z_score
        
        return indicators
    
    def _calculate_custom_indicators(self, high_prices: np.ndarray, low_prices: np.ndarray, 
                                   close_prices: np.ndarray, volumes: np.ndarray) -> Dict[str, float]:
        """Calculate custom indicators"""
        indicators = {}
        
        # Price efficiency ratio
        if len(close_prices) >= 20:
            net_change = abs(close_prices[-1] - close_prices[-20])
            total_movement = np.sum(np.abs(np.diff(close_prices[-20:])))
            efficiency = net_change / total_movement if total_movement > 0 else 0
            indicators['price_efficiency_20'] = efficiency
        
        # Fractal dimension
        if len(close_prices) >= 50:
            try:
                # Simplified fractal dimension calculation
                n = len(close_prices[-50:])
                prices = close_prices[-50:]
                
                # Calculate Hurst exponent
                lags = range(2, min(20, n//2))
                tau = [np.sqrt(np.std(np.subtract(prices[lag:], prices[:-lag]))) for lag in lags]
                poly = np.polyfit(np.log(lags), np.log(tau), 1)
                hurst = poly[0] * 2.0
                
                indicators['hurst_exponent'] = hurst
                indicators['fractal_dimension'] = 2 - hurst
            except:
                indicators['hurst_exponent'] = 0.5
                indicators['fractal_dimension'] = 1.5
        
        # Market microstructure indicators
        if len(close_prices) >= 10:
            # Bid-ask spread proxy (high-low spread)
            spreads = (high_prices[-10:] - low_prices[-10:]) / close_prices[-10:]
            indicators['avg_spread_10'] = np.mean(spreads)
            
            # Price impact proxy
            price_changes = np.abs(np.diff(close_prices[-10:]))
            volume_changes = volumes[-9:]  # One less due to diff
            if len(volume_changes) > 0 and np.std(volume_changes) > 0:
                impact_corr = np.corrcoef(price_changes, volume_changes)[0, 1]
                indicators['price_impact_corr'] = impact_corr if not np.isnan(impact_corr) else 0.0
            else:
                indicators['price_impact_corr'] = 0.0
        
        return indicators

class AlternativeDataIntegrator:
    """Integrates alternative data sources"""
    
    def __init__(self):
        self.sentiment_history = deque(maxlen=1000)
        self.news_scores = deque(maxlen=100)
        self.social_metrics = deque(maxlen=500)
        
    def add_sentiment_data(self, sentiment_score: float, timestamp: float):
        """Add sentiment analysis data"""
        self.sentiment_history.append({
            'score': sentiment_score,
            'timestamp': timestamp
        })
    
    def add_news_data(self, news_score: float, relevance: float, timestamp: float):
        """Add news sentiment data"""
        self.news_scores.append({
            'score': news_score,
            'relevance': relevance,
            'timestamp': timestamp
        })
    
    def add_social_data(self, mentions: int, engagement: float, timestamp: float):
        """Add social media data"""
        self.social_metrics.append({
            'mentions': mentions,
            'engagement': engagement,
            'timestamp': timestamp
        })
    
    def get_alternative_features(self) -> Dict[str, float]:
        """Extract features from alternative data"""
        features = {}
        
        # Sentiment features
        if self.sentiment_history:
            recent_sentiment = [s['score'] for s in list(self.sentiment_history)[-20:]]
            features['sentiment_mean'] = np.mean(recent_sentiment)
            features['sentiment_std'] = np.std(recent_sentiment)
            features['sentiment_trend'] = self._calculate_trend(recent_sentiment)
        
        # News features
        if self.news_scores:
            recent_news = [n['score'] for n in list(self.news_scores)[-10:]]
            recent_relevance = [n['relevance'] for n in list(self.news_scores)[-10:]]
            
            features['news_sentiment'] = np.mean(recent_news)
            features['news_relevance'] = np.mean(recent_relevance)
            features['news_volume'] = len(recent_news)
        
        # Social media features
        if self.social_metrics:
            recent_mentions = [s['mentions'] for s in list(self.social_metrics)[-20:]]
            recent_engagement = [s['engagement'] for s in list(self.social_metrics)[-20:]]
            
            features['social_mentions'] = np.sum(recent_mentions)
            features['social_engagement'] = np.mean(recent_engagement)
            features['social_buzz'] = np.std(recent_mentions)
        
        return features
    
    def _calculate_trend(self, data: List[float]) -> float:
        """Calculate trend in data"""
        if len(data) < 3:
            return 0.0
        
        x = np.arange(len(data))
        slope, _, r_value, _, _ = stats.linregress(x, data)
        return slope * (r_value ** 2)  # Weight by R-squared

class DynamicFeatureSelector:
    """Dynamic feature selection based on performance"""
    
    def __init__(self, config: FeaturePipelineConfig):
        self.config = config
        self.feature_scores = defaultdict(deque)
        self.feature_importance = {}
        self.selected_features = set()
        self.scaler = self._create_scaler()
        
        # Selection models
        self.selector_model = RandomForestRegressor(n_estimators=50, random_state=42)
        self.update_counter = 0
        
    def _create_scaler(self):
        """Create appropriate scaler"""
        if self.config.scaling_method == 'standard':
            return StandardScaler()
        elif self.config.scaling_method == 'robust':
            return RobustScaler()
        elif self.config.scaling_method == 'minmax':
            return MinMaxScaler()
        else:
            return StandardScaler()
    
    def update_feature_scores(self, features: Dict[str, float], target: float):
        """Update feature importance scores"""
        self.update_counter += 1
        
        # Store feature values for batch processing
        for feature_name, feature_value in features.items():
            self.feature_scores[feature_name].append((feature_value, target))
        
        # Update feature selection periodically
        if self.update_counter % self.config.update_frequency == 0:
            self._update_feature_selection()
    
    def _update_feature_selection(self):
        """Update feature selection based on accumulated data"""
        if not self.feature_scores:
            return
        
        # Prepare data for feature selection
        feature_names = list(self.feature_scores.keys())
        min_samples = min(len(scores) for scores in self.feature_scores.values())
        
        if min_samples < 10:  # Need minimum samples
            return
        
        # Create feature matrix
        X = []
        y = []
        
        for i in range(min_samples):
            feature_vector = []
            target_values = []
            
            for feature_name in feature_names:
                feature_value, target = self.feature_scores[feature_name][i]
                feature_vector.append(feature_value)
                target_values.append(target)
            
            X.append(feature_vector)
            y.append(np.mean(target_values))  # Average target if multiple
        
        X = np.array(X)
        y = np.array(y)
        
        # Handle NaN values
        X = np.nan_to_num(X)
        y = np.nan_to_num(y)
        
        # Scale features
        try:
            X_scaled = self.scaler.fit_transform(X)
        except:
            X_scaled = X
        
        # Feature selection based on method
        if self.config.feature_selection_method == 'mutual_info':
            scores = mutual_info_regression(X_scaled, y)
            self.feature_importance = dict(zip(feature_names, scores))
        
        elif self.config.feature_selection_method == 'rfe':
            rfe = RFE(self.selector_model, n_features_to_select=min(self.config.max_features, len(feature_names)))
            rfe.fit(X_scaled, y)
            self.feature_importance = dict(zip(feature_names, rfe.ranking_))
        
        elif self.config.feature_selection_method == 'importance':
            self.selector_model.fit(X_scaled, y)
            importances = self.selector_model.feature_importances_
            self.feature_importance = dict(zip(feature_names, importances))
        
        # Select top features
        sorted_features = sorted(self.feature_importance.items(), key=lambda x: x[1], reverse=True)
        self.selected_features = set([name for name, score in sorted_features[:self.config.max_features] 
                                    if score > self.config.selection_threshold])
        
        logger.info(f"Updated feature selection: {len(self.selected_features)} features selected")
    
    def select_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """Select features based on current selection"""
        if not self.selected_features:
            return features
        
        return {name: value for name, value in features.items() if name in self.selected_features}
    
    def get_feature_rankings(self) -> Dict[str, float]:
        """Get current feature importance rankings"""
        return self.feature_importance.copy()

class AdvancedFeaturePipeline:
    """Main feature engineering pipeline"""
    
    def __init__(self, config: FeaturePipelineConfig):
        self.config = config
        self.technical_engine = TechnicalIndicatorEngine()
        self.alternative_data = AlternativeDataIntegrator()
        self.feature_selector = DynamicFeatureSelector(config)
        
        # Feature cache
        self.feature_cache = {}
        self.cache_timestamps = {}
        
        # Performance tracking
        self.computation_times = deque(maxlen=1000)
        self.feature_counts = deque(maxlen=1000)
        
    async def process_market_data(self, ohlcv: Dict[str, float], timestamp: float, 
                                target: Optional[float] = None) -> Dict[str, float]:
        """Process market data and generate features"""
        start_time = time.time()
        
        # Update technical indicators
        self.technical_engine.update_data(ohlcv, timestamp)
        
        # Calculate all features
        features = {}
        
        # Technical indicators
        technical_features = self.technical_engine.get_all_indicators()
        features.update(technical_features)
        
        # Alternative data features
        if self.config.enable_alternative_data:
            alt_features = self.alternative_data.get_alternative_features()
            features.update(alt_features)
        
        # Dynamic feature selection
        if self.config.enable_dynamic_selection:
            if target is not None:
                self.feature_selector.update_feature_scores(features, target)
            features = self.feature_selector.select_features(features)
        
        # Track performance
        computation_time = (time.time() - start_time) * 1000  # milliseconds
        self.computation_times.append(computation_time)
        self.feature_counts.append(len(features))
        
        return features
    
    def add_alternative_data(self, data_type: str, data: Dict[str, Any], timestamp: float):
        """Add alternative data"""
        if data_type == 'sentiment':
            self.alternative_data.add_sentiment_data(data['score'], timestamp)
        elif data_type == 'news':
            self.alternative_data.add_news_data(data['score'], data['relevance'], timestamp)
        elif data_type == 'social':
            self.alternative_data.add_social_data(data['mentions'], data['engagement'], timestamp)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get pipeline performance metrics"""
        return {
            'avg_computation_time_ms': np.mean(self.computation_times) if self.computation_times else 0,
            'p95_computation_time_ms': np.percentile(self.computation_times, 95) if self.computation_times else 0,
            'avg_feature_count': np.mean(self.feature_counts) if self.feature_counts else 0,
            'selected_features': len(self.feature_selector.selected_features),
            'total_features_computed': len(self.technical_engine.indicator_cache),
            'feature_importance_top_10': dict(list(sorted(
                self.feature_selector.get_feature_rankings().items(), 
                key=lambda x: x[1], reverse=True
            )[:10]))
        }

# Example usage
async def main():
    """Example usage of advanced feature pipeline"""
    config = FeaturePipelineConfig(
        max_features=50,
        feature_selection_method='mutual_info',
        enable_alternative_data=True,
        enable_dynamic_selection=True
    )
    
    pipeline = AdvancedFeaturePipeline(config)
    
    # Simulate market data
    base_price = 50000
    for i in range(200):
        # Generate OHLCV data
        price_change = np.random.normal(0, 0.01) * base_price
        new_price = base_price + price_change
        
        ohlcv = {
            'open': base_price,
            'high': max(base_price, new_price) + abs(np.random.normal(0, 0.005)) * base_price,
            'low': min(base_price, new_price) - abs(np.random.normal(0, 0.005)) * base_price,
            'close': new_price,
            'volume': np.random.exponential(1000)
        }
        
        # Generate target (future return)
        target = np.random.normal(0, 0.01) if i > 0 else None
        
        # Process features
        features = await pipeline.process_market_data(ohlcv, time.time(), target)
        
        # Add some alternative data
        if i % 10 == 0:
            pipeline.add_alternative_data('sentiment', {'score': np.random.normal(0, 1)}, time.time())
            pipeline.add_alternative_data('news', {'score': np.random.normal(0, 1), 'relevance': np.random.random()}, time.time())
            pipeline.add_alternative_data('social', {'mentions': np.random.poisson(100), 'engagement': np.random.random()}, time.time())
        
        base_price = new_price
        
        if i % 50 == 0:
            print(f"Step {i}: {len(features)} features generated")
    
    # Performance metrics
    metrics = pipeline.get_performance_metrics()
    print(f"\nPerformance Metrics:")
    for key, value in metrics.items():
        if key != 'feature_importance_top_10':
            print(f"  {key}: {value}")
    
    print(f"\nTop 10 Features:")
    for feature, importance in metrics['feature_importance_top_10'].items():
        print(f"  {feature}: {importance:.4f}")

if __name__ == "__main__":
    asyncio.run(main())
