#!/usr/bin/env python3
"""
Fix profit_loss column issue in database
"""

import sqlite3
import os
from pathlib import Path

def fix_database():
    """Fix the profit_loss column issue"""
    
    # Database path
    db_path = "bybit_trading_bot.db"
    
    print(f"Checking database: {db_path}")
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if trades table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='trades'")
        trades_table_exists = cursor.fetchone() is not None
        
        if not trades_table_exists:
            print("Creating trades table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    symbol VARCHAR(20) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL NOT NULL,
                    profit_loss REAL DEFAULT 0.0,
                    execution_time REAL DEFAULT 0.0,
                    order_id VARCHAR(100),
                    trade_id VARCHAR(100),
                    commission REAL DEFAULT 0.0,
                    strategy VARCHAR(50),
                    confidence REAL DEFAULT 0.0,
                    market_conditions TEXT,
                    technical_indicators TEXT,
                    order_type VARCHAR(20) DEFAULT 'Market',
                    time_in_force VARCHAR(10) DEFAULT 'IOC',
                    reduce_only BOOLEAN DEFAULT FALSE,
                    close_on_trigger BOOLEAN DEFAULT FALSE,
                    position_idx INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("SUCCESS: Created trades table with profit_loss column")
        else:
            # Check if profit_loss column exists
            cursor.execute("PRAGMA table_info(trades)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'profit_loss' not in columns:
                print("Adding profit_loss column to trades table...")
                cursor.execute("ALTER TABLE trades ADD COLUMN profit_loss REAL DEFAULT 0.0")
                print("SUCCESS: Added profit_loss column")
            else:
                print("SUCCESS: profit_loss column already exists")
        
        # Check trading_memories table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='trading_memories'")
        memories_table_exists = cursor.fetchone() is not None
        
        if not memories_table_exists:
            print("Creating trading_memories table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trading_memories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20) NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    pattern_data TEXT NOT NULL,
                    pattern_hash VARCHAR(64) NOT NULL,
                    outcome TEXT NOT NULL,
                    success BOOLEAN NOT NULL,
                    profit_loss REAL DEFAULT 0,
                    strategy VARCHAR(50),
                    confidence REAL DEFAULT 0,
                    market_conditions TEXT,
                    technical_indicators TEXT,
                    metadata TEXT DEFAULT '{}',
                    time_decay_factor REAL DEFAULT 0.95,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("SUCCESS: Created trading_memories table")
        else:
            # Check if profit_loss column exists in trading_memories
            cursor.execute("PRAGMA table_info(trading_memories)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'profit_loss' not in columns:
                print("Adding profit_loss column to trading_memories table...")
                cursor.execute("ALTER TABLE trading_memories ADD COLUMN profit_loss REAL DEFAULT 0.0")
                print("SUCCESS: Added profit_loss column to trading_memories")
            else:
                print("SUCCESS: profit_loss column already exists in trading_memories")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("SUCCESS: Database profit_loss column issue fixed!")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to fix database: {e}")
        return False

if __name__ == "__main__":
    fix_database()
