#!/usr/bin/env python3
"""
Multi-Agent Reinforcement Learning System for Trading
Implements cutting-edge multi-agent deep Q-learning with different time horizons

Features:
- Multi-agent DQN with different time horizons (1min, 5min, 1h, 4h, daily)
- Hierarchical coordination between agents
- Experience replay with prioritized sampling
- Double DQN with dueling networks
- Multi-objective optimization (profit, risk, drawdown)
- Adaptive exploration strategies
- Meta-learning for strategy adaptation
"""

import asyncio
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, NamedTuple
from dataclasses import dataclass, field
from collections import deque, namedtuple
import random
import logging
import time
from datetime import datetime, timedelta
import pickle
import json

logger = logging.getLogger(__name__)

# Experience tuple for replay buffer
Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done', 'priority'])

@dataclass
class AgentConfig:
    """Configuration for individual RL agent"""
    name: str
    timeframe: str  # '1m', '5m', '1h', '4h', '1d'
    state_size: int = 128
    action_size: int = 3  # 0=hold, 1=buy, 2=sell
    hidden_size: int = 256
    learning_rate: float = 0.001
    gamma: float = 0.99  # Discount factor
    epsilon_start: float = 1.0
    epsilon_end: float = 0.01
    epsilon_decay: float = 0.995
    memory_size: int = 100000
    batch_size: int = 64
    target_update_freq: int = 1000
    priority_alpha: float = 0.6
    priority_beta: float = 0.4
    
@dataclass
class MultiAgentConfig:
    """Configuration for multi-agent system"""
    coordination_method: str = "hierarchical"  # "hierarchical", "consensus", "competitive"
    meta_learning_rate: float = 0.0001
    coordination_weight: float = 0.3
    profit_weight: float = 0.4
    risk_weight: float = 0.3
    max_position_size: float = 1.0
    risk_free_rate: float = 0.02

class DuelingDQN(nn.Module):
    """Dueling Deep Q-Network architecture"""
    
    def __init__(self, state_size: int, action_size: int, hidden_size: int = 256):
        super(DuelingDQN, self).__init__()
        
        # Shared feature layers
        self.feature_layers = nn.Sequential(
            nn.Linear(state_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU()
        )
        
        # Value stream
        self.value_stream = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1)
        )
        
        # Advantage stream
        self.advantage_stream = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, action_size)
        )
        
    def forward(self, state):
        """Forward pass through dueling network"""
        features = self.feature_layers(state)
        
        value = self.value_stream(features)
        advantage = self.advantage_stream(features)
        
        # Combine value and advantage streams
        q_values = value + (advantage - advantage.mean(dim=1, keepdim=True))
        
        return q_values

class PrioritizedReplayBuffer:
    """Prioritized Experience Replay Buffer"""
    
    def __init__(self, capacity: int, alpha: float = 0.6):
        self.capacity = capacity
        self.alpha = alpha
        self.buffer = []
        self.priorities = np.zeros((capacity,), dtype=np.float32)
        self.position = 0
        self.max_priority = 1.0
        
    def add(self, state, action, reward, next_state, done):
        """Add experience to buffer with maximum priority"""
        if len(self.buffer) < self.capacity:
            self.buffer.append(None)
        
        self.buffer[self.position] = Experience(state, action, reward, next_state, done, self.max_priority)
        self.priorities[self.position] = self.max_priority
        
        self.position = (self.position + 1) % self.capacity
        
    def sample(self, batch_size: int, beta: float = 0.4):
        """Sample batch with prioritized sampling"""
        if len(self.buffer) < batch_size:
            return None, None, None
        
        # Calculate sampling probabilities
        priorities = self.priorities[:len(self.buffer)]
        probabilities = priorities ** self.alpha
        probabilities /= probabilities.sum()
        
        # Sample indices
        indices = np.random.choice(len(self.buffer), batch_size, p=probabilities)
        
        # Calculate importance sampling weights
        weights = (len(self.buffer) * probabilities[indices]) ** (-beta)
        weights /= weights.max()
        
        # Get experiences
        experiences = [self.buffer[idx] for idx in indices]
        
        return experiences, indices, weights
    
    def update_priorities(self, indices, priorities):
        """Update priorities for sampled experiences"""
        for idx, priority in zip(indices, priorities):
            self.priorities[idx] = priority
            self.max_priority = max(self.max_priority, priority)

class TradingAgent:
    """Individual trading agent with specific timeframe"""
    
    def __init__(self, config: AgentConfig, device: torch.device):
        self.config = config
        self.device = device
        
        # Neural networks
        self.q_network = DuelingDQN(config.state_size, config.action_size, config.hidden_size).to(device)
        self.target_network = DuelingDQN(config.state_size, config.action_size, config.hidden_size).to(device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=config.learning_rate)
        
        # Experience replay
        self.memory = PrioritizedReplayBuffer(config.memory_size, config.priority_alpha)
        
        # Training state
        self.epsilon = config.epsilon_start
        self.steps = 0
        self.training_losses = deque(maxlen=1000)
        
        # Performance tracking
        self.episode_rewards = deque(maxlen=100)
        self.episode_lengths = deque(maxlen=100)
        self.win_rate = 0.0
        
        # Copy weights to target network
        self.update_target_network()
        
    def get_action(self, state: np.ndarray, training: bool = True) -> int:
        """Select action using epsilon-greedy policy"""
        if training and random.random() < self.epsilon:
            return random.randint(0, self.config.action_size - 1)
        
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            q_values = self.q_network(state_tensor)
            return q_values.argmax().item()
    
    def store_experience(self, state, action, reward, next_state, done):
        """Store experience in replay buffer"""
        self.memory.add(state, action, reward, next_state, done)
    
    def train(self) -> Optional[float]:
        """Train the agent using prioritized experience replay"""
        if len(self.memory.buffer) < self.config.batch_size:
            return None
        
        # Sample batch
        experiences, indices, weights = self.memory.sample(self.config.batch_size, self.config.priority_beta)
        if experiences is None:
            return None
        
        # Prepare batch tensors
        states = torch.FloatTensor([e.state for e in experiences]).to(self.device)
        actions = torch.LongTensor([e.action for e in experiences]).to(self.device)
        rewards = torch.FloatTensor([e.reward for e in experiences]).to(self.device)
        next_states = torch.FloatTensor([e.next_state for e in experiences]).to(self.device)
        dones = torch.BoolTensor([e.done for e in experiences]).to(self.device)
        weights_tensor = torch.FloatTensor(weights).to(self.device)
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Double DQN: use main network to select actions, target network to evaluate
        with torch.no_grad():
            next_actions = self.q_network(next_states).argmax(1)
            next_q_values = self.target_network(next_states).gather(1, next_actions.unsqueeze(1))
            target_q_values = rewards.unsqueeze(1) + (self.config.gamma * next_q_values * ~dones.unsqueeze(1))
        
        # Calculate loss with importance sampling weights
        td_errors = target_q_values - current_q_values
        loss = (weights_tensor.unsqueeze(1) * td_errors.pow(2)).mean()
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        self.optimizer.step()
        
        # Update priorities
        priorities = td_errors.abs().detach().cpu().numpy().flatten() + 1e-6
        self.memory.update_priorities(indices, priorities)
        
        # Update epsilon
        self.epsilon = max(self.config.epsilon_end, self.epsilon * self.config.epsilon_decay)
        
        # Update target network
        self.steps += 1
        if self.steps % self.config.target_update_freq == 0:
            self.update_target_network()
        
        self.training_losses.append(loss.item())
        return loss.item()
    
    def update_target_network(self):
        """Copy weights from main network to target network"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get agent performance metrics"""
        return {
            'avg_reward': np.mean(self.episode_rewards) if self.episode_rewards else 0.0,
            'avg_episode_length': np.mean(self.episode_lengths) if self.episode_lengths else 0.0,
            'win_rate': self.win_rate,
            'epsilon': self.epsilon,
            'avg_loss': np.mean(self.training_losses) if self.training_losses else 0.0,
            'steps': self.steps
        }

class HierarchicalCoordinator:
    """Hierarchical coordinator for multi-agent system"""
    
    def __init__(self, agents: Dict[str, TradingAgent], config: MultiAgentConfig):
        self.agents = agents
        self.config = config
        self.timeframe_hierarchy = ['1m', '5m', '15m', '1h', '4h', '1d']
        
        # Meta-learning components
        self.coordination_weights = {tf: 1.0 for tf in self.timeframe_hierarchy}
        self.performance_history = {tf: deque(maxlen=100) for tf in self.timeframe_hierarchy}
        
    def coordinate_actions(self, states: Dict[str, np.ndarray], market_context: Dict[str, Any]) -> Dict[str, int]:
        """Coordinate actions across all agents"""
        individual_actions = {}
        q_values = {}
        
        # Get individual agent actions and Q-values
        for timeframe, agent in self.agents.items():
            if timeframe in states:
                action = agent.get_action(states[timeframe], training=True)
                individual_actions[timeframe] = action
                
                # Get Q-values for coordination
                with torch.no_grad():
                    state_tensor = torch.FloatTensor(states[timeframe]).unsqueeze(0).to(agent.device)
                    q_vals = agent.q_network(state_tensor).cpu().numpy()[0]
                    q_values[timeframe] = q_vals
        
        # Apply hierarchical coordination
        coordinated_actions = self._apply_hierarchical_coordination(
            individual_actions, q_values, market_context
        )
        
        return coordinated_actions
    
    def _apply_hierarchical_coordination(self, actions: Dict[str, int], q_values: Dict[str, np.ndarray], 
                                       market_context: Dict[str, Any]) -> Dict[str, int]:
        """Apply hierarchical coordination logic"""
        coordinated_actions = actions.copy()
        
        # Weight actions by timeframe importance and recent performance
        action_scores = {}
        for timeframe in actions:
            if timeframe in q_values:
                # Base score from Q-values
                base_score = np.max(q_values[timeframe])
                
                # Weight by coordination weight
                coord_weight = self.coordination_weights.get(timeframe, 1.0)
                
                # Weight by recent performance
                recent_perf = np.mean(self.performance_history[timeframe]) if self.performance_history[timeframe] else 0.0
                
                action_scores[timeframe] = base_score * coord_weight * (1.0 + recent_perf)
        
        # Find dominant timeframe
        if action_scores:
            dominant_timeframe = max(action_scores, key=action_scores.get)
            dominant_action = actions[dominant_timeframe]
            
            # Apply coordination rules
            for timeframe in coordinated_actions:
                if timeframe != dominant_timeframe:
                    # Shorter timeframes should align with longer timeframes
                    tf_index = self.timeframe_hierarchy.index(timeframe) if timeframe in self.timeframe_hierarchy else 0
                    dom_index = self.timeframe_hierarchy.index(dominant_timeframe) if dominant_timeframe in self.timeframe_hierarchy else 0
                    
                    if tf_index < dom_index:  # Shorter timeframe
                        # Apply coordination weight
                        if random.random() < self.config.coordination_weight:
                            coordinated_actions[timeframe] = dominant_action
        
        return coordinated_actions
    
    def update_performance(self, timeframe: str, reward: float):
        """Update performance history for coordination weight adjustment"""
        self.performance_history[timeframe].append(reward)
        
        # Adaptive coordination weight adjustment
        if len(self.performance_history[timeframe]) >= 10:
            recent_performance = np.mean(list(self.performance_history[timeframe])[-10:])
            self.coordination_weights[timeframe] = max(0.1, min(2.0, 1.0 + recent_performance))

class MultiAgentTradingSystem:
    """Main multi-agent trading system"""

    def __init__(self, config: MultiAgentConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Initialize agents for different timeframes
        self.agents = {}
        timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']

        for tf in timeframes:
            agent_config = AgentConfig(
                name=f"agent_{tf}",
                timeframe=tf,
                state_size=128,  # Will be adjusted based on feature size
                action_size=3,
                hidden_size=256,
                learning_rate=0.001 * (0.8 ** timeframes.index(tf))  # Slower learning for longer timeframes
            )
            self.agents[tf] = TradingAgent(agent_config, self.device)

        # Initialize coordinator
        self.coordinator = HierarchicalCoordinator(self.agents, config)

        # System state
        self.episode_count = 0
        self.total_steps = 0
        self.system_performance = {
            'total_reward': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0
        }

        # Advanced features
        self.meta_optimizer = optim.Adam([
            torch.tensor(list(self.coordinator.coordination_weights.values()), requires_grad=True)
        ], lr=config.meta_learning_rate)

        self.ensemble_predictions = deque(maxlen=1000)
        self.market_regime_detector = MarketRegimeDetector()

        logger.info(f"Multi-agent system initialized with {len(self.agents)} agents on {self.device}")

    async def process_market_data(self, market_data: Dict[str, Any]) -> Dict[str, int]:
        """Process market data and generate coordinated actions"""

        # Detect market regime
        regime = self.market_regime_detector.detect_regime(market_data)
        market_data['regime'] = regime

        # Extract states for each timeframe
        states = {}
        for timeframe in self.agents:
            if f"features_{timeframe}" in market_data:
                states[timeframe] = market_data[f"features_{timeframe}"]

        # Get coordinated actions
        actions = self.coordinator.coordinate_actions(states, market_data)

        # Store ensemble prediction
        self.ensemble_predictions.append({
            'timestamp': time.time(),
            'actions': actions,
            'regime': regime,
            'confidence': self._calculate_ensemble_confidence(states)
        })

        return actions

    def _calculate_ensemble_confidence(self, states: Dict[str, np.ndarray]) -> float:
        """Calculate confidence in ensemble prediction"""
        if not states:
            return 0.0

        q_value_variances = []
        for timeframe, state in states.items():
            if timeframe in self.agents:
                with torch.no_grad():
                    state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
                    q_values = self.agents[timeframe].q_network(state_tensor).cpu().numpy()[0]
                    variance = np.var(q_values)
                    q_value_variances.append(variance)

        # Higher variance = lower confidence
        avg_variance = np.mean(q_value_variances) if q_value_variances else 1.0
        confidence = 1.0 / (1.0 + avg_variance)
        return confidence

class MarketRegimeDetector:
    """Detect market regimes for adaptive strategy selection"""

    def __init__(self, lookback_period: int = 100):
        self.lookback_period = lookback_period
        self.price_history = deque(maxlen=lookback_period)
        self.volume_history = deque(maxlen=lookback_period)
        self.volatility_history = deque(maxlen=lookback_period)

    def detect_regime(self, market_data: Dict[str, Any]) -> str:
        """Detect current market regime"""
        if 'price' in market_data:
            self.price_history.append(market_data['price'])
        if 'volume' in market_data:
            self.volume_history.append(market_data['volume'])

        if len(self.price_history) < 20:
            return 'unknown'

        # Calculate volatility
        prices = np.array(list(self.price_history))
        returns = np.diff(np.log(prices))
        volatility = np.std(returns) * np.sqrt(252)  # Annualized
        self.volatility_history.append(volatility)

        # Regime detection logic
        if len(self.volatility_history) >= 10:
            recent_vol = np.mean(list(self.volatility_history)[-10:])
            historical_vol = np.mean(list(self.volatility_history)[:-10]) if len(self.volatility_history) > 10 else recent_vol

            # Trend detection
            trend_strength = self._calculate_trend_strength(prices)

            # Volume analysis
            volume_trend = self._analyze_volume_trend()

            # Classify regime
            if recent_vol > historical_vol * 1.5:
                return 'high_volatility'
            elif trend_strength > 0.7:
                return 'trending'
            elif trend_strength < -0.7:
                return 'trending_down'
            elif volume_trend == 'increasing':
                return 'accumulation'
            elif volume_trend == 'decreasing':
                return 'distribution'
            else:
                return 'sideways'

        return 'unknown'

    def _calculate_trend_strength(self, prices: np.ndarray) -> float:
        """Calculate trend strength using linear regression"""
        if len(prices) < 10:
            return 0.0

        x = np.arange(len(prices))
        slope, _, r_value, _, _ = stats.linregress(x, prices)

        # Normalize slope by price level
        normalized_slope = slope / np.mean(prices)

        # Weight by R-squared (trend consistency)
        trend_strength = normalized_slope * (r_value ** 2)

        return np.clip(trend_strength * 100, -1.0, 1.0)

    def _analyze_volume_trend(self) -> str:
        """Analyze volume trend"""
        if len(self.volume_history) < 10:
            return 'unknown'

        volumes = np.array(list(self.volume_history))
        recent_volume = np.mean(volumes[-5:])
        historical_volume = np.mean(volumes[:-5])

        if recent_volume > historical_volume * 1.2:
            return 'increasing'
        elif recent_volume < historical_volume * 0.8:
            return 'decreasing'
        else:
            return 'stable'
    
    def train_agents(self, experiences: Dict[str, List[Tuple]]) -> Dict[str, float]:
        """Train all agents with their respective experiences"""
        training_losses = {}
        
        for timeframe, agent in self.agents.items():
            if timeframe in experiences:
                # Store experiences
                for state, action, reward, next_state, done in experiences[timeframe]:
                    agent.store_experience(state, action, reward, next_state, done)
                
                # Train agent
                loss = agent.train()
                if loss is not None:
                    training_losses[timeframe] = loss
                
                # Update coordinator performance
                if experiences[timeframe]:
                    avg_reward = np.mean([exp[2] for exp in experiences[timeframe]])
                    self.coordinator.update_performance(timeframe, avg_reward)
        
        return training_losses
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system performance metrics"""
        agent_metrics = {}
        for timeframe, agent in self.agents.items():
            agent_metrics[timeframe] = agent.get_performance_metrics()
        
        # Calculate system-wide metrics
        avg_rewards = [metrics['avg_reward'] for metrics in agent_metrics.values()]
        system_avg_reward = np.mean(avg_rewards) if avg_rewards else 0.0
        
        return {
            'episode_count': self.episode_count,
            'total_steps': self.total_steps,
            'system_avg_reward': system_avg_reward,
            'agent_metrics': agent_metrics,
            'coordination_weights': self.coordinator.coordination_weights,
            'device': str(self.device)
        }
    
    def save_models(self, filepath: str):
        """Save all agent models"""
        models = {}
        for timeframe, agent in self.agents.items():
            models[timeframe] = {
                'q_network': agent.q_network.state_dict(),
                'target_network': agent.target_network.state_dict(),
                'optimizer': agent.optimizer.state_dict(),
                'config': agent.config
            }
        
        torch.save(models, filepath)
        logger.info(f"Models saved to {filepath}")
    
    def load_models(self, filepath: str):
        """Load all agent models"""
        try:
            models = torch.load(filepath, map_location=self.device)
            for timeframe, model_data in models.items():
                if timeframe in self.agents:
                    self.agents[timeframe].q_network.load_state_dict(model_data['q_network'])
                    self.agents[timeframe].target_network.load_state_dict(model_data['target_network'])
                    self.agents[timeframe].optimizer.load_state_dict(model_data['optimizer'])
            
            logger.info(f"Models loaded from {filepath}")
        except Exception as e:
            logger.error(f"Failed to load models: {e}")

# Example usage
async def main():
    """Example usage of multi-agent trading system"""
    config = MultiAgentConfig(
        coordination_method="hierarchical",
        meta_learning_rate=0.0001,
        coordination_weight=0.3
    )
    
    system = MultiAgentTradingSystem(config)
    
    # Simulate training
    for episode in range(100):
        # Simulate market data
        market_data = {
            f"features_{tf}": np.random.randn(128) for tf in ['1m', '5m', '1h']
        }
        
        # Get actions
        actions = await system.process_market_data(market_data)
        print(f"Episode {episode}: Actions = {actions}")
        
        # Simulate experiences (normally from environment)
        experiences = {}
        for tf in actions:
            experiences[tf] = [
                (np.random.randn(128), actions[tf], np.random.randn(), np.random.randn(128), False)
            ]
        
        # Train agents
        losses = system.train_agents(experiences)
        
        if episode % 10 == 0:
            metrics = system.get_system_metrics()
            print(f"System metrics: {metrics}")

if __name__ == "__main__":
    asyncio.run(main())
