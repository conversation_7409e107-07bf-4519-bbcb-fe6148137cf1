#!/usr/bin/env python3
"""
Final System Validation Script
Test all components to ensure 100% functionality
"""

import asyncio
import sys
import traceback
from datetime import datetime, timezone
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_core_imports():
    """Test all core imports"""
    print("TESTING: Core imports...")
    
    try:
        from bybit_bot.core.config import EnhancedBotConfig
        print("SUCCESS: EnhancedBotConfig import")
        
        from bybit_bot.core.database_manager import DatabaseManager
        print("SUCCESS: DatabaseManager import")
        
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        print("SUCCESS: EnhancedBybitClient import")
        
        from bybit_bot.main import BybitTradingBotSystem
        print("SUCCESS: BybitTradingBotSystem import")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Core imports failed: {e}")
        traceback.print_exc()
        return False

async def test_config_loading():
    """Test configuration loading"""
    print("\nTESTING: Configuration loading...")
    
    try:
        from bybit_bot.core.config import EnhancedBotConfig
        config = EnhancedBotConfig()
        
        print(f"SUCCESS: Config loaded - API keys available: {bool(config.api_keys)}")
        print(f"SUCCESS: Bybit API key exists: {bool(getattr(config.api_keys, 'bybit', {}).get('api_key'))}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Config loading failed: {e}")
        traceback.print_exc()
        return False

async def test_database_connection():
    """Test database connection"""
    print("\nTESTING: Database connection...")
    
    try:
        from bybit_bot.core.config import EnhancedBotConfig
        from bybit_bot.core.database_manager import DatabaseManager
        
        config = EnhancedBotConfig()
        db = DatabaseManager(config)
        await db.initialize()
        
        # Test simple query
        result = await db.execute("SELECT 1 as test")
        print("SUCCESS: Database connection and query")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"ERROR: Database connection failed: {e}")
        traceback.print_exc()
        return False

async def test_bybit_client():
    """Test Bybit client initialization"""
    print("\nTESTING: Bybit client...")
    
    try:
        from bybit_bot.core.config import EnhancedBotConfig
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        
        config = EnhancedBotConfig()
        client = EnhancedBybitClient(config)
        
        # Test initialization
        await client.initialize()
        print("SUCCESS: Bybit client initialized")
        
        # Test basic API call
        try:
            ticker = await client.get_ticker("ETHUSDT")
            print(f"SUCCESS: API call - ETHUSDT ticker: {bool(ticker)}")
        except Exception as api_e:
            print(f"WARNING: API call failed (expected with testnet): {api_e}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"ERROR: Bybit client failed: {e}")
        traceback.print_exc()
        return False

async def test_main_system():
    """Test main system initialization"""
    print("\nTESTING: Main system initialization...")
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        
        # Create system instance
        system = BybitTradingBotSystem()
        print("SUCCESS: Main system instance created")
        
        # Test initialization (without running)
        await system.initialize_components()
        print("SUCCESS: System components initialized")
        
        # Test shutdown
        await system.shutdown()
        print("SUCCESS: System shutdown completed")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Main system failed: {e}")
        traceback.print_exc()
        return False

async def test_mpc_components():
    """Test MPC components"""
    print("\nTESTING: MPC components...")
    
    try:
        from bybit_bot.mcp.mpc_security_layer import MPCSecurityLayer
        from bybit_bot.mcp.threshold_signatures import ThresholdSignatureManager
        
        # Test MPC Security Layer
        security_layer = MPCSecurityLayer(party_id=1)
        await security_layer.initialize()
        print("SUCCESS: MPC Security Layer initialized")
        
        # Test Threshold Signatures
        threshold_manager = ThresholdSignatureManager(threshold=2, total_parties=3, party_id=1)
        print("SUCCESS: Threshold Signature Manager created")
        
        return True
        
    except Exception as e:
        print(f"ERROR: MPC components failed: {e}")
        traceback.print_exc()
        return False

async def test_ai_components():
    """Test AI components"""
    print("\nTESTING: AI components...")
    
    try:
        from bybit_bot.ai.meta_learner import MetaLearner
        from bybit_bot.ai.supergpt_integration import SuperGPTIntegration
        
        # Test Meta Learner
        meta_learner = MetaLearner()
        print("SUCCESS: Meta Learner created")
        
        # Test SuperGPT Integration
        supergpt = SuperGPTIntegration()
        print("SUCCESS: SuperGPT Integration created")
        
        return True
        
    except Exception as e:
        print(f"ERROR: AI components failed: {e}")
        traceback.print_exc()
        return False

async def run_comprehensive_validation():
    """Run comprehensive system validation"""
    print("=" * 80)
    print("BYBIT TRADING BOT - COMPREHENSIVE SYSTEM VALIDATION")
    print("=" * 80)
    
    tests = [
        ("Core Imports", test_core_imports),
        ("Configuration Loading", test_config_loading),
        ("Database Connection", test_database_connection),
        ("Bybit Client", test_bybit_client),
        ("Main System", test_main_system),
        ("MPC Components", test_mpc_components),
        ("AI Components", test_ai_components),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"CRITICAL ERROR in {test_name}: {e}")
            results[test_name] = False
    
    # Final report
    print("\n" + "=" * 80)
    print("VALIDATION RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nSUMMARY: {passed}/{total} tests passed")
    
    if passed == total:
        print("SUCCESS: ALL SYSTEMS OPERATIONAL - 100% FUNCTIONALITY ACHIEVED")
        print("READY FOR HIGH-PERFORMANCE TRADING")
    else:
        print("WARNING: Some systems need attention")
    
    print("=" * 80)
    
    return passed == total

if __name__ == "__main__":
    try:
        result = asyncio.run(run_comprehensive_validation())
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        traceback.print_exc()
        sys.exit(1)
