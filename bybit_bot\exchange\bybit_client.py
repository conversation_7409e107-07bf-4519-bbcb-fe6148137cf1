"""
Bybit API Client for Trading Bot
Handles all interactions with Bybit exchange API
"""

import asyncio
import hmac
import hashlib
import json
import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional, Union
from urllib.parse import urlencode

import aiohttp
from pybit.unified_trading import HTTP, WebSocket
from ..utils.global_rate_limiter import rate_limiter

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger

logger = logging.getLogger("bybit_trading_bot.bybit_client")


class BybitClient:
    """
    Comprehensive Bybit API client with real-time data and trading capabilities
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingBotLogger(config)
        
        # API endpoints - handle both config patterns
        self.testnet = getattr(config, 'bybit_testnet', False) or getattr(config.api_keys.bybit, 'testnet', False)
        if self.testnet:
            self.base_url = "https://api-testnet.bybit.com"
            self.ws_url = "wss://stream-testnet.bybit.com/v5/public/linear"
        else:
            self.base_url = "https://api.bybit.com"
            self.ws_url = "wss://stream.bybit.com/v5/public/linear"

        # API credentials - handle both config patterns
        self.api_key = getattr(config, 'bybit_api_key', '') or config.api_keys.bybit.get('api_key', '')
        self.api_secret = getattr(config, 'bybit_api_secret', '') or config.api_keys.bybit.get('api_secret', '')
        self.recv_window = getattr(config, 'bybit_recv_window', 120000)  # Increased to 120 seconds for maximum timestamp tolerance
        
        # HTTP session
        self.session: Optional[aiohttp.ClientSession] = None
        
        # WebSocket connection
        self.ws = None
        self.ws_connected = False
        
        # Data storage
        self.market_data = {}
        self.account_data = {}
        self.positions = {}
        self.orders = {}
        
        # Rate limiting
        self.rate_limits = {
            'requests_per_second': 10,
            'last_request_time': 0.0,
            'request_count': 0
        }

    async def initialize(self):
        """Initialize the Bybit client"""
        try:
            logger.info("Initializing Bybit API client...")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                connector=aiohttp.TCPConnector(limit=100)
            )
            
            # Test API connection
            await self._test_connection()
            
            # Initialize WebSocket
            await self._initialize_websocket()
            
            logger.info("Bybit API client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Bybit client: {e}")
            raise

    async def close(self):
        """Close all connections"""
        try:
            if self.ws is not None and hasattr(self.ws, 'close'):
                close_method = getattr(self.ws, 'close')
                if asyncio.iscoroutinefunction(close_method):
                    await close_method()
                else:
                    # Handle synchronous close method
                    close_method()
            if self.session:
                await self.session.close()
            logger.info("Bybit client connections closed")
        except Exception as e:
            logger.error(f"Error closing Bybit client: {e}")

    async def _test_connection(self):
        """Test API connection"""
        try:
            response = await self._make_request("GET", "/v5/market/time")
            if response.get("retCode") == 0:
                logger.info("Bybit API connection successful")
                return True
            else:
                logger.error(f"API connection failed: {response}")
                return False
        except Exception as e:
            logger.error(f"Failed to test API connection: {e}")
            raise

    async def _initialize_websocket(self):
        """Initialize WebSocket connection for real-time data"""
        try:
            # Note: Implement WebSocket connection here
            # For now, we'll use HTTP polling
            logger.info("WebSocket initialization (HTTP polling mode)")
            
        except Exception as e:
            logger.error(f"Failed to initialize WebSocket: {e}")

    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, signed: bool = False) -> Dict:
        """Make HTTP request to Bybit API"""
        try:
            # CRITICAL: Use global rate limiter before ANY API request
            await rate_limiter.acquire("api_request")
            # Ensure session is initialized - recreate if None
            if self.session is None:
                logger.warning("HTTP session was None, recreating...")
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=30),
                    connector=aiohttp.TCPConnector(limit=100, ttl_dns_cache=300, use_dns_cache=True)
                )

            # Rate limiting
            await self._rate_limit()

            url = f"{self.base_url}{endpoint}"
            headers = {
                "Content-Type": "application/json",
                "X-BAPI-API-KEY": self.api_key
            }

            if params is None:
                params = {}

            # Add timestamp for signed requests
            if signed:
                # Use local time with increased recv_window for tolerance
                # Add small buffer to account for network latency and clock drift
                current_time = time.time()
                # Subtract 5 seconds to account for potential clock drift and network latency
                adjusted_time = current_time - 5.0
                timestamp = str(int(adjusted_time * 1000))
                logger.debug(f"Using adjusted timestamp: {timestamp} (current_time: {current_time}, adjusted: {adjusted_time})")

                # Create signature BEFORE adding timestamp and recv_window to params
                signature = self._create_signature(method, params, timestamp)

                # Add signature headers (timestamp and recv_window go in headers, NOT in params)
                headers["X-BAPI-SIGN"] = signature
                headers["X-BAPI-TIMESTAMP"] = timestamp
                headers["X-BAPI-RECV-WINDOW"] = str(self.recv_window)

            # Make request
            if method == "GET":
                query_string = urlencode(params) if params else ""
                url = f"{url}?{query_string}" if query_string else url
                async with self.session.get(url, headers=headers) as response:
                    result = await response.json()
                    # Report success to rate limiter
                    rate_limiter.report_success()
                    return result
            else:
                async with self.session.request(method, url, json=params, headers=headers) as response:
                    result = await response.json()
                    # Report success to rate limiter
                    rate_limiter.report_success()
                    return result

        except Exception as e:
            error_msg = f"API request failed: {method} {endpoint} - {e}"
            logger.error(error_msg)

            # Report error to rate limiter for proper handling
            rate_limiter.report_error(str(e))
            raise





    def _create_signature(self, method: str, params: Dict, timestamp: str) -> str:
        """Create API signature for authenticated requests"""
        try:
            # Prepare the signing string according to Bybit V5 API documentation
            if method == "GET":
                # Sort parameters and create query string
                sorted_params = sorted(params.items())
                query_string = urlencode(sorted_params)
                # For GET requests: timestamp + api_key + recv_window + queryString
                signing_string = f"{timestamp}{self.api_key}{self.recv_window}{query_string}"
            else:
                # For POST requests: timestamp + api_key + recv_window + body
                # CRITICAL: JSON must match exact format that Bybit expects (with spaces after colons)
                param_str = json.dumps(params, separators=(', ', ': ')) if params else ""
                signing_string = f"{timestamp}{self.api_key}{self.recv_window}{param_str}"
            
            # Debug logging
            logger.debug(f"Signing string: {signing_string}")
            
            # Create HMAC SHA256 signature
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                signing_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return signature
            
        except Exception as e:
            logger.error(f"Failed to create signature: {e}")
            logger.error(f"Method: {method}, Params: {params}")
            logger.error(f"Timestamp: {timestamp}")
            if 'signing_string' in locals():
                logger.error(f"Signing string: {signing_string}")
            raise

    async def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        
        # Reset counter every second
        if current_time - self.rate_limits['last_request_time'] >= 1:
            self.rate_limits['request_count'] = 0
            self.rate_limits['last_request_time'] = current_time
        
        # Check if we need to wait
        if self.rate_limits['request_count'] >= self.rate_limits['requests_per_second']:
            sleep_time = 1 - (current_time - self.rate_limits['last_request_time'])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                self.rate_limits['request_count'] = 0
                self.rate_limits['last_request_time'] = time.time()
        
        self.rate_limits['request_count'] += 1

    # Market Data Methods
    async def get_symbols(self, category: str = None) -> List[Dict]:
        """Get available trading symbols"""
        try:
            # Use category from config if not specified
            if category is None:
                category = getattr(self.config.trading, 'category', 'spot')

            response = await self._make_request("GET", "/v5/market/instruments-info", {"category": category})
            if response.get("retCode") == 0:
                return response.get("result", {}).get("list", [])
            else:
                logger.error(f"Failed to get symbols: {response}")
                return []
        except Exception as e:
            logger.error(f"Failed to get symbols: {e}")
            raise

    async def get_ticker(self, symbol: str, category: str = None) -> Dict:
        """Get ticker data for a symbol"""
        try:
            # Use category from config if not specified
            if category is None:
                category = getattr(self.config.trading, 'category', 'spot')

            response = await self._make_request("GET", "/v5/market/tickers", {"category": category, "symbol": symbol})
            if response.get("retCode") == 0:
                tickers = response.get("result", {}).get("list", [])
                return tickers[0] if tickers else {}
            else:
                logger.error(f"Failed to get ticker for {symbol}: {response}")
                return {}
        except Exception as e:
            logger.error(f"Failed to get ticker for {symbol}: {e}")
            raise

    async def get_order_book(self, symbol: str, limit: int = 25, category: str = None) -> Dict:
        """Get order book data"""
        try:
            # Use category from config if not specified
            if category is None:
                category = getattr(self.config.trading, 'category', 'spot')

            response = await self._make_request("GET", "/v5/market/orderbook", {
                "category": category,
                "symbol": symbol,
                "limit": limit
            })
            if response.get("retCode") == 0:
                return response.get("result", {})
            else:
                logger.error(f"Failed to get order book for {symbol}: {response}")
                return {}
        except Exception as e:
            logger.error(f"Failed to get order book for {symbol}: {e}")
            raise

    async def get_klines(self, symbol: str, interval: str, limit: int = 200, category: str = None) -> List[Dict]:
        """Get kline/candlestick data"""
        try:
            # Use category from config if not specified
            if category is None:
                category = getattr(self.config.trading, 'category', 'spot')

            response = await self._make_request("GET", "/v5/market/kline", {
                "category": category,
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            })
            if response.get("retCode") == 0:
                return response.get("result", {}).get("list", [])
            else:
                logger.error(f"Failed to get klines for {symbol}: {response}")
                return []
        except Exception as e:
            logger.error(f"Failed to get klines for {symbol}: {e}")
            raise

    # Account and Trading Methods
    async def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            response = await self._make_request("GET", "/v5/account/info", signed=True)
            if response.get("retCode") == 0:
                return {"result": response.get("result", {})}
            else:
                logger.error(f"Failed to get account info: {response}")
                return {"result": {}}
        except Exception as e:
            logger.error(f"Failed to get account info: {e}")
            return {"result": {}}

    async def get_wallet_balance(self, account_type: str = "UNIFIED") -> Dict:
        """Get wallet balance"""
        try:
            response = await self._make_request("GET", "/v5/account/wallet-balance", {
                "accountType": account_type
            }, signed=True)
            if response.get("retCode") == 0:
                return {"result": response.get("result", {})}
            else:
                logger.error(f"Failed to get wallet balance: {response}")
                return {"result": {}}
        except Exception as e:
            logger.error(f"Failed to get wallet balance: {e}")
            return {"result": {}}

    async def get_positions(self, category: str = None, symbol: str = None) -> List[Dict]:
        """Get positions"""
        try:
            # CRITICAL FIX: Positions endpoint only supports 'linear' or 'option'
            # If config is 'spot', use 'linear' for positions since spot doesn't have positions
            if category is None:
                config_category = getattr(self.config.trading, 'category', 'spot')
                if config_category == 'spot':
                    category = 'linear'  # Use linear for positions when config is spot
                else:
                    category = config_category

            params = {"category": category}

            # Add settleCoin for linear/inverse categories only
            if category in ["linear", "inverse"]:
                params["settleCoin"] = "USDT"
            if symbol:
                params["symbol"] = symbol

            response = await self._make_request("GET", "/v5/position/list", params, signed=True)
            if response.get("retCode") == 0:
                return response.get("result", {}).get("list", [])
            else:
                logger.error(f"Failed to get positions: {response}")
                return []
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
            raise

    def _validate_and_fix_order_size(self, symbol: str, quantity: float, price: float) -> tuple[bool, float, str]:
        """
        Validate and fix order size for Bybit requirements - NO FAKE DATA
        Returns: (is_valid, corrected_quantity, reason)
        """

        # REAL BYBIT MINIMUM ORDER REQUIREMENTS + $0.1 MINIMUM ENFORCED - NO FAKE DATA
        BYBIT_MINIMUMS = {
            "BTCUSDT": {"min_qty": 0.001, "qty_precision": 5, "min_notional": 0.1},  # $0.1 minimum
            "ETHUSDT": {"min_qty": 0.01, "qty_precision": 4, "min_notional": 0.1},   # $0.1 minimum
            "SOLUSDT": {"min_qty": 0.1, "qty_precision": 3, "min_notional": 0.1},    # $0.1 minimum
            "ADAUSDT": {"min_qty": 1.0, "qty_precision": 2, "min_notional": 0.1},    # $0.1 minimum
            "DOGEUSDT": {"min_qty": 1.0, "qty_precision": 2, "min_notional": 0.1}    # $0.1 minimum
        }

        if symbol not in BYBIT_MINIMUMS:
            # Default minimums for unknown symbols - $0.1 MINIMUM ENFORCED
            min_qty = 1.0
            qty_precision = 6
            min_notional = 0.1  # $0.1 minimum for all symbols
        else:
            requirements = BYBIT_MINIMUMS[symbol]
            min_qty = requirements["min_qty"]
            qty_precision = requirements["qty_precision"]
            min_notional = requirements["min_notional"]

        # Check minimum quantity
        if quantity < min_qty:
            corrected_quantity = min_qty
            logger.warning(f"FIXING ORDER SIZE: Quantity {quantity} below minimum {min_qty} for {symbol}")
            logger.warning(f"CORRECTED: {quantity} -> {corrected_quantity}")
            return False, corrected_quantity, f"Below minimum quantity {min_qty}"

        # Check minimum notional value
        notional_value = quantity * price
        if notional_value < min_notional:
            corrected_quantity = max(min_qty, min_notional / price)
            corrected_quantity = round(corrected_quantity, qty_precision)
            logger.warning(f"FIXING NOTIONAL VALUE: ${notional_value:.2f} below minimum ${min_notional}")
            logger.warning(f"CORRECTED: {quantity} -> {corrected_quantity} (${corrected_quantity * price:.2f})")
            return False, corrected_quantity, f"Below minimum notional ${min_notional}"

        # Round to correct precision
        corrected_quantity = round(quantity, qty_precision)
        if corrected_quantity != quantity:
            logger.info(f"PRECISION FIX: {quantity} -> {corrected_quantity}")
            return False, corrected_quantity, f"Rounded to {qty_precision} decimals"

        return True, quantity, "Valid order size"

    async def _check_sufficient_balance(self, symbol: str, side: str, quantity: float, price: float, category: str) -> Dict:
        """Check if account has sufficient balance for the order"""
        try:
            # Get account balance
            balance_data = await self.get_account_balance()

            if not balance_data:
                return {
                    'sufficient': False,
                    'reason': 'Could not retrieve account balance',
                    'required': quantity * price,
                    'available': 0.0
                }

            # Calculate required amount
            required_amount = quantity * price

            # For spot trading
            if category == 'spot':
                if side.lower() == 'buy':
                    # Need quote currency (USDT for BTCUSDT)
                    base_symbol = symbol.replace('USDT', '').replace('USDC', '').replace('BTC', '').replace('ETH', '')
                    quote_currency = 'USDT' if 'USDT' in symbol else 'USDC' if 'USDC' in symbol else 'BTC' if 'BTC' in symbol else 'ETH'

                    available_balance = float(balance_data.get('coin', {}).get(quote_currency, {}).get('availableToWithdraw', 0))
                else:
                    # Need base currency (BTC for BTCUSDT)
                    base_currency = symbol.replace('USDT', '').replace('USDC', '').replace('BTC', '').replace('ETH', '')
                    available_balance = float(balance_data.get('coin', {}).get(base_currency, {}).get('availableToWithdraw', 0))
                    required_amount = quantity  # For selling, we need the base currency amount
            else:
                # For derivatives (linear/option)
                available_balance = float(balance_data.get('totalAvailableBalance', 0))

            # Add 5% buffer for fees and slippage
            required_with_buffer = required_amount * 1.05

            if available_balance < required_with_buffer:
                return {
                    'sufficient': False,
                    'reason': f'Insufficient balance: need ${required_with_buffer:.2f}, have ${available_balance:.2f}',
                    'required': required_with_buffer,
                    'available': available_balance
                }

            return {
                'sufficient': True,
                'reason': 'Sufficient balance available',
                'required': required_with_buffer,
                'available': available_balance
            }

        except Exception as e:
            logger.error(f"Error checking balance: {e}")
            return {
                'sufficient': False,
                'reason': f'Balance check error: {e}',
                'required': quantity * price,
                'available': 0.0
            }

    async def place_order(self, symbol: str, side: str, order_type: str, qty: str, price: str = None, **kwargs) -> Dict:
        """Place an order with automatic order size validation and balance checking - NO FAKE DATA"""
        try:
            # Support both spot and derivatives trading
            category = kwargs.get("category", getattr(self.config.trading, 'category', 'spot'))  # Use config category

            # Convert qty to float for validation
            quantity_float = float(qty)

            # Get current price if not provided
            if not price:
                ticker = await self.get_ticker(symbol)
                if ticker and 'lastPrice' in ticker:
                    current_price = float(ticker['lastPrice'])
                else:
                    logger.error(f"Could not get current price for {symbol}")
                    return {}
            else:
                current_price = float(price)

            # CRITICAL: Check balance before placing order
            balance_check = await self._check_sufficient_balance(symbol, side, quantity_float, current_price, category)
            if not balance_check['sufficient']:
                logger.error(f"INSUFFICIENT BALANCE: {balance_check['reason']}")
                logger.error(f"Required: ${balance_check['required']:.2f}, Available: ${balance_check['available']:.2f}")
                return {"error": "insufficient_balance", "message": balance_check['reason']}

            # Validate and fix order size - NO FAKE DATA
            is_valid, corrected_qty, reason = self._validate_and_fix_order_size(symbol, quantity_float, current_price)

            if not is_valid:
                logger.warning(f"ORDER SIZE FIXED: {reason}")
                logger.warning(f"Original: {qty} -> Corrected: {corrected_qty}")
                qty = str(corrected_qty)  # Use corrected quantity

                # Re-check balance with corrected quantity
                balance_check = await self._check_sufficient_balance(symbol, side, corrected_qty, current_price, category)
                if not balance_check['sufficient']:
                    logger.error(f"INSUFFICIENT BALANCE AFTER CORRECTION: {balance_check['reason']}")
                    return {"error": "insufficient_balance", "message": balance_check['reason']}

            params = {
                "category": category,
                "symbol": symbol,
                "side": side,
                "orderType": order_type,
                "qty": qty
            }

            if price:
                params["price"] = price

            # Add additional parameters (category already handled above)
            for key, value in kwargs.items():
                if key != "category":  # Avoid duplicate category
                    params[key] = value

            logger.info(f"PLACING ORDER: {symbol} {side} {qty} @ {current_price} (${float(qty) * current_price:.2f})")

            response = await self._make_request("POST", "/v5/order/create", params, signed=True)
            if response.get("retCode") == 0:
                logger.info(f"Order placed successfully: {response.get('result', {})}")
                return response.get("result", {})
            else:
                logger.error(f"Failed to place order: {response}")
                return {}
        except Exception as e:
            logger.error(f"Failed to place order: {e}")
            raise

    async def cancel_order(self, symbol: str, order_id: str = None, order_link_id: str = None, category: str = None) -> Dict:
        """Cancel an order"""
        try:
            # Use category from config if not specified
            if category is None:
                category = getattr(self.config.trading, 'category', 'spot')

            params = {
                "category": category,
                "symbol": symbol
            }
            
            if order_id:
                params["orderId"] = order_id
            elif order_link_id:
                params["orderLinkId"] = order_link_id
            else:
                raise ValueError("Either order_id or order_link_id must be provided")
            
            response = await self._make_request("POST", "/v5/order/cancel", params, signed=True)
            if response.get("retCode") == 0:
                logger.info(f"Order cancelled successfully: {response.get('result', {})}")
                return response.get("result", {})
            else:
                logger.error(f"Failed to cancel order: {response}")
                return {}
        except Exception as e:
            logger.error(f"Failed to cancel order: {e}")
            raise

    async def get_open_orders(self, symbol: Optional[str] = None, category: str = None) -> List[Dict[str, Any]]:
        """Get open orders"""
        try:
            # Use category from config if not specified
            if category is None:
                category = getattr(self.config.trading, 'category', 'spot')

            params = {"category": category}
            if symbol:
                params["symbol"] = symbol
            else:
                # Add settleCoin when no symbol is specified
                params["settleCoin"] = "USDT"

            response = await self._make_request("GET", "/v5/order/realtime", params, signed=True)
            if response.get("retCode") == 0:
                result = response.get("result", {})
                return result.get("list", []) if result else []
            else:
                logger.error(f"Failed to get open orders: {response}")
                return []
        except Exception as e:
            logger.error(f"Failed to get open orders: {e}")
            raise

    async def get_order_history(self, symbol: Optional[str] = None, category: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Get order history"""
        try:
            # Use category from config if not specified
            if category is None:
                category = getattr(self.config.trading, 'category', 'spot')

            params = {"category": category, "limit": limit}
            if symbol:
                params["symbol"] = symbol
                
            response = await self._make_request("GET", "/v5/order/history", params, signed=True)
            if response.get("retCode") == 0:
                result = response.get("result", {})
                return result.get("list", []) if result else []
            else:
                logger.error(f"Failed to get order history: {response}")
                return []
        except Exception as e:
            logger.error(f"Failed to get order history: {e}")
            raise

    async def get_funding_rate(self, symbol: str, category: str = None) -> Dict:
        """Get current funding rate for a symbol (only available for linear/inverse)"""
        try:
            # Use category from config if not specified, but funding rates only exist for linear/inverse
            if category is None:
                category = getattr(self.config.trading, 'category', 'linear')
                # Force linear for funding rates if spot is configured
                if category == 'spot':
                    category = 'linear'

            response = await self._make_request("GET", "/v5/market/funding/history", {
                "category": category,
                "symbol": symbol,
                "limit": 1
            })
            if response.get("retCode") == 0:
                funding_list = response.get("result", {}).get("list", [])
                return funding_list[0] if funding_list else {}
            else:
                logger.error(f"Failed to get funding rate for {symbol}: {response}")
                return {}
        except Exception as e:
            logger.error(f"Failed to get funding rate for {symbol}: {e}")
            raise
