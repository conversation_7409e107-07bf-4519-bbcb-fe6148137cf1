#!/usr/bin/env python3
"""
MAIN.PY - BYBIT TRADING BOT - SINGLE ENTRY POINT
MAXIMUM PROFIT GENERATION SYSTEM - ALL FUNCTIONS ACTIVE
ULTRA PROFIT AMPLIFICATION WITH AI-ENHANCED MARGIN TRADING
"""

# CRITICAL: WEBSOCKETS COMPATIBILITY FIX - MUST BE FIRST
import sys
import os

# Add parent directory to path for websockets_compatibility import
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import and activate comprehensive websockets compatibility
try:
    from websockets_compatibility import create_comprehensive_websockets_compatibility
    print("[MAIN] Activating comprehensive websockets compatibility...")
    create_comprehensive_websockets_compatibility()
    print("[MAIN] SUCCESS: WebSockets compatibility layer active")
except ImportError:
    print("[MAIN] WARNING: websockets_compatibility.py not found, using fallback...")
    # Fallback compatibility creation
    from types import ModuleType

    try:
        import websockets
        print(f"[MAIN] WebSockets version: {websockets.__version__}")

        # Create dummy protocol classes for compatibility
        class DummyWebSocketClientProtocol:
            pass
        class DummyWebSocketServerProtocol:
            pass
        class DummyWebSocketCommonProtocol:
            pass

        # Create essential compatibility modules
        modules_to_create = [
            ('websockets.legacy', {
                'connect': websockets.connect,
                'serve': websockets.serve,
                'WebSocketClientProtocol': DummyWebSocketClientProtocol,
                'WebSocketServerProtocol': DummyWebSocketServerProtocol,
                'WebSocketCommonProtocol': DummyWebSocketCommonProtocol
            }),
            ('websockets.legacy.client', {
                'connect': websockets.connect,
                'WebSocketClientProtocol': DummyWebSocketClientProtocol,
                'WebSocketCommonProtocol': DummyWebSocketCommonProtocol
            }),
            ('websockets.legacy.server', {
                'serve': websockets.serve,
                'WebSocketServerProtocol': DummyWebSocketServerProtocol,
                'WebSocketCommonProtocol': DummyWebSocketCommonProtocol
            }),
            ('websockets.asyncio', {'connect': websockets.connect, 'serve': websockets.serve}),
            ('websockets.asyncio.client', {'connect': websockets.connect}),
            ('websockets.asyncio.server', {'serve': websockets.serve}),
        ]

        for module_name, attrs in modules_to_create:
            if module_name not in sys.modules:
                module = ModuleType(module_name)
                for attr_name, attr_value in attrs.items():
                    setattr(module, attr_name, attr_value)
                sys.modules[module_name] = module

        print("[MAIN] SUCCESS: Fallback websockets compatibility created")

    except ImportError as e:
        print(f"[MAIN] ERROR: WebSockets import failed: {e}")
        # Create minimal empty modules
        minimal_modules = [
            'websockets.legacy', 'websockets.legacy.client', 'websockets.legacy.server',
            'websockets.asyncio', 'websockets.asyncio.client', 'websockets.asyncio.server'
        ]
        for module_name in minimal_modules:
            if module_name not in sys.modules:
                sys.modules[module_name] = ModuleType(module_name)
        print("[MAIN] SUCCESS: Minimal websockets compatibility created")
except Exception as e:
    print(f"[MAIN] ERROR: Websockets compatibility error: {e}")
    # Emergency fallback
    from types import ModuleType
    minimal_modules = [
        'websockets.legacy', 'websockets.legacy.client', 'websockets.legacy.server',
        'websockets.asyncio', 'websockets.asyncio.client', 'websockets.asyncio.server'
    ]
    for module_name in minimal_modules:
        if module_name not in sys.modules:
            sys.modules[module_name] = ModuleType(module_name)
    print("[MAIN] SUCCESS: Emergency websockets compatibility created")

import asyncio
import logging
import time
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

# Skip six dependency handling - not actually needed by profit engines

# Setup
load_dotenv()
current_dir = Path(__file__).parent
project_root = current_dir.parent  # Go up one level to BOT directory
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))
(project_root / "logs").mkdir(exist_ok=True)

# AUTO-FIX SYSTEM: Integrate all critical fixes before starting

def apply_critical_fixes():
    """Apply all critical fixes automatically on startup"""
    import re
    from pathlib import Path
    
    fixes_applied = 0
    
    # Fix 1: SQLite NOW() Compatibility
    project_root = Path(__file__).parent.parent  # Go up to BOT directory
    sqlite_files = [
        "bybit_bot/ai/memory_manager.py",
        "bybit_bot/ai/advanced_memory_system.py",
        "bybit_bot/data_crawler/huggingface_integration.py",
        "bybit_bot/core/bot_manager.py"
    ]

    for file_path_str in sqlite_files:
        file_path = project_root / file_path_str
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "NOW()" in content:
                    content = re.sub(r'NOW\(\)', 'CURRENT_TIMESTAMP', content)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    fixes_applied += 1
                    print(f"    ✓ Fixed SQLite compatibility in {file_path}")
            except Exception as e:
                print(f"    [WARNING] Could not fix {file_path}: {e}")
    
    return fixes_applied > 0

# Apply fixes automatically
try:
    apply_critical_fixes()
except Exception as e:
    print(f"[WARNING] Some fixes failed: {e}")

# Simple logging to prevent Windows file locking issues

# Use basic file handler to avoid Windows rotation issues
project_root = Path(__file__).parent.parent  # Go up to BOT directory
log_handler = logging.FileHandler(project_root / 'logs/bybit_trading_bot.log', encoding='utf-8')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        log_handler,
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("BybitTradingBot")

class BybitTradingBotSystem:
    """Main trading bot system that orchestrates all components"""

    def __init__(self, config=None):
        self.is_running = False
        self.start_time = datetime.now(timezone.utc)
        self.last_4hour_report = datetime.now(timezone.utc)
        self.last_hourly_report = datetime.now(timezone.utc)  # NEW: Track hourly reports
        self.total_profit = 0.0
        self.total_trades = 0

        # Initialize config early for enhanced learning systems
        if config is not None:
            self.config = config
        else:
            # Will be initialized later in async startup
            self.config = None

        # Component type annotations
        self.intelligent_ml: Optional[Any] = None
        self.platform_risk_converter: Optional[Any] = None
        self.platform_exploit_strategies: Optional[Any] = None
        self.profit_target_enforcer: Optional[Any] = None
        self.market_predictor: Optional[Any] = None
        self.huggingface_manager: Optional[Any] = None
        self.risk_manager: Optional[Any] = None
        self.meta_cognition: Optional[Any] = None
        self.performance_analyzer: Optional[Any] = None
        self.ultra_profit_amplifier: Optional[Any] = None
        self.memory_manager: Optional[Any] = None
        self.advanced_memory: Optional[Any] = None
        self.hyper_profit_engine: Optional[Any] = None
        self.advanced_profit_engine: Optional[Any] = None
        self.strategy_manager: Optional[Any] = None
        self.database_manager: Optional[Any] = None
        self.db_manager: Optional[Any] = None
        self.enhanced_time_manager: Optional[Any] = None
        self.agent_orchestrator: Optional[Any] = None

        # PERFORMANCE SCALING SYSTEM - CPU-efficient scaling with all components
        self.performance_scaling_system: Optional[Any] = None

        # ENHANCED LEARNING SYSTEMS - Initialize as None, will be set during startup
        self.enhanced_multi_timeframe_learner: Optional[Any] = None
        self.tier1_integration_manager: Optional[Any] = None
        self.advanced_data_integration_engine: Optional[Any] = None
        self.enhanced_adaptive_learning_engine: Optional[Any] = None

        # MPC SECURITY SYSTEMS - Initialize as None, will be set during startup
        self.mpc_engine: Optional[Any] = None
        self.threshold_manager: Optional[Any] = None
        self.mpc_wallet: Optional[Any] = None
        self.mpc_security: Optional[Any] = None
        self.mpc_active: bool = False

        # OPTIMIZATION SYSTEMS - All 8 tiers integrated
        self.optimization_manager: Optional[Any] = None
        self.streaming_processor: Optional[Any] = None
        self.compression_engine: Optional[Any] = None
        self.multi_agent_rl: Optional[Any] = None
        self.graph_neural_networks: Optional[Any] = None
        self.edge_computing: Optional[Any] = None
        self.quantum_engine: Optional[Any] = None
        self.feature_pipeline: Optional[Any] = None
        self.adaptive_learning: Optional[Any] = None
        self.optimization_active: bool = False

        # ULTRA-AGGRESSIVE PROFIT TARGET SYSTEM - 3.3x INCREASE FOR MAXIMUM PROFIT
        self.profit_targets: Dict[str, Any] = {
            'hourly_target': 6250.0,      # $6,250 per hour target (was $625 - 10x INCREASE!)
            'daily_target': 150000.0,     # $150,000 per day target (was $15,000 - 10x INCREASE!)
            'session_target': 0.0,        # Will be calculated based on runtime
            'profit_per_minute': 104.17,  # $104.17 per minute (was $10.42 - 10x INCREASE!)
            'profit_per_second': 1.74,    # $1.74 per second (was $0.174 - 10x INCREASE!)
            'target_achievement_rate': 0.0,  # Percentage of target achieved
            'performance_multiplier': 1.0,   # ML adjustment factor
            'ultra_mode_active': True,        # NEW: Ultra profit mode
            'amplification_factor': 1.0,      # NEW: Profit amplification from ultra engine
            'margin_amplification_active': True,  # NEW: AI-enhanced margin trading
            'ai_systems_active': False,      # NEW: Track AI activation status
            'leverage_optimization': True    # NEW: Dynamic leverage optimization
        }

        # HOURLY PROFIT TRACKING
        self.hourly_stats: Dict[str, Any] = {
            'current_hour_start': datetime.now(timezone.utc),
            'current_hour_profit': 0.0,
            'current_hour_trades': 0,
            'hourly_history': [],  # List of hourly performance records
            'best_hour_profit': 0.0,
            'worst_hour_profit': 0.0,
            'average_hourly_profit': 0.0,
            'hours_above_target': 0,
            'hours_below_target': 0,
            'margin_trading_hours': 0,     # NEW: Hours with margin trading active
            'ai_enhanced_hours': 0         # NEW: Hours with AI enhancement active
        }

        # REAL ORDER TRACKING FOR ACTUAL P&L MONITORING
        self.active_orders: Dict[str, Dict[str, Any]] = {}  # Track real orders for P&L updates

        self.session_stats: Dict[str, Any] = {
            'start_balance': 0.0,
            'current_balance': 0.0,
            'total_profit': 0.0,
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'max_profit': 0.0,
            'max_loss': 0.0,
            'strategies_used': {},
            'symbols_traded': set(),
            'profit_velocity': 0.0,      # Profit per minute
            'target_performance': 0.0,   # How well we're meeting targets
            'margin_trades': 0,          # NEW: Count of margin trades
            'leverage_usage': {},        # NEW: Track leverage usage
            'ai_decisions': 0            # NEW: Count of AI-driven decisions
        }

        self.components = {}

        # HOLD and OPPORTUNITY MONITORING STATE
        self.hold_mode = False
        self.hold_start_time: float = 0.0
        self.hold_duration: float = 0.0
        self.opportunities_found_during_hold = []
        self.profit_accumulated_during_hold = 0.0
        self.recovery_opportunities = []
        self.last_trade_time = 0
        self.trade_count = 0

        logger.info("Bybit Trading Bot System initialized")

    async def generate_hourly_profit_summary(self):
        """Generate comprehensive hourly profit summary with target analysis"""
        try:
            current_time = datetime.now(timezone.utc)
            runtime_hours = (current_time - self.start_time).total_seconds() / 3600
            runtime_minutes = (current_time - self.start_time).total_seconds() / 60

            # Calculate current session targets
            expected_profit_by_now = runtime_hours * self.profit_targets['hourly_target']
            self.profit_targets['session_target'] = expected_profit_by_now

            # Update target achievement rate
            if expected_profit_by_now > 0:
                self.profit_targets['target_achievement_rate'] = (self.total_profit / expected_profit_by_now) * 100

            # Calculate profit velocity
            if runtime_minutes > 0:
                self.session_stats['profit_velocity'] = self.total_profit / runtime_minutes
                self.session_stats['target_performance'] = (self.session_stats['profit_velocity'] / self.profit_targets['profit_per_minute']) * 100

            # Complete current hour stats
            hour_profit = self.hourly_stats['current_hour_profit']
            hour_trades = self.hourly_stats['current_hour_trades']

            # Update hourly records
            self.hourly_stats['hourly_history'].append({
                'hour_start': self.hourly_stats['current_hour_start'],
                'hour_end': current_time,
                'profit': hour_profit,
                'trades': hour_trades,
                'target_met': hour_profit >= self.profit_targets['hourly_target'],
                'performance_pct': (hour_profit / self.profit_targets['hourly_target']) * 100 if self.profit_targets['hourly_target'] > 0 else 0
            })

            # Update best/worst tracking
            if hour_profit > self.hourly_stats['best_hour_profit']:
                self.hourly_stats['best_hour_profit'] = hour_profit
            if hour_profit < self.hourly_stats['worst_hour_profit'] or self.hourly_stats['worst_hour_profit'] == 0:
                self.hourly_stats['worst_hour_profit'] = hour_profit

            # Update target achievement counters
            if hour_profit >= self.profit_targets['hourly_target']:
                self.hourly_stats['hours_above_target'] += 1
            else:
                self.hourly_stats['hours_below_target'] += 1

            # Calculate average hourly profit
            if len(self.hourly_stats['hourly_history']) > 0:
                total_hourly_profit = sum(h['profit'] for h in self.hourly_stats['hourly_history'])
                self.hourly_stats['average_hourly_profit'] = total_hourly_profit / len(self.hourly_stats['hourly_history'])

            # COMPREHENSIVE HOURLY REPORT
            print("\n" + "=" * 80)
            print("=" * 80)
            print(f"Session Runtime: {runtime_hours:.2f} hours ({runtime_minutes:.1f} minutes)")
            print(f"Current Hour Completed: {self.hourly_stats['current_hour_start'].strftime('%H:%M')} - {current_time.strftime('%H:%M')}")
            print()

            print("TRADING ACTIVITY:")
            print(f"  This Hour Trades:     {hour_trades:>10}")
            print(f"  Total Session Trades: {self.total_trades:>10}")
            print(f"  Success Rate:         {(self.session_stats['successful_trades'] / max(self.total_trades, 1) * 100):>9.1f}%")
            print()

            # TARGET ACHIEVEMENT STATUS
            if hour_profit >= self.profit_targets['hourly_target']:
                status = "TARGET ACHIEVED"
                status_symbol = "SUCCESS"
            elif hour_profit >= self.profit_targets['hourly_target'] * 0.8:
                status = "NEAR TARGET"
                status_symbol = "WARNING"
            else:
                status = "BELOW TARGET"
                status_symbol = "ALERT"

            print(f"HOUR STATUS: {status_symbol} - {status}")
            print("=" * 80)

            # Log detailed summary

            # Reset for next hour
            self.hourly_stats['current_hour_start'] = current_time
            self.hourly_stats['current_hour_profit'] = 0.0
            self.hourly_stats['current_hour_trades'] = 0
            self.last_hourly_report = current_time

            # SEND TARGET PERFORMANCE TO ML SYSTEM
            await self.update_ml_profit_targets()

        except Exception as e:
            logger.error(f"Error generating hourly profit summary: {e}")

    async def update_ml_profit_targets(self):
        """Update ML system with current profit target performance for strategy optimization"""
        try:
            if hasattr(self, 'intelligent_ml') and self.intelligent_ml:
                target_performance_data = {
                    'hourly_target': self.profit_targets['hourly_target'],
                    'current_achievement_rate': self.profit_targets['target_achievement_rate'],
                    'profit_velocity': self.session_stats['profit_velocity'],
                    'target_velocity': self.profit_targets['profit_per_minute'],
                    'performance_gap': self.profit_targets['target_achievement_rate'] - 100.0,
                    'hours_above_target': self.hourly_stats['hours_above_target'],
                    'hours_below_target': self.hourly_stats['hours_below_target'],
                    'average_hourly_profit': self.hourly_stats['average_hourly_profit']
                }

                # Send to ML system for strategy adjustment
                await self.intelligent_ml.update_profit_targets(target_performance_data)
                logger.info("ML system updated with profit target performance data")

            # Update profit target enforcer if available
            if hasattr(self, 'profit_target_enforcer') and self.profit_target_enforcer:
                await self.profit_target_enforcer.update_target_performance(
                    self.profit_targets['target_achievement_rate'],
                    self.session_stats['profit_velocity']
                )
                logger.info("Profit target enforcer updated with performance metrics")

        except Exception as e:
            logger.error(f"Error updating ML profit targets: {e}")

    async def validate_system_readiness(self):
        """Validate all critical systems are ready for profitable trading"""
        print("  [CHECKING] Validating Enhanced Bybit Client...")

        try:
            # Test enhanced client method signatures with error handling
            print("  [DEBUG] Importing EnhancedBybitClient...")
            try:
                from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
                print("  [DEBUG] SUCCESS: EnhancedBybitClient imported successfully")
            except ImportError as e:
                print(f"  [ERROR] Failed to import EnhancedBybitClient: {e}")
                raise e

            print("  [DEBUG] Importing EnhancedBotConfig...")
            from bybit_bot.core.config import EnhancedBotConfig
            
            config = EnhancedBotConfig()
            client = EnhancedBybitClient(config)
            
            # Check if get_market_data has correct signature
            import inspect
            sig = inspect.signature(client.get_market_data)
            params = list(sig.parameters.keys())
            
            if 'timeframe' in params and 'limit' in params:
                print("    [OK] get_market_data method signature correct")
            else:
                print("    [ERROR] get_market_data method signature incorrect")
                return False
            
            # Check if get_current_price exists
            if hasattr(client, 'get_current_price'):
                print("    [OK] get_current_price method exists")
            else:
                print("    [ERROR] get_current_price method missing")
                return False

            # Test API signature generation
            print("  [CHECKING] Validating API signature generation...")
            from bybit_bot.exchange.bybit_client import BybitClient
            
            base_client = BybitClient(config)
            if hasattr(base_client, '_create_signature'):
                print("    [OK] API signature method exists")
            else:
                print("    [ERROR] API signature method missing")
                return False

            # Check configuration
            print("  [CHECKING] Validating configuration...")
            api_key = getattr(config, 'bybit_api_key', '') or config.api_keys.bybit.get('api_key', '')
            api_secret = getattr(config, 'bybit_api_secret', '') or config.api_keys.bybit.get('api_secret', '')
            
            if api_key and api_secret:
                print(f"    [OK] API credentials found: {api_key[:8]}...")
            else:
                print("    [ERROR] API credentials missing")
                return False
            
            return True
            
        except Exception as e:
            print(f"  [ERROR] Validation failed: {e}")
            return False

    async def initialize_components(self):
        """Initialize core components for testing and validation"""
        try:
            # Initialize config if not already done
            if self.config is None:
                from bybit_bot.core.config import EnhancedBotConfig
                self.config = EnhancedBotConfig()

            # Initialize enhanced learning systems
            if self.enhanced_multi_timeframe_learner is None:
                from bybit_bot.ai.enhanced_multi_timeframe_learner import EnhancedMultiTimeframeLearner
                self.enhanced_multi_timeframe_learner = EnhancedMultiTimeframeLearner(self.config)

            if self.tier1_integration_manager is None:
                from bybit_bot.ai.tier1_integration_manager import Tier1IntegrationManager
                db_path = str(Path(__file__).parent.parent / "bybit_trading_bot.db")
                self.tier1_integration_manager = Tier1IntegrationManager(self.config, db_path)
                self.tier1_integration_manager.set_enhanced_multi_timeframe_learner(self.enhanced_multi_timeframe_learner)

            if self.advanced_data_integration_engine is None:
                from bybit_bot.ai.advanced_data_integration_engine import AdvancedDataIntegrationEngine
                db_path = str(Path(__file__).parent.parent / "bybit_trading_bot.db")
                self.advanced_data_integration_engine = AdvancedDataIntegrationEngine(
                    config=self.config,
                    db_path=db_path
                )

            if self.enhanced_adaptive_learning_engine is None:
                from bybit_bot.ai.adaptive_learning_engine import AdaptiveLearningEngine
                self.enhanced_adaptive_learning_engine = AdaptiveLearningEngine()

            # Initialize backup cleanup system
            print("  [INITIALIZING] Backup cleanup system...")
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(__file__)))
                from schedule_backup_cleanup import integrate_with_main_system
                self.backup_cleanup_controls = integrate_with_main_system()
                print("  [SUCCESS] Backup cleanup system integrated (10-backup limit active)")
            except Exception as e:
                print(f"  [WARNING] Backup cleanup integration failed: {e}")
                self.backup_cleanup_controls = None

            return True

        except Exception as e:
            print(f"Component initialization failed: {e}")
            return False

    async def initialize_all_systems(self):
        """Initialize all trading system components - ALL FUNCTIONS ACTIVE"""
        try:
            print("[VALIDATING] PRE-INITIALIZATION VALIDATION...")

            # VALIDATION: Check critical fixes are applied
            validation_passed = await self.validate_system_readiness()
            if not validation_passed:
                print("[ERROR] VALIDATION FAILED: System not ready for trading")
                return False

            logger.info("Initializing ALL trading system components for maximum profit...")

            # Initialize core configuration with error handling
            try:
                from bybit_bot.core.config import EnhancedBotConfig

                self.config = EnhancedBotConfig()

                # Validate API keys are loaded
                if not hasattr(self.config, 'api_keys'):
                    raise Exception("API keys not loaded in config")

                logger.info("Configuration loaded successfully")
                print(f"DEBUG: Config type: {type(self.config)}")
                print(f"DEBUG: API keys available: {hasattr(self.config, 'api_keys')}")

            except ImportError as import_error:
                logger.error(f"Configuration import failed: {import_error}")
                print(f"ERROR: Cannot import EnhancedBotConfig - {import_error}")
                print("SOLUTION: Check if bybit_bot.core.config module exists and is properly structured")
                return False
            except Exception as config_error:
                logger.error(f"Configuration loading failed: {config_error}")
                print(f"ERROR: Configuration loading failed: {config_error}")
                import traceback
                print(f"TRACEBACK: {traceback.format_exc()}")
                return False

            # Initialize database with error handling
            try:
                from bybit_bot.database.connection import DatabaseManager
                self.db_manager = DatabaseManager(self.config)
                await self.db_manager.initialize()
                self.database_manager = self.db_manager  # Alias for compatibility
                self.components['database'] = self.db_manager
                logger.info("Database initialized")

            except ImportError as import_error:
                logger.error(f"Database import failed: {import_error}")
                print(f"ERROR: Cannot import DatabaseManager - {import_error}")
                print("SOLUTION: Check if bybit_bot.database.connection module exists")
                self.db_manager = None
                self.database_manager = None
            except Exception as db_error:
                logger.error(f"Database initialization failed: {db_error}")
                print(f"WARNING: Database initialization failed: {db_error}")
                import traceback
                print(f"TRACEBACK: {traceback.format_exc()}")
                # Create a minimal database manager for basic operations
                self.db_manager = None
                self.database_manager = None

            # Startup delay to prevent rate limiting
            await asyncio.sleep(5)

            # Initialize Enhanced Bybit client for MAXIMUM PROFIT
            try:
                from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient

                # Validate config has API keys before creating client
                if not hasattr(self.config, 'api_keys') or not self.config.api_keys:
                    raise Exception("No API keys found in config")

                print(f"DEBUG: API keys available: {bool(self.config.api_keys)}")
                print(f"DEBUG: Bybit API key exists: {bool(getattr(self.config.api_keys, 'bybit', {}).get('api_key'))}")

                self.bybit_client = EnhancedBybitClient(self.config)
                await self.bybit_client.initialize()

                # Test market data retrieval immediately
                test_symbol = "ETHUSDT"
                ticker_data = await self.bybit_client.get_ticker(test_symbol)
                orderbook_data = await self.bybit_client.get_order_book(test_symbol)

                print(f"DEBUG: Ticker data for {test_symbol}: {bool(ticker_data)}")
                print(f"DEBUG: Order book data for {test_symbol}: {bool(orderbook_data)}")

                if not ticker_data and not orderbook_data:
                    print("WARNING: Market data retrieval test failed")
                    logger.warning("Market data retrieval test failed during initialization")
                else:
                    print("SUCCESS: Market data retrieval test passed")
                    logger.info("Market data retrieval test passed during initialization")

                self.components['bybit_client'] = self.bybit_client
                self.enhanced_bybit_client = self.bybit_client  # Alias for compatibility
                logger.info("Enhanced Bybit client initialized for maximum profit generation")

            except ImportError as import_error:
                logger.error(f"Bybit client import failed: {import_error}")
                print(f"ERROR: Cannot import EnhancedBybitClient - {import_error}")
                print("SOLUTION: Check if bybit_bot.exchange.enhanced_bybit_client module exists")
                return False
            except Exception as client_error:
                logger.error(f"Bybit client initialization failed: {client_error}")
                print(f"ERROR: Bybit client initialization failed: {client_error}")
                import traceback
                print(f"TRACEBACK: {traceback.format_exc()}")
                return False

            # Startup delay to prevent rate limiting
            await asyncio.sleep(5)

            # CORE TRADING SYSTEM - MINIMAL DEPENDENCIES
            logger.info("Core trading system initialization - minimal dependencies mode")

            # AI FOLDER ACTIVATION - MANDATORY ALL SYSTEMS ACTIVE
            try:
                from bybit_bot.ai.ai_folder_activation_manager import activate_ai_folder, initialize_ai_activation_manager

                # Initialize AI activation manager with proper dependencies
                ai_manager = initialize_ai_activation_manager(
                    config=self.config,
                    database_manager=self.database_manager,
                    bybit_client=self.bybit_client
                )

                # Activate all AI systems with dependencies
                ai_activation_results = await activate_ai_folder(
                    config=self.config,
                    database_manager=self.database_manager,
                    bybit_client=self.bybit_client
                )

                active_count = sum(1 for result in ai_activation_results.values() if result)
                total_count = len(ai_activation_results)

                logger.info(f"AI Folder Activation: {active_count}/{total_count} systems operational")

                self.ai_activation_manager = ai_manager
                self.ai_instances = ai_manager.get_active_ai_instances()
                self.profit_targets['ai_systems_active'] = active_count >= total_count * 0.8  # 80% success threshold

                # Mark AI monitoring ready for background activation
                self.ai_manager = ai_manager
                self.ai_manager_ready = True

            except ImportError as import_error:
                logger.error(f"AI Folder activation import failed: {import_error}")
                print(f"WARNING: Cannot import AI activation manager - {import_error}")
                print("SOLUTION: Check if bybit_bot.ai.ai_folder_activation_manager module exists")
                self.ai_instances = {}
                self.ai_manager_ready = False
            except Exception as e:
                logger.error(f"AI Folder activation failed: {e}")
                print(f"WARNING: AI Folder activation incomplete - {e}")
                import traceback
                print(f"TRACEBACK: {traceback.format_exc()}")
                self.ai_instances = {}
                self.ai_manager_ready = False

            # ENHANCED TIME MANAGER - MANDATORY COMPONENT (50-60s timing)
            try:
                from bybit_bot.core.enhanced_time_manager import EnhancedTimeManager
                if self.database_manager:
                    self.enhanced_time_manager = EnhancedTimeManager(
                        config=self.config,
                        database_manager=self.database_manager
                    )
                else:
                    # Create a minimal time manager without database
                    self.enhanced_time_manager = None
                logger.info("Enhanced Time Manager activated - granular time awareness operational")
            except Exception as e:
                print(f"ERROR: Enhanced Time Manager initialization failed: {e}")
                logger.error(f"Enhanced Time Manager failed: {e}")
                self.enhanced_time_manager = None

            # AGENT ORCHESTRATOR - MANDATORY COMPONENT (70-80s timing)
            try:
                from bybit_bot.agents.agent_orchestrator import AgentOrchestrator
                if self.database_manager:
                    self.agent_orchestrator = AgentOrchestrator(
                        config=self.config,
                        database_manager=self.database_manager
                    )
                else:
                    self.agent_orchestrator = None
                logger.info("Agent Orchestrator activated - multi-agent coordination operational")
            except Exception as e:
                print(f"ERROR: Agent Orchestrator initialization failed: {e}")
                logger.error(f"Agent Orchestrator failed: {e}")
                self.agent_orchestrator = None

            # PERFORMANCE SCALING SYSTEM - COMPREHENSIVE SCALING IMPLEMENTATION (80-90s timing)
            try:
                from bybit_bot.performance.scaling_engine import PerformanceScalingSystem
                self.performance_scaling_system = PerformanceScalingSystem(self.config)

                # Initialize the scaling system
                scaling_init_success = await self.performance_scaling_system.initialize()

                if scaling_init_success:
                    # Start scaling operations
                    await self.performance_scaling_system.start_scaling_operations()
                    logger.info("ENHANCED Performance Scaling System activated - CPU-efficient scaling operational")
                    print("SUCCESS: Performance Scaling System with all 10 components active:")
                    print("  1. CPU-efficient load balancer with multiple algorithms")
                    print("  2. Intelligent connection pooling with dynamic sizing")
                    print("  3. WebSocket multiplexing manager for concurrent connections")
                    print("  4. Auto-scaling engine with CPU/memory/latency triggers")
                    print("  5. Redis caching layer for market data optimization")
                    print("  6. Priority queue system for trade signals")
                    print("  7. Resource monitoring dashboard with real-time alerts")
                    print("  8. Database optimization with indexing and query optimization")
                    print("  9. Geographic load distribution (framework ready)")
                    print("  10. Performance benchmarking suite with comprehensive metrics")
                else:
                    print("WARNING: Performance Scaling System initialization failed")
                    logger.warning("Performance Scaling System initialization failed")

            except ImportError as import_error:
                logger.error(f"Performance Scaling System import failed: {import_error}")
                print(f"ERROR: Cannot import PerformanceScalingSystem - {import_error}")
                print("SOLUTION: Check if bybit_bot.performance.scaling_engine module exists")
                self.performance_scaling_system = None
            except Exception as e:
                print(f"ERROR: Performance Scaling System initialization failed: {e}")
                logger.error(f"Performance Scaling System failed: {e}")
                import traceback
                print(f"TRACEBACK: {traceback.format_exc()}")
                self.performance_scaling_system = None

            # INTELLIGENT ML SYSTEM - NO EXTERNAL DEPENDENCIES
            try:
                from bybit_bot.ai.intelligent_ml_system import IntelligentMLSystem
                self.intelligent_ml = IntelligentMLSystem(self.config.__dict__)
                logger.info("Intelligent ML System initialized")
            except ImportError as import_error:
                logger.error(f"Failed to import Intelligent ML System: {import_error}")
                print(f"WARNING: Cannot import IntelligentMLSystem - {import_error}")
                self.intelligent_ml = None
            except Exception as e:
                logger.error(f"Failed to initialize Intelligent ML System: {e}")
                print(f"WARNING: Intelligent ML System initialization failed: {e}")
                self.intelligent_ml = None

            # PLATFORM RISK-TO-PROFIT CONVERTER
            try:
                from bybit_bot.risk_management.platform_risk_profit_converter import BybitPlatformRiskConverter, AdvancedPlatformExploitStrategies
                self.platform_risk_converter = BybitPlatformRiskConverter(self.config.__dict__)
                self.platform_exploit_strategies = AdvancedPlatformExploitStrategies(self.bybit_client, self.config.__dict__)
                logger.info("Platform Risk Converter initialized")
            except ImportError as import_error:
                logger.error(f"Failed to import Platform Risk Converter: {import_error}")
                print(f"WARNING: Cannot import Platform Risk Converter - {import_error}")
                self.platform_risk_converter = None
                self.platform_exploit_strategies = None
            except Exception as e:
                logger.error(f"Platform Risk Converter initialization failed: {e}")
                print(f"WARNING: Platform Risk Converter initialization failed: {e}")
                self.platform_risk_converter = None
                self.platform_exploit_strategies = None

            # FULL ML CAPABILITIES RESTORATION - ALL COMPONENTS ACTIVE

            # PROFIT TARGET ENFORCER - ACTIVE LEARNING (AFTER ULTRA AMPLIFIER)
            try:
                if self.database_manager is not None:
                    from bybit_bot.profit_maximization.profit_target_enforcer import ProfitTargetEnforcer
                    self.profit_target_enforcer = ProfitTargetEnforcer(
                        config=self.config,
                        database_manager=self.database_manager
                    )
                else:
                    print("WARNING: Database manager not available for Profit Target Enforcer")
                    self.profit_target_enforcer = None
            except Exception as e:
                print(f"ERROR: Profit Target Enforcer initialization failed: {e}")
                self.profit_target_enforcer = None

            # ML MARKET PREDICTOR - REAL DATA IMPLEMENTATION
            try:
                from bybit_bot.ml.market_predictor import MLMarketPredictor
                self.market_predictor = MLMarketPredictor(
                    config=self.config,
                    db_manager=self.database_manager
                )
                logger.info("ML Market Predictor initialized with REAL data processing")
            except Exception as e:
                print(f"ERROR: ML Market Predictor initialization failed: {e}")
                self.market_predictor = None

            # HUGGINGFACE MANAGER - REAL DATA INTEGRATION
            try:
                from bybit_bot.data_integration.huggingface_manager import HuggingFaceManager
                self.huggingface_manager = HuggingFaceManager(
                    config=self.config,
                    database_manager=self.database_manager
                )
                # Set Bybit client for real market data access
                self.huggingface_manager.bybit_client = self.bybit_client
                logger.info("HuggingFace Manager initialized with REAL data processing")
            except Exception as e:
                print(f"ERROR: HuggingFace Manager initialization failed: {e}")
                self.huggingface_manager = None

            # ADVANCED RISK MANAGER WITH ML
            try:
                from bybit_bot.ai.advanced_risk_manager import AdvancedRiskManager
                self.risk_manager = AdvancedRiskManager(
                    config=self.config.to_dict(),
                    database_manager=self.database_manager,
                    bybit_client=self.bybit_client
                )
            except Exception as e:
                print(f"ERROR: Risk Manager initialization failed: {e}")
                # Fallback to basic risk manager
                try:
                    if self.database_manager is not None:
                        from bybit_bot.risk.risk_manager import RiskManager
                        self.risk_manager = RiskManager(
                            config=self.config,
                            database_manager=self.database_manager,
                            bybit_client=self.bybit_client
                        )
                    else:
                        print("WARNING: Database manager not available for Risk Manager")
                        self.risk_manager = None
                except Exception as e2:
                    print(f"ERROR: Fallback Risk Manager also failed: {e2}")
                    self.risk_manager = None

            # META-COGNITION ENGINE
            try:
                from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
                self.meta_cognition = MetaCognitionEngine(
                    config=self.config,
                    database_manager=self.database_manager
                )
            except Exception as e:
                print(f"ERROR: Meta-Cognition Engine initialization failed: {e}")
                self.meta_cognition = None

            # PERFORMANCE ANALYZER
            try:
                if self.database_manager is not None:
                    from bybit_bot.analytics.performance_analyzer import PerformanceAnalyzer
                    self.performance_analyzer = PerformanceAnalyzer(
                        config=self.config,
                        database_manager=self.database_manager
                    )
                else:
                    print("WARNING: Database manager not available for Performance Analyzer")
                    self.performance_analyzer = None
            except Exception as e:
                print(f"ERROR: Performance Analyzer initialization failed: {e}")
                self.performance_analyzer = None

            # ULTRA PROFIT AMPLIFICATION ENGINE - NEW MAXIMUM PROFIT SYSTEM
            try:
                if self.database_manager is not None:
                    from bybit_bot.profit_maximization.ultra_profit_amplification_engine import UltraProfitAmplificationEngine
                    self.ultra_profit_amplifier = UltraProfitAmplificationEngine(
                        config=self.config,
                        database_manager=self.database_manager
                    )
                else:
                    print("WARNING: Database manager not available for Ultra Profit Amplification Engine")
                    self.ultra_profit_amplifier = None
                
                # INJECT EXISTING MARGIN TRADING COMPONENTS FOR AI-ENHANCED INTEGRATION
                margin_components_injected = False
                try:
                    # Inject margin trading client (Enhanced Bybit Client)
                    margin_client = getattr(self, 'bybit_client', None)
                    
                    # Inject risk manager if available
                    risk_manager = getattr(self, 'risk_manager', None)
                    
                    # Inject strategy manager if available  
                    strategy_manager = getattr(self, 'strategy_manager', None)
                    
                    # Inject AI components from activated AI folder
                    meta_learner = self.ai_instances.get('meta_learner', None)
                    meta_cognition = self.ai_instances.get('meta_cognition_engine', None)
                    ml_system = getattr(self, 'intelligent_ml', None)
                    
                    # Inject all available components
                    if self.ultra_profit_amplifier is not None:
                        self.ultra_profit_amplifier.inject_margin_components(
                            margin_client=margin_client,
                            risk_manager=risk_manager,
                            strategy_manager=strategy_manager,
                            meta_learner=meta_learner,
                            meta_cognition=meta_cognition,
                            ml_system=ml_system
                        )
                    else:
                        print("WARNING: Ultra Profit Amplifier not available for component injection")
                    
                    margin_components_injected = True
                    self.profit_targets['margin_amplification_active'] = True
                    
                except Exception as inject_error:
                    logger.warning(f"Margin component injection partially failed: {inject_error}")
                    print(f"WARNING: Partial margin integration - {inject_error}")

                if margin_components_injected:
                    logger.info("Margin components successfully injected into Ultra Profit Amplifier")
                    print("SUCCESS: Margin amplification components integrated")

            except Exception as e:
                print(f"ERROR: Ultra Profit Amplification Engine initialization failed: {e}")
                self.ultra_profit_amplifier = None
                self.profit_targets['margin_amplification_active'] = False

            # ADVANCED MEMORY SYSTEM WITH TIME AWARENESS
            try:
                if self.database_manager is not None:
                    from bybit_bot.ai.memory_manager import PersistentMemoryManager
                    from bybit_bot.ai.advanced_memory_system import AdvancedMemorySystem

                    # Initialize base memory manager
                    self.memory_manager = PersistentMemoryManager(
                        config=self.config,
                        database_manager=self.database_manager
                    )
                    await self.memory_manager.initialize()

                    # Initialize advanced memory system
                    self.advanced_memory = AdvancedMemorySystem(
                        config=self.config,
                        database_manager=self.database_manager,
                        base_memory_manager=self.memory_manager
                    )
                    await self.advanced_memory.initialize()
                else:
                    print("WARNING: Database manager not available for Memory System")
                    self.memory_manager = None
                    self.advanced_memory = None

                # Update system run time for downtime tracking
                downtime = None
                if self.memory_manager is not None:
                    try:
                        await self.memory_manager.update_system_run_time()
                        # Check system downtime
                        downtime = await self.memory_manager.get_system_downtime()
                    except Exception as e:
                        print(f"WARNING: Memory manager operation failed: {e}")
                        downtime = None
                if downtime:
                    print(f"INFO: System downtime detected: {downtime}")
                else:
                    print("INFO: No system downtime detected")

            except Exception as e:
                print(f"ERROR: Advanced Memory System initialization failed: {e}")
                self.memory_manager = None
                self.advanced_memory = None

            # PROFIT ENGINES
            try:
                if self.database_manager is not None:
                    from bybit_bot.profit_maximization.hyper_profit_engine import HyperProfitEngine
                    self.hyper_profit_engine = HyperProfitEngine(
                        config=self.config,
                        bybit_client=self.bybit_client,
                        database_manager=self.database_manager
                    )
                else:
                    print("WARNING: Database manager not available for Hyper Profit Engine")
                    self.hyper_profit_engine = None
            except Exception as e:
                print(f"ERROR: Hyper Profit Engine initialization failed: {e}")
                self.hyper_profit_engine = None

            try:
                if self.database_manager is not None:
                    from bybit_bot.profit_maximization.advanced_profit_engine import AdvancedProfitEngine
                    self.advanced_profit_engine = AdvancedProfitEngine(
                        config=self.config,
                        bybit_client=self.bybit_client,
                        database_manager=self.database_manager
                    )
                else:
                    print("WARNING: Database manager not available for Advanced Profit Engine")
                    self.advanced_profit_engine = None
            except Exception as e:
                print(f"ERROR: Advanced Profit Engine initialization failed: {e}")
                self.advanced_profit_engine = None

            # MARGIN SAFETY MANAGER - ULTRA CONSERVATIVE PROTECTION
            try:
                from bybit_bot.risk_management.margin_safety_manager import MarginSafetyManager
                self.margin_safety_manager = MarginSafetyManager(
                    bybit_client=self.bybit_client,
                    config=self.config
                )
            except Exception as e:
                print(f"ERROR: Margin Safety Manager initialization failed: {e}")
                self.margin_safety_manager = None

            # ENHANCED STRATEGY MANAGER WITH MARGIN TRADING (AFTER PROFIT SYSTEMS)
            try:
                from bybit_bot.strategies.strategy_manager import StrategyManager
                self.strategy_manager = StrategyManager(
                    config=self.config,
                    database_manager=self.database_manager,
                    bybit_client=self.bybit_client,
                    memory_manager=getattr(self, 'memory_manager', None),
                    advanced_memory=getattr(self, 'advanced_memory', None)
                )
                await self.strategy_manager.initialize()
            except Exception as e:
                print(f"ERROR: Strategy Manager initialization failed: {e}")
                self.strategy_manager = None

            # CRITICAL: ACTIVATE ALL DATA CRAWLERS FOR REAL STRATEGY FEEDING
            print("")
            try:
                if self.database_manager is not None:
                    # Market Data Crawler - REAL TIME PRICE/VOLUME DATA
                    from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
                    self.market_data_crawler = MarketDataCrawler(self.config, self.database_manager)
                    await self.market_data_crawler.initialize()
                    self.market_data_crawler_ready = True

                    # News Sentiment Crawler - REAL NEWS ANALYSIS
                    from bybit_bot.data_crawler.news_sentiment_crawler import NewsSentimentCrawler
                    self.news_sentiment_crawler = NewsSentimentCrawler(self.config, self.database_manager)
                    await self.news_sentiment_crawler.initialize()
                    self.news_sentiment_crawler_ready = True

                    # Social Sentiment Crawler - REAL SOCIAL MEDIA DATA
                    from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
                    self.social_sentiment_crawler = SocialSentimentCrawler(self.config, self.database_manager)
                    await self.social_sentiment_crawler.initialize()
                    self.social_sentiment_crawler_ready = True

                    # Economic Data Crawler - REAL ECONOMIC INDICATORS
                    from bybit_bot.data_crawler.economic_data_crawler import EconomicDataCrawler
                    self.economic_data_crawler = EconomicDataCrawler(self.config, self.database_manager)
                    await self.economic_data_crawler.initialize()
                    self.economic_data_crawler_ready = True

                    # Firecrawl Collector - REAL WEB SCRAPING
                    from bybit_bot.data_crawler.firecrawl_collector import FirecrawlDataCollector
                    self.firecrawl_collector = FirecrawlDataCollector(self.config, self.database_manager)
                    await self.firecrawl_collector.initialize()
                    self.firecrawl_collector_ready = True

                else:
                    print("ERROR: Database manager not available for data crawlers")

            except Exception as e:
                print(f"ERROR: Data crawler activation failed: {e}")
                import traceback
                traceback.print_exc()

            # CRITICAL: ACTIVATE ENHANCED MULTI-TIMEFRAME LEARNING SYSTEM
            print("")
            try:
                from bybit_bot.ai.enhanced_multi_timeframe_learner import EnhancedMultiTimeframeLearner
                from bybit_bot.ai.adaptive_learning_engine import AdaptiveLearningEngine
                from bybit_bot.ai.advanced_data_integration_engine import AdvancedDataIntegrationEngine

                # Initialize Enhanced Multi-Timeframe Learner
                self.enhanced_multi_timeframe_learner = EnhancedMultiTimeframeLearner(self.config)
                print("SUCCESS: Enhanced Multi-Timeframe Learner initialized")

                # Initialize Enhanced Adaptive Learning Engine
                self.enhanced_adaptive_learning_engine = AdaptiveLearningEngine()
                print("SUCCESS: Enhanced Adaptive Learning Engine initialized")

                # Initialize Advanced Data Integration Engine
                self.advanced_data_integration_engine = AdvancedDataIntegrationEngine(
                    config=self.config,
                    db_path=str(Path(__file__).parent.parent / "bybit_trading_bot.db")
                )
                print("SUCCESS: Advanced Data Integration Engine initialized")

                # TIER 1 OPTIMIZATIONS: Initialize CPU-efficient learning components
                from bybit_bot.ai.tier1_integration_manager import Tier1IntegrationManager

                self.tier1_integration_manager = Tier1IntegrationManager(
                    config=self.config,
                    db_path=str(Path(__file__).parent.parent / "bybit_trading_bot.db")
                )
                print("SUCCESS: Tier 1 Integration Manager initialized")

                # Set up integration between systems
                self.enhanced_adaptive_learning_engine.set_multi_timeframe_learner(self.enhanced_multi_timeframe_learner)
                self.enhanced_adaptive_learning_engine.set_data_integration_engine(self.advanced_data_integration_engine)

                # Integrate Tier 1 with enhanced learning
                self.tier1_integration_manager.set_enhanced_multi_timeframe_learner(self.enhanced_multi_timeframe_learner)

                # Mark enhanced learning systems as ready
                self.enhanced_learning_systems_ready = True
                self.tier1_systems_ready = True
                print("SUCCESS: Enhanced learning systems + Tier 1 optimizations integrated and ready")

                # CRITICAL: INITIALIZE ALL 8 OPTIMIZATION TIERS
                print("")
                print("INITIALIZING OPTIMIZATION SYSTEMS - ALL 8 TIERS")
                print("=" * 60)

                try:
                    from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig

                    # Create optimization configuration
                    optimization_config = OptimizationConfig(
                        enable_streaming=True,
                        enable_compression=True,
                        enable_multi_agent_rl=True,
                        enable_graph_nn=True,
                        enable_edge_computing=True,
                        enable_quantum=True,
                        enable_feature_engineering=True,
                        enable_adaptive_learning=True,
                        kafka_bootstrap_servers="localhost:9092",
                        compression_ratio_target=0.85,
                        num_agents=5,
                        num_graph_nodes=50,
                        edge_nodes=2,
                        num_qubits=8,
                        max_features=200,
                        model_ensemble_size=5
                    )

                    # Initialize optimization manager
                    self.optimization_manager = OptimizationComponentsManager(optimization_config)

                    # Initialize all optimization components
                    optimization_success = await self.optimization_manager.initialize_all_components()

                    if optimization_success:
                        self.optimization_active = True

                        # Store individual component references for easy access
                        self.streaming_processor = self.optimization_manager.components.get('streaming_processor')
                        self.compression_engine = self.optimization_manager.components.get('compression_engine')
                        self.multi_agent_rl = self.optimization_manager.components.get('multi_agent_rl')
                        self.graph_neural_networks = self.optimization_manager.components.get('graph_neural_networks')
                        self.edge_computing = self.optimization_manager.components.get('edge_computing')
                        self.quantum_engine = self.optimization_manager.components.get('quantum_engine')
                        self.feature_pipeline = self.optimization_manager.components.get('feature_pipeline')
                        self.adaptive_learning = self.optimization_manager.components.get('adaptive_learning')

                        print("SUCCESS: All 8 optimization tiers initialized and active")
                        print("  ✓ Tier 1: Advanced Streaming Data Architecture")
                        print("  ✓ Tier 2: Time Series Compression Engine")
                        print("  ✓ Tier 3: Multi-Agent Reinforcement Learning")
                        print("  ✓ Tier 4: Graph Neural Networks")
                        print("  ✓ Tier 5: Edge Computing Infrastructure")
                        print("  ✓ Tier 6: Quantum-Inspired Algorithms")
                        print("  ✓ Tier 7: Advanced Feature Engineering")
                        print("  ✓ Tier 8: Real-Time Model Adaptation")

                        # Update profit targets with optimization multiplier
                        self.profit_targets['optimization_active'] = True
                        self.profit_targets['optimization_multiplier'] = 2.5  # 2.5x improvement expected

                    else:
                        print("WARNING: Optimization systems partially initialized")
                        self.optimization_active = False

                except Exception as e:
                    print(f"ERROR: Optimization systems initialization failed: {e}")
                    import traceback
                    traceback.print_exc()
                    self.optimization_active = False
                    self.optimization_manager = None

            except Exception as e:
                print(f"ERROR: Enhanced Learning Systems initialization failed: {e}")
                import traceback
                traceback.print_exc()
                self.enhanced_learning_systems_ready = False

            # CRITICAL: ACTIVATE REAL-TIME LEARNING MONITOR FOR STRATEGY IMPROVEMENT
            print("")
            try:
                from bybit_bot.ai.real_time_learning_monitor import RealTimeLearningMonitor
                project_root = Path(__file__).parent.parent
                self.learning_monitor = RealTimeLearningMonitor(db_path=str(project_root / "bybit_trading_bot.db"))
                # Store learning monitor for later activation - NO IMMEDIATE ASYNC TASK
                self.learning_monitor_ready = True
            except Exception as e:
                print(f"ERROR: Real-Time Learning Monitor failed: {e}")
                self.learning_monitor = None

            print("")
            try:
                from bybit_bot.data_collector.comprehensive_bybit_data import ComprehensiveBybitDataCollector
                project_root = Path(__file__).parent.parent
                self.data_collector = ComprehensiveBybitDataCollector(
                    bybit_client=self.bybit_client,
                    db_path=str(project_root / "bybit_trading_bot.db")
                )
                # Store data collector for later activation - NO IMMEDIATE ASYNC TASK
                self.data_collector_ready = True
            except Exception as e:
                print(f"ERROR: Comprehensive Data Collector failed: {e}")
                self.data_collector = None

            # CRITICAL: ACTIVATE COMPREHENSIVE STRATEGY LEARNER - LEARNS FROM ALL DATA
            print("")
            try:
                from bybit_bot.ai.comprehensive_strategy_learner import ComprehensiveStrategyLearner
                project_root = Path(__file__).parent.parent
                self.strategy_learner = ComprehensiveStrategyLearner(db_path=str(project_root / "bybit_trading_bot.db"))
                # Store strategy learner for later activation - NO IMMEDIATE ASYNC TASK
                self.strategy_learner_ready = True

            except Exception as e:
                print(f"ERROR: Learning monitor activation failed: {e}")

            print("")
            try:
                from bybit_bot.profit_tracking.real_pnl_tracker import RealPnLTracker
                project_root = Path(__file__).parent.parent
                self.pnl_tracker = RealPnLTracker(self.bybit_client, db_path=str(project_root / "bybit_trading_bot.db"))
                # Store P&L tracker for later activation - NO IMMEDIATE ASYNC TASK
                self.pnl_tracker_ready = True

            except Exception as e:
                print(f"ERROR: P&L tracker activation failed: {e}")
                import traceback
                traceback.print_exc()

            print("")
            print("")

            # MPC (MULTI-PARTY COMPUTATION) SECURITY INTEGRATION - BYBIT KEYLESS WALLET
            print("INITIALIZING MPC SECURITY LAYER...")
            try:
                from bybit_bot.mcp.mpc_engine import MPCCryptographicEngine
                from bybit_bot.mcp.threshold_signatures import ThresholdSignatureManager
                from bybit_bot.mcp.bybit_mpc_wallet import BybitMPCWallet, MPCWalletConfig
                from bybit_bot.mcp.mpc_security_layer import MPCSecurityLayer, SecurityPolicy

                # MPC Configuration for Bybit integration
                mpc_config = MPCWalletConfig(
                    wallet_id=f"bybit_wallet_{int(time.time())}",
                    threshold=2,  # 2-of-3 threshold signature scheme
                    total_parties=3,
                    party_endpoints={
                        1: "localhost:8001",
                        2: "localhost:8002",
                        3: "localhost:8003"
                    },
                    api_credentials={
                        'bybit_api_key': getattr(self.config.api_keys.bybit, 'api_key', ''),
                        'bybit_secret': getattr(self.config.api_keys.bybit, 'secret', '')
                    },
                    security_level='ENTERPRISE'
                )

                # Initialize MPC components
                self.mpc_engine = MPCCryptographicEngine(party_id=1, threshold=2, total_parties=3)
                self.threshold_manager = ThresholdSignatureManager(party_id=1, threshold=2, total_parties=3)
                self.mpc_wallet = BybitMPCWallet(config=mpc_config, party_id=1)
                self.mpc_security = MPCSecurityLayer(party_id=1)

                # Initialize all MPC components
                await self.mpc_engine.initialize()
                await self.threshold_manager.initialize()
                await self.mpc_wallet.initialize()
                await self.mpc_security.initialize()

                # Store in components registry
                self.components['mpc_engine'] = self.mpc_engine
                self.components['threshold_manager'] = self.threshold_manager
                self.components['mpc_wallet'] = self.mpc_wallet
                self.components['mpc_security'] = self.mpc_security

                # Mark MPC as active
                self.mpc_active = True

                print("SUCCESS: MPC Security Layer initialized - Bybit Keyless Wallet active")
                logger.info("MPC Security Layer initialized with threshold signatures and distributed key management")

            except ImportError as import_error:
                logger.error(f"MPC components import failed: {import_error}")
                print(f"WARNING: Cannot import MPC components - {import_error}")
                print("SOLUTION: Check if bybit_bot.mcp modules exist and dependencies are installed")
                self.mpc_active = False
                self.mpc_engine = None
                self.threshold_manager = None
                self.mpc_wallet = None
                self.mpc_security = None
            except Exception as mpc_error:
                logger.error(f"MPC initialization failed: {mpc_error}")
                print(f"WARNING: MPC Security Layer initialization failed: {mpc_error}")
                import traceback
                print(f"TRACEBACK: {traceback.format_exc()}")
                self.mpc_active = False
                self.mpc_engine = None
                self.threshold_manager = None
                self.mpc_wallet = None
                self.mpc_security = None

            logger.info(f"{len(self.components)} system components initialized successfully")

            # COMPREHENSIVE SYSTEM STATUS REPORT
            print("\n" + "=" * 80)
            print("SYSTEM INITIALIZATION STATUS REPORT")
            print("=" * 80)

            # Core Components Status
            core_components = {
                'Configuration': hasattr(self, 'config') and self.config is not None,
                'Database Manager': hasattr(self, 'db_manager') and self.db_manager is not None,
                'Enhanced Bybit Client': hasattr(self, 'bybit_client') and self.bybit_client is not None,
            }

            print("CORE COMPONENTS:")
            for component, status in core_components.items():
                status_text = "ACTIVE" if status else "FAILED"
                print(f"  {component}: {status_text}")

            # AI Components Status
            ai_components = {
                'AI Activation Manager': hasattr(self, 'ai_manager') and self.ai_manager is not None,
                'Intelligent ML System': hasattr(self, 'intelligent_ml') and self.intelligent_ml is not None,
                'Meta Cognition Engine': hasattr(self, 'meta_cognition') and self.meta_cognition is not None,
                'Risk Manager': hasattr(self, 'risk_manager') and self.risk_manager is not None,
            }

            print("\nAI COMPONENTS:")
            for component, status in ai_components.items():
                status_text = "ACTIVE" if status else "FAILED"
                print(f"  {component}: {status_text}")

            # Profit Components Status
            profit_components = {
                'Ultra Profit Amplifier': hasattr(self, 'ultra_profit_amplifier') and self.ultra_profit_amplifier is not None,
                'Hyper Profit Engine': hasattr(self, 'hyper_profit_engine') and self.hyper_profit_engine is not None,
                'Advanced Profit Engine': hasattr(self, 'advanced_profit_engine') and self.advanced_profit_engine is not None,
                'P&L Tracker': hasattr(self, 'pnl_tracker') and self.pnl_tracker is not None,
            }

            print("\nPROFIT COMPONENTS:")
            for component, status in profit_components.items():
                status_text = "ACTIVE" if status else "FAILED"
                print(f"  {component}: {status_text}")

            # MPC Security Components Status
            mpc_components = {
                'MPC Cryptographic Engine': hasattr(self, 'mpc_engine') and self.mpc_engine is not None,
                'Threshold Signature Manager': hasattr(self, 'threshold_manager') and self.threshold_manager is not None,
                'Bybit MPC Wallet': hasattr(self, 'mpc_wallet') and self.mpc_wallet is not None,
                'MPC Security Layer': hasattr(self, 'mpc_security') and self.mpc_security is not None,
            }

            print("\nMPC SECURITY COMPONENTS:")
            for component, status in mpc_components.items():
                status_text = "ACTIVE" if status else "FAILED"
                print(f"  {component}: {status_text}")

            # Calculate success rate
            all_components = {**core_components, **ai_components, **profit_components, **mpc_components}
            active_count = sum(1 for status in all_components.values() if status)
            total_count = len(all_components)
            success_rate = (active_count / total_count) * 100

            print(f"\nOVERALL SUCCESS RATE: {active_count}/{total_count} ({success_rate:.1f}%)")
            print("=" * 80)

            # Always return True if we have core components (database, bybit_client)
            core_ready = core_components['Enhanced Bybit Client'] and core_components['Configuration']
            if core_ready:
                print("SUCCESS: Core trading infrastructure is ready")
                print("READY FOR LIVE TRADING OPERATIONS")
                return True
            else:
                print("ERROR: Core trading infrastructure missing")
                print("CANNOT START TRADING - FIX CORE COMPONENTS FIRST")
                return False

        except Exception as e:
            import traceback
            logger.error(f"Failed to initialize systems: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            print(f"ERROR: System initialization failed with: {e}")
            print(f"ERROR TYPE: {type(e).__name__}")

            # Check if we have core components despite the error
            if hasattr(self, 'db_manager') and hasattr(self, 'bybit_client'):
                print("WARNING: Some components failed but core trading infrastructure is ready")
                logger.warning("Continuing with basic trading functionality")
                return True
            else:
                print("ERROR: Core trading infrastructure failed to initialize")
                return False

    async def start_all_engines(self):
        """Start ALL trading engines and systems for MAXIMUM PROFIT"""
        logger.info("Starting ALL trading engines for ULTRA maximum profit...")
        self.is_running = True

        # Start REAL DATA API SERVER for frontend/mobile access
        try:
            from real_data_api_server import set_trading_bot_instance, start_real_data_api_server
            set_trading_bot_instance(self)
            asyncio.create_task(start_real_data_api_server())
            logger.info("Real Data API Server started - NO FAKE DATA")
        except Exception as e:
            logger.error(f"Failed to start Real Data API Server: {e}")

        # Start systems SEQUENTIALLY to prevent API rate limit overload
        logger.info("Starting trading engines sequentially to prevent API overload...")

        # Start ULTRA profit amplifier first (highest priority)
        ultra_amplifier_task = asyncio.create_task(self._run_ultra_profit_amplifier())
        logger.info("Ultra Profit Amplifier started")
        await asyncio.sleep(5)  # 5 second delay

        # Start profit target enforcer (profit tracking)
        profit_enforcer_task = asyncio.create_task(self._run_profit_target_enforcer())
        logger.info("Profit target enforcer started")
        await asyncio.sleep(5)  # 5 second delay

        # Start meta-cognition (no API calls)
        meta_task = asyncio.create_task(self._run_meta_cognition())
        logger.info("Meta-cognition engine started")
        await asyncio.sleep(10)  # 10 second delay

        # Start performance monitor (minimal API calls)
        monitor_task = asyncio.create_task(self._run_performance_monitor())
        logger.info("Performance monitor started")
        await asyncio.sleep(15)  # 15 second delay

        # Start enhanced time manager (minimal API calls)
        time_manager_task = asyncio.create_task(self._run_enhanced_time_manager())
        logger.info("Enhanced time manager started")
        await asyncio.sleep(10)  # 10 second delay

        # Start agent orchestrator (moderate API calls)
        agent_orchestrator_task = asyncio.create_task(self._run_agent_orchestrator())
        logger.info("Agent orchestrator started")
        await asyncio.sleep(15)  # 15 second delay

        # Start risk manager (some API calls)
        risk_task = asyncio.create_task(self._run_risk_manager())
        logger.info("Risk manager started")
        await asyncio.sleep(20)  # 20 second delay

        # Start strategy manager (moderate API calls)
        strategy_task = asyncio.create_task(self._run_strategy_manager())
        logger.info("Strategy manager started")
        await asyncio.sleep(25)  # 25 second delay

        # Start market predictor (heavy API calls)
        predictor_task = asyncio.create_task(self._run_market_predictor())
        logger.info("Market predictor started")
        await asyncio.sleep(30)  # 30 second delay

        # Start main trading loop (heavy API usage)
        main_task = asyncio.create_task(self._run_main_trading_loop())
        logger.info("Main trading loop started")
        await asyncio.sleep(35)  # 35 second delay

        # Start advanced profit engine (very heavy API usage)
        advanced_task = asyncio.create_task(self._run_advanced_profit_engine())
        logger.info("Advanced profit engine started")
        await asyncio.sleep(40)  # 40 second delay

        # Start hyper profit engine last (heaviest API usage)
        hyper_task = asyncio.create_task(self._run_hyper_profit_engine())
        logger.info("Hyper profit engine started")

        # PROFIT ACCELERATION: Start WebSocket streaming monitoring
        websocket_task = asyncio.create_task(self._run_websocket_streaming_monitor())
        logger.info("PROFIT ACCELERATION: WebSocket streaming monitor started")

        # MPC SECURITY: Start MPC security monitoring
        mpc_task = None
        if hasattr(self, 'mpc_active') and self.mpc_active:
            mpc_task = asyncio.create_task(self._run_mpc_security_monitor())
            logger.info("MPC SECURITY: Multi-Party Computation security monitor started")
            await asyncio.sleep(5)  # 5 second delay

        tasks = [ultra_amplifier_task, profit_enforcer_task, meta_task, monitor_task, time_manager_task, agent_orchestrator_task, risk_task, strategy_task, predictor_task, main_task, advanced_task, hyper_task, websocket_task]
        if mpc_task:
            tasks.append(mpc_task)

        # Run all engines concurrently
        await asyncio.gather(*tasks, return_exceptions=True)

    async def _run_ultra_profit_amplifier(self):
        """Run the ultra profit amplification engine"""
        try:
            if self.ultra_profit_amplifier:
                logger.info("Starting Ultra Profit Amplification Engine...")
                await self.ultra_profit_amplifier.start_amplification_engine()
            else:
                logger.warning("Ultra Profit Amplifier not available")
                while self.is_running:
                    await asyncio.sleep(60)  # Keep task alive
        except Exception as e:
            logger.error(f"Ultra Profit Amplifier error: {e}")

    async def _run_profit_target_enforcer(self):
        """Run the profit target enforcement engine"""
        try:
            if self.profit_target_enforcer:
                logger.info("Starting Profit Target Enforcer...")
                await self.profit_target_enforcer.start()
            else:
                logger.warning("Profit Target Enforcer not available")
                while self.is_running:
                    await asyncio.sleep(60)  # Keep task alive
        except Exception as e:
            logger.error(f"Profit Target Enforcer error: {e}")

    async def _run_hyper_profit_engine(self):
        """Run the hyper profit maximization engine"""
        try:
            if self.hyper_profit_engine:
                logger.info("Starting Hyper Profit Engine...")
                await self.hyper_profit_engine.start()
            else:
                logger.info("Hyper Profit Engine not available - skipping")
                while self.is_running:
                    await asyncio.sleep(60)  # Keep task alive
        except Exception as e:
            logger.error(f"Hyper Profit Engine error: {e}")

    async def _run_websocket_streaming_monitor(self):
        """
        Monitor WebSocket streaming performance and health
        PROFIT ACCELERATION: Ensure optimal streaming performance
        """
        try:
            logger.info("PROFIT ACCELERATION: Starting WebSocket streaming monitor...")

            # Wait for strategy manager to initialize
            await asyncio.sleep(10)

            while self.is_running:
                try:
                    # Get WebSocket performance metrics
                    if self.strategy_manager:
                        metrics = self.strategy_manager.get_websocket_performance_metrics()

                        if metrics.get('websocket_streaming_enabled'):
                            # Log performance metrics every 5 minutes
                            if metrics.get('messages_received', 0) > 0:
                                logger.info(
                                    f"PROFIT ACCELERATION WebSocket Performance: "
                                    f"Messages: {metrics.get('messages_received', 0)}, "
                                    f"Latency: {metrics.get('average_latency_ms', 0):.2f}ms, "
                                    f"MPS: {metrics.get('messages_per_second', 0):.1f}, "
                                    f"Symbols: {len(metrics.get('subscribed_symbols', []))}"
                                )

                            # Check for performance issues
                            avg_latency = metrics.get('average_latency_ms', 0)
                            if avg_latency > 50:  # Alert if latency > 50ms
                                logger.warning(
                                    f"PROFIT ACCELERATION: High WebSocket latency detected: {avg_latency:.2f}ms"
                                )

                            # Check connection health
                            if not metrics.get('is_connected', False):
                                logger.error("PROFIT ACCELERATION: WebSocket connection lost - attempting reconnection")
                                # Strategy manager will handle reconnection automatically
                        else:
                            logger.warning("PROFIT ACCELERATION: WebSocket streaming not enabled")

                    # Monitor every 5 minutes
                    await asyncio.sleep(300)

                except Exception as e:
                    logger.error(f"Error in WebSocket streaming monitor: {e}")
                    await asyncio.sleep(60)  # Shorter retry interval on error

        except Exception as e:
            logger.error(f"WebSocket streaming monitor error: {e}")
            # Keep task alive even on error
            while self.is_running:
                await asyncio.sleep(300)

    async def _run_advanced_profit_engine(self):
        """Run the advanced profit engine"""
        try:
            if self.advanced_profit_engine:
                logger.info("Starting Advanced Profit Engine...")
                await self.advanced_profit_engine.start()
            else:
                logger.info("Advanced Profit Engine not available - skipping")
                while self.is_running:
                    await asyncio.sleep(60)  # Keep task alive
        except Exception as e:
            logger.error(f"Advanced Profit Engine error: {e}")

    async def _run_market_predictor(self):
        """Run the ML market predictor"""
        try:
            logger.info("Starting ML Market Predictor...")
            if self.market_predictor:
                await self.market_predictor.start()
            else:
                logger.warning("Market Predictor not initialized")
        except Exception as e:
            logger.error(f"Market Predictor error: {e}")

    async def _run_strategy_manager(self):
        """Run the strategy manager"""
        try:
            logger.info("Starting Strategy Manager...")
            if self.strategy_manager:
                await self.strategy_manager.start()
            else:
                logger.warning("Strategy Manager not initialized")
        except Exception as e:
            logger.error(f"Strategy Manager error: {e}")

    async def _run_enhanced_time_manager(self):
        """Run the enhanced time manager"""
        try:
            logger.info("Starting Enhanced Time Manager...")
            if self.enhanced_time_manager:
                await self.enhanced_time_manager.start_time_awareness()
            else:
                logger.warning("Enhanced Time Manager not initialized")
                while self.is_running:
                    await asyncio.sleep(60)  # Keep task alive
        except Exception as e:
            logger.error(f"Enhanced Time Manager error: {e}")

    async def _run_agent_orchestrator(self):
        """Run the agent orchestrator"""
        try:
            logger.info("Starting Agent Orchestrator...")
            if self.agent_orchestrator:
                await self.agent_orchestrator.start_orchestration()
            else:
                logger.warning("Agent Orchestrator not initialized")
                while self.is_running:
                    await asyncio.sleep(60)  # Keep task alive
        except Exception as e:
            logger.error(f"Agent Orchestrator error: {e}")

    async def _run_risk_manager(self):
        """Run the risk manager"""
        try:
            logger.info("Starting Risk Manager...")
            if self.risk_manager:
                await self.risk_manager.initialize()
                logger.info("Risk Manager initialized successfully")
            else:
                logger.error("Risk Manager is None - check initialization")
        except Exception as e:
            logger.error(f"Risk Manager error: {e}")

    async def _run_meta_cognition(self):
        """Run the AI meta-cognition engine"""
        try:
            logger.info("Starting Meta-Cognition Engine...")
            if self.meta_cognition:
                await self.meta_cognition.start()
            else:
                logger.info("Meta-cognition not available - running basic self-monitoring")
                # Basic self-monitoring loop without pandas dependencies
                while self.is_running:
                    await asyncio.sleep(30)  # Monitor every 30 seconds
                    logger.info("BASIC SELF-MONITORING: System operational")
        except Exception as e:
            logger.error(f"Meta-Cognition Engine error: {e}")

    async def _run_performance_monitor(self):
        """Run the performance monitoring system"""
        try:
            logger.info("Starting Performance Monitor...")
            while self.is_running:
                # Monitor system performance
                if self.performance_analyzer:
                    await self.performance_analyzer.analyze_performance()
                await asyncio.sleep(60)  # Monitor every minute
        except Exception as e:
            logger.error(f"Performance Monitor error: {e}")

    async def run_trading_loop(self):
        """Public alias for main trading loop"""
        return await self._run_main_trading_loop()

    async def _run_main_trading_loop(self):
        """Main trading loop that coordinates all activities with REAL TRADING"""

        # ACTIVATE ALL BACKGROUND TASKS NOW THAT EVENT LOOP IS RUNNING

        # Activate comprehensive data collector
        if hasattr(self, 'data_collector') and self.data_collector and hasattr(self, 'data_collector_ready'):
            try:
                asyncio.create_task(self.data_collector.start_comprehensive_collection())
            except Exception as e:
                print(f"ERROR: Failed to start data collector: {e}")

        # Activate enhanced learning systems
        if hasattr(self, 'enhanced_learning_systems_ready') and self.enhanced_learning_systems_ready:
            try:
                # Initialize advanced data integration engine
                await self.advanced_data_integration_engine.initialize()
                print("SUCCESS: Advanced Data Integration Engine activated")
            except Exception as e:
                print(f"ERROR: Failed to activate advanced data integration: {e}")

        # Activate Tier 1 optimization systems
        if hasattr(self, 'tier1_systems_ready') and self.tier1_systems_ready:
            try:
                await self.tier1_integration_manager.initialize()
                print("SUCCESS: Tier 1 Integration Manager activated")
            except Exception as e:
                print(f"ERROR: Failed to activate Tier 1 systems: {e}")

        # Activate comprehensive strategy learner
        if hasattr(self, 'strategy_learner') and self.strategy_learner and hasattr(self, 'strategy_learner_ready'):
            try:
                asyncio.create_task(self.strategy_learner.start_comprehensive_learning())
            except Exception as e:
                print(f"ERROR: Failed to start strategy learner: {e}")

        # Activate learning monitor
        if hasattr(self, 'learning_monitor') and self.learning_monitor and hasattr(self, 'learning_monitor_ready'):
            try:
                asyncio.create_task(self.learning_monitor.start_monitoring())
            except Exception as e:
                print(f"ERROR: Failed to start learning monitor: {e}")

        # Activate P&L tracker
        if hasattr(self, 'pnl_tracker') and self.pnl_tracker and hasattr(self, 'pnl_tracker_ready'):
            try:
                asyncio.create_task(self.pnl_tracker.start_tracking())
            except Exception as e:
                print(f"ERROR: Failed to start P&L tracker: {e}")

        # Activate all data crawlers
        if hasattr(self, 'market_data_crawler') and self.market_data_crawler and hasattr(self, 'market_data_crawler_ready'):
            try:
                asyncio.create_task(self.market_data_crawler.start())
            except Exception as e:
                print(f"ERROR: Failed to start market data crawler: {e}")

        if hasattr(self, 'news_sentiment_crawler') and self.news_sentiment_crawler and hasattr(self, 'news_sentiment_crawler_ready'):
            try:
                asyncio.create_task(self.news_sentiment_crawler.start())
            except Exception as e:
                print(f"ERROR: Failed to start news sentiment crawler: {e}")

        if hasattr(self, 'social_sentiment_crawler') and self.social_sentiment_crawler and hasattr(self, 'social_sentiment_crawler_ready'):
            try:
                asyncio.create_task(self.social_sentiment_crawler.start())
            except Exception as e:
                print(f"ERROR: Failed to start social sentiment crawler: {e}")

        if hasattr(self, 'economic_data_crawler') and self.economic_data_crawler and hasattr(self, 'economic_data_crawler_ready'):
            try:
                asyncio.create_task(self.economic_data_crawler.start())
            except Exception as e:
                print(f"ERROR: Failed to start economic data crawler: {e}")

        if hasattr(self, 'firecrawl_collector') and self.firecrawl_collector and hasattr(self, 'firecrawl_collector_ready'):
            try:
                asyncio.create_task(self.firecrawl_collector.start_continuous_crawling())
            except Exception as e:
                print(f"ERROR: Failed to start firecrawl collector: {e}")

        # Activate AI manager monitoring
        if hasattr(self, 'ai_manager') and self.ai_manager and hasattr(self, 'ai_manager_ready'):
            try:
                asyncio.create_task(self.ai_manager.start_continuous_monitoring())
            except Exception as e:
                print(f"ERROR: Failed to start AI manager monitoring: {e}")

        trade_count = 0
        last_balance_check: float = 0.0

        while self.is_running:
            try:
                # CRITICAL: Check if rate limiter is in emergency mode
                from bybit_bot.utils.global_rate_limiter import rate_limiter
                if rate_limiter.emergency_mode:
                    logger.error("CRITICAL: Rate limiter in emergency mode - ENTERING HOLD MODE")

                    # Calculate remaining emergency time
                    emergency_remaining = rate_limiter.config.emergency_delay - (time.time() - rate_limiter.last_error_time)

                    # ENTER HOLD MODE - Continue monitoring but no trading
                    if not self.hold_mode:
                        self.hold_mode = True
                        self.hold_start_time = time.time()
                        self.hold_duration = emergency_remaining

                    if emergency_remaining > 0:
                        logger.info(f"Rate limit cooldown: {emergency_remaining:.0f} seconds remaining")

                        # AGGRESSIVE MODE: Continue trading with reduced frequency instead of full HOLD
                        await asyncio.sleep(min(30, emergency_remaining))  # Shorter wait time for more aggressive trading
                    else:
                        # EXIT COOLDOWN and resume full trading
                        rate_limiter.emergency_mode = False
                        self.hold_mode = False
                        logger.info("Exiting cooldown - resuming full trading operations")

                        # EXECUTE HIGH-PRIORITY OPPORTUNITIES FOUND DURING HOLD
                        await self._execute_hold_opportunities()

                        await asyncio.sleep(30)  # Wait 30 seconds before resuming

                # Get current account balance every 30 seconds
                current_time = time.time()
                if current_time - last_balance_check > 30:
                    try:
                        total_equity = 0.0  # Initialize variable before try block
                        balance_data = await self.bybit_client.get_account_balance()
                        if balance_data and 'result' in balance_data and 'list' in balance_data['result']:
                            account_info = balance_data['result']['list'][0]
                            total_equity = float(account_info.get('totalEquity', 0))
                            available_balance = float(account_info.get('totalAvailableBalance', 0))
                            logger.info(f"LIVE ACCOUNT: Total Equity: ${total_equity:.2f} | Available: ${available_balance:.2f}")
                        last_balance_check = current_time

                        # Update session stats with current balance
                        if self.session_stats['start_balance'] == 0:
                            self.session_stats['start_balance'] = total_equity
                        self.session_stats['current_balance'] = total_equity

                    except Exception as e:
                        logger.error(f"Error checking account balance: {e}")

                # Check for HOURLY profit report and target tracking
                current_datetime = datetime.now(timezone.utc)
                if (current_datetime - self.last_hourly_report).total_seconds() >= 3600:  # 1 hour = 3600 seconds
                    await self.generate_hourly_profit_summary()
                    self.last_hourly_report = current_datetime

                # Check for 4-hour profit report
                if (current_datetime - self.last_4hour_report).total_seconds() >= 14400:  # 4 hours = 14400 seconds
                    await self.generate_4hour_profit_report()
                    self.last_4hour_report = current_datetime

                # Skip trading if rate limiter is in emergency mode
                if rate_limiter.emergency_mode:
                    await asyncio.sleep(5)  # Short wait before next iteration
                    continue

                # ULTRA CONSERVATIVE MARGIN SAFETY SYSTEM
                # Initialize account_info variable at the start of each trading cycle
                account_info = None
                margin_ratio = 0.0
                total_equity = 0.0
                margin_balance = 0.0
                available_margin = 0.0

                if self.margin_safety_manager:
                    try:
                        await self.margin_safety_manager.check_margin_safety()

                        # Check if trading is allowed
                        if not self.margin_safety_manager.is_trading_allowed():
                            logger.warning("Trading suspended due to margin safety concerns")
                            await asyncio.sleep(30)
                            continue

                        # Get current margin status for display
                        margin_status = await self.margin_safety_manager.get_margin_status()
                        if margin_status:

                            # Store margin info for position sizing
                            margin_ratio = margin_status.margin_ratio
                            total_equity = margin_status.total_equity
                            margin_balance = margin_status.margin_balance
                            available_margin = margin_status.available_balance

                            # Create account_info dict for compatibility with existing code
                            account_info = {
                                'marginRatio': margin_ratio * 100,  # Convert back to percentage for compatibility
                                'totalEquity': total_equity,
                                'totalMarginBalance': margin_balance,
                                'totalInitialMargin': margin_balance - available_margin,
                                'availableMargin': available_margin
                            }
                        else:
                            await asyncio.sleep(30)
                            continue

                    except Exception as e:
                        logger.error(f"Margin Safety Manager error: {e}")
                        await asyncio.sleep(30)
                        continue
                else:
                    # Fallback to basic margin check if safety manager not available
                    try:
                        wallet_data = await self.bybit_client.get_wallet_balance("UNIFIED")
                        if wallet_data and 'list' in wallet_data:
                            account_info = wallet_data['list'][0]
                            margin_ratio = float(account_info.get('marginRatio', 100)) / 100  # Convert to decimal
                            total_equity = float(account_info.get('totalEquity', 0))
                            margin_balance = float(account_info.get('totalMarginBalance', 0))
                            available_margin = margin_balance - float(account_info.get('totalInitialMargin', 0))

                            if margin_ratio > 0.5:  # 50% margin ratio limit
                                await asyncio.sleep(30)
                                continue
                        else:
                            await asyncio.sleep(30)
                            continue
                    except Exception as e:
                        logger.error(f"Basic margin check failed: {e}")
                        await asyncio.sleep(30)
                        continue

                # Ensure account_info is available for the rest of the trading loop
                if account_info is None:
                    logger.error("account_info variable not properly initialized")
                    await asyncio.sleep(30)
                    continue

                # DERIVATIVES TRADING STRATEGY: Focus on major pairs with leverage
                derivatives_trading_pairs = ["BTC/USDT", "ETH/USDT"]  # Major pairs for DERIVATIVES with leverage

                # Process pairs for DERIVATIVES trading opportunities (WITH LEVERAGE)
                for symbol in derivatives_trading_pairs:
                    try:
                        # Get current market data
                        market_data = await self.bybit_client.get_market_data(symbol, "1", 5)

                        # OPTIMIZATION PROCESSING: Process market data through all 8 optimization tiers
                        optimization_results = {}
                        if self.optimization_active and self.optimization_manager and market_data:
                            try:
                                # Prepare market data for optimization processing
                                optimization_input = {
                                    'symbol': symbol,
                                    'ohlcv': {
                                        'open': float(market_data[-1].get('open', 0)),
                                        'high': float(market_data[-1].get('high', 0)),
                                        'low': float(market_data[-1].get('low', 0)),
                                        'close': float(market_data[-1].get('close', 0)),
                                        'volume': float(market_data[-1].get('volume', 0))
                                    },
                                    'timestamp': time.time(),
                                    'historical_data': market_data
                                }

                                # Process through optimization pipeline
                                optimization_results = await self.optimization_manager.process_market_data(optimization_input)

                                # Log optimization performance
                                if optimization_results.get('processing_time', 0) < 0.1:  # Less than 100ms
                                    logger.debug(f"Optimization processing: {symbol} - {optimization_results.get('processing_time', 0):.3f}s")

                            except Exception as e:
                                logger.warning(f"Optimization processing failed for {symbol}: {e}")
                                optimization_results = {}

                        if market_data and len(market_data) >= 2:
                            current_price = float(market_data[-1]['close'])
                            prev_price = float(market_data[-2]['close'])
                            price_change = (current_price - prev_price) / prev_price

                            # Debug: Print price changes

                            # COMPREHENSIVE ML ANALYSIS - ALL COMPONENTS ACTIVE
                            ml_signal = None
                            market_prediction = None
                            risk_assessment = None
                            sentiment_data = None
                            strategy_signals = []
                            platform_opportunities = []

                            # 1. HUGGINGFACE MANAGER - REAL SENTIMENT & MARKET DATA
                            if self.huggingface_manager:
                                try:
                                    sentiment_data = await self.huggingface_manager.get_market_sentiment_data()
                                    if sentiment_data and sentiment_data.get('overall_sentiment'):
                                        logger.info(f"Market sentiment for {symbol}: {sentiment_data['overall_sentiment']:.3f}")
                                except Exception as e:
                                    logger.warning(f"HuggingFace sentiment analysis error: {e}")

                            # 2. INTELLIGENT ML SYSTEM
                            if self.intelligent_ml and account_info:
                                await self.intelligent_ml.update_margin_status(account_info.get('marginRatio', 0))
                                ml_signal = await self.intelligent_ml.analyze_market_data(symbol, {
                                    'price': current_price,
                                    'volume': market_data[-1].get('volume', 0),
                                    'price_change': price_change,
                                    'sentiment': sentiment_data.get('overall_sentiment', 0.5) if sentiment_data else 0.5
                                })

                                # Store market data for analysis
                                if hasattr(self, 'market_data_history'):
                                    self.market_data_history[symbol] = market_data[-10:]  # Keep last 10 candles

                            # 3. ML MARKET PREDICTOR - REAL CALCULATIONS
                            if self.market_predictor:
                                try:
                                    # Use the real ML market predictor methods
                                    market_prediction = await self.market_predictor.make_ensemble_prediction(symbol)
                                    if market_prediction:
                                        logger.info(f"ML prediction for {symbol}: {market_prediction}")
                                except Exception as e:
                                    logger.warning(f"ML Market Predictor error: {e}")
                                    market_prediction = None

                            # 3. ADVANCED RISK MANAGER
                            if self.risk_manager and account_info:
                                try:
                                    risk_assessment = await self.risk_manager.assess_risk(symbol, {
                                        'price': current_price,
                                        'volume': float(market_data[-1].get('volume', 0)) if market_data else 0,
                                        'market_volatility': abs(price_change)
                                    }, account_info)

                                    # Store risk assessment for trading decisions
                                    if hasattr(self, 'risk_assessments'):
                                        self.risk_assessments[symbol] = risk_assessment
                                except Exception as e:
                                    print(f"Risk Manager error: {e}")

                            # 4. STRATEGY MANAGER WITH MARGIN TRADING
                            if self.strategy_manager:
                                try:
                                    # Update strategy weights from profit target enforcer
                                    if self.profit_target_enforcer:
                                        enforcer_weights = self.profit_target_enforcer.get_strategy_weights()
                                        await self.strategy_manager.update_strategy_weights_from_enforcer(enforcer_weights)

                                    strategy_signals = await self.strategy_manager.generate_signals([symbol])
                                    if strategy_signals:
                                        best_signal = max(strategy_signals, key=lambda s: s.confidence * s.strength)

                                        # Store best strategy signal for trading decision
                                        if hasattr(self, 'strategy_signals'):
                                            self.strategy_signals[symbol] = best_signal
                                except Exception as e:
                                    print(f"Strategy Manager error: {e}")

                            # 5. META-COGNITION ENGINE
                            if self.meta_cognition:
                                try:
                                    await self.meta_cognition.analyze_decision_context({
                                        'ml_signal': ml_signal,
                                        'market_prediction': market_prediction,
                                        'risk_assessment': risk_assessment,
                                        'strategy_signals': strategy_signals,
                                        'account_info': account_info,
                                        'market_data': market_data[-5:]
                                    })

                                    # Store risk assessment for decision making
                                    if hasattr(self, 'risk_assessments'):
                                        self.risk_assessments[symbol] = risk_assessment
                                except Exception as e:
                                    print(f"Meta-Cognition error: {e}")

                            # 6. PLATFORM RISK-TO-PROFIT ANALYSIS
                            if self.platform_risk_converter and account_info:
                                try:
                                    platform_opportunities = await self.platform_risk_converter.analyze_platform_risks({
                                        'price': current_price,
                                        'volume': market_data[-1].get('volume', 0),
                                        'price_change': price_change,
                                        'funding_rate': 0.0001,  # Would get real funding rate
                                        'timestamp': time.time()
                                    }, account_info)

                                    if platform_opportunities:
                                        best_opportunity = max(platform_opportunities, key=lambda x: x.profit_potential * x.confidence)

                                        # Execute high-confidence opportunities
                                        if best_opportunity.confidence > 0.8 and best_opportunity.profit_potential > 100:
                                            if self.platform_exploit_strategies:
                                                if best_opportunity.risk_type.value == "funding_rate_manipulation":
                                                    profit = await self.platform_exploit_strategies.execute_funding_rate_arbitrage(best_opportunity)
                                                    if profit > 0:
                                                        # TRACK PROFIT FOR TARGET ENFORCEMENT
                                                        if self.profit_target_enforcer:
                                                            await self.profit_target_enforcer.track_profit(profit, "funding_rate_arbitrage")
                                                elif best_opportunity.risk_type.value == "liquidation_cascade":
                                                    profit = await self.platform_exploit_strategies.execute_liquidation_cascade_strategy(best_opportunity)
                                                    if profit > 0:
                                                        # TRACK PROFIT FOR TARGET ENFORCEMENT
                                                        if self.profit_target_enforcer:
                                                            await self.profit_target_enforcer.track_profit(profit, "liquidation_cascade")
                                                elif best_opportunity.risk_type.value == "margin_call_timing":
                                                    profit = await self.platform_exploit_strategies.execute_margin_call_front_running(best_opportunity)
                                                    if profit > 0:
                                                        # TRACK PROFIT FOR TARGET ENFORCEMENT
                                                        if self.profit_target_enforcer:
                                                            await self.profit_target_enforcer.track_profit(profit, "margin_call_front_running")
                                except Exception as e:
                                    print(f"Platform Risk Analysis error: {e}")

                            # INTELLIGENT PROFIT STRATEGY: Adapt to margin levels
                            # account_info is now guaranteed to be available from margin safety check above
                            current_margin_ratio = account_info.get('marginRatio', 0)

                            # ULTRA-AGGRESSIVE THRESHOLD FOR MAXIMUM PROFIT
                            if current_margin_ratio > 95:  # EXTREME RISK - Still aggressive
                                threshold = 0.001  # 0.1% movement required - ULTRA-AGGRESSIVE
                            elif current_margin_ratio > 90:  # HIGH RISK - Aggressive
                                threshold = 0.0005  # 0.05% movement required - ULTRA-AGGRESSIVE
                            elif current_margin_ratio > 80:  # MODERATE RISK - Very aggressive
                                threshold = 0.0003  # 0.03% movement required - ULTRA-AGGRESSIVE
                                print(f"LOW RISK THRESHOLD: {threshold*100}% movement required")
                            else:  # SAFE ZONE - More aggressive
                                threshold = 0.001  # 0.1% movement required
                                print(f"SAFE ZONE THRESHOLD: {threshold*100}% movement required")

                            # SOPHISTICATED ML-DRIVEN TRADING DECISION
                            should_trade = False
                            trade_reason = ""
                            side = "Buy"

                            # Calculate estimated profit for position sizing (needed for all trading decisions)
                            estimated_profit = abs(price_change) * 100  # Basic profit estimation
                            confidence_score = 0.0
                            position_size_multiplier = 1.0

                            optimal_params = {}

                            # TIER 1 OPTIMIZATION: Get optimized parameters from Tier 1 system
                            tier1_params = {}
                            if hasattr(self, 'tier1_optimized_params') and symbol in self.tier1_optimized_params:
                                tier1_params = self.tier1_optimized_params[symbol]
                                position_size_multiplier = tier1_params.get('position_size_multiplier', position_size_multiplier)
                                print(f"TIER 1 OPTIMIZATION: {symbol} -> Size Multiplier={position_size_multiplier:.2f} (from Tier 1 learning)")

                            # STRATEGY LEARNER: Get additional optimization parameters
                            if hasattr(self, 'strategy_learner') and self.strategy_learner:
                                try:
                                    current_conditions = {
                                        'price_24h_pcnt': price_change,
                                        'volume_24h': float(market_data[-1].get('volume', 0)) if market_data else 0,
                                        'current_price': current_price,
                                        'timestamp': datetime.now(timezone.utc)
                                    }
                                    optimal_params = await self.strategy_learner.get_optimal_strategy_parameters(
                                        symbol, current_conditions
                                    )
                                    if optimal_params:
                                        # Combine with Tier 1 parameters (Tier 1 takes priority)
                                        if not tier1_params:
                                            position_size_multiplier = optimal_params.get('position_size_multiplier', position_size_multiplier)
                                            print(f"STRATEGY LEARNER: {symbol} -> Size Multiplier={position_size_multiplier:.2f} (from strategy learner)")
                                        else:
                                            print(f"STRATEGY LEARNER: {symbol} -> Using Tier 1 optimized parameters (priority)")
                                except Exception as e:
                                    print(f"Strategy learner error: {e}")
                                    optimal_params = {}

                            # Merge all optimization parameters
                            final_optimization_params = {**optimal_params, **tier1_params}

                            # ENSEMBLE ML DECISION MAKING
                            ml_votes: List[Dict[str, Any]] = []

                            # Vote 0: OPTIMIZATION SYSTEMS (NEW - HIGHEST PRIORITY)
                            if optimization_results and self.optimization_active:
                                try:
                                    # Extract optimization signals
                                    optimization_confidence = 0.0
                                    optimization_action = 'hold'

                                    # Multi-Agent RL signal
                                    if optimization_results.get('rl_action'):
                                        rl_action = optimization_results['rl_action']
                                        if rl_action in ['buy', 'sell']:
                                            optimization_action = rl_action
                                            optimization_confidence += 0.3

                                    # Quantum prediction signal
                                    if optimization_results.get('quantum_prediction'):
                                        quantum_pred = optimization_results['quantum_prediction']
                                        if quantum_pred > 0.6:
                                            optimization_action = 'buy'
                                            optimization_confidence += 0.2
                                        elif quantum_pred < 0.4:
                                            optimization_action = 'sell'
                                            optimization_confidence += 0.2

                                    # Feature engineering enhancement
                                    if optimization_results.get('num_features', 0) > 100:
                                        optimization_confidence += 0.1  # Bonus for rich feature set

                                    # Compression efficiency bonus
                                    if optimization_results.get('compression_ratio', 0) > 0.8:
                                        optimization_confidence += 0.05  # Bonus for efficient data processing

                                    # Add optimization vote if confidence is sufficient
                                    if optimization_confidence > 0.4 and optimization_action != 'hold':
                                        ml_votes.append({
                                            'action': optimization_action,
                                            'confidence': min(optimization_confidence, 0.95),  # Cap at 95%
                                            'weight': 2.0,  # High weight for optimization systems
                                            'source': 'Optimization-8Tiers',
                                            'details': {
                                                'processing_time': optimization_results.get('processing_time', 0),
                                                'features_used': optimization_results.get('num_features', 0),
                                                'compression_ratio': optimization_results.get('compression_ratio', 0),
                                                'quantum_prediction': optimization_results.get('quantum_prediction', 0),
                                                'rl_action': optimization_results.get('rl_action', 'hold')
                                            }
                                        })
                                        print(f"OPTIMIZATION VOTE: {optimization_action.upper()} | Confidence: {optimization_confidence:.3f} | Features: {optimization_results.get('num_features', 0)}")

                                except Exception as e:
                                    logger.warning(f"Optimization voting failed: {e}")

                            # Vote 1: Intelligent ML System
                            if ml_signal and ml_signal.confidence > 0.6 and ml_signal.signal_type != 'hold':
                                ml_votes.append({
                                    'action': ml_signal.signal_type,
                                    'confidence': ml_signal.confidence,
                                    'weight': 0.25,
                                    'source': 'IntelligentML'
                                })

                            # Vote 2: Market Predictor
                            if market_prediction and market_prediction.confidence > 0.6:
                                prediction_action = 'buy' if market_prediction.direction == 'up' else 'sell'
                                ml_votes.append({
                                    'action': prediction_action,
                                    'confidence': market_prediction.confidence,
                                    'weight': 0.25,
                                    'source': 'MarketPredictor'
                                })

                            # Vote 3: Strategy Manager
                            if strategy_signals:
                                best_strategy = max(strategy_signals, key=lambda s: s.confidence * s.strength)
                                if best_strategy.confidence > 0.6:
                                    ml_votes.append({
                                        'action': best_strategy.action.lower(),
                                        'confidence': best_strategy.confidence,
                                        'weight': 0.35,
                                        'source': f'Strategy-{best_strategy.strategy}'
                                    })

                            # Vote 4: Memory-Based Strategy Recommendation
                            if hasattr(self, 'strategy_manager') and self.strategy_manager:
                                try:
                                    # Define market_conditions based on current market data
                                    volume_24h = float(market_data[-1].get('volume', 0)) if market_data else 0
                                    market_conditions = {
                                        'price': current_price,
                                        'volume': volume_24h,
                                        'price_change': price_change,
                                        'volatility': abs(price_change),
                                        'trend': 'bullish' if price_change > 0 else 'bearish' if price_change < 0 else 'neutral',
                                        'regime': 'trending' if abs(price_change) > 0.02 else 'ranging',
                                        'timestamp': time.time()
                                    }

                                    memory_recommendation = await self.strategy_manager.get_memory_based_strategy_recommendation(
                                        symbol, market_conditions
                                    )
                                    if memory_recommendation and memory_recommendation['confidence'] > 0.6:
                                        # Determine action based on recommended strategy
                                        strategy_name = memory_recommendation['recommended_strategy']
                                        expected_profit = memory_recommendation['expected_profit']

                                        # Map strategy to action (simplified mapping)
                                        action = 'buy' if expected_profit > 0 else 'sell'
                                        if 'short' in strategy_name.lower() or 'sell' in strategy_name.lower():
                                            action = 'sell'
                                        elif 'long' in strategy_name.lower() or 'buy' in strategy_name.lower():
                                            action = 'buy'

                                        ml_votes.append({
                                            'action': action,
                                            'confidence': memory_recommendation['confidence'],
                                            'weight': 0.25,  # High weight for memory-based decisions
                                            'source': f'Memory-{strategy_name}',
                                            'expected_profit': expected_profit,
                                            'supporting_memories': memory_recommendation['supporting_memories']
                                        })

                                        print(f"MEMORY RECOMMENDATION: {strategy_name} -> {action.upper()} "
                                              f"(confidence: {memory_recommendation['confidence']:.2f}, "
                                              f"expected profit: {expected_profit:.2f}, "
                                              f"memories: {memory_recommendation['supporting_memories']})")

                                except Exception as e:
                                    logger.error(f"Error getting memory-based recommendation: {e}")

                            # Vote 5: Risk Assessment Override
                            if risk_assessment and risk_assessment.risk_level == 'high':
                                # High risk - reduce position or avoid trading
                                position_size_multiplier *= 0.3
                                print(f"RISK OVERRIDE: High risk detected - reducing position size by 70%")
                            elif risk_assessment and risk_assessment.risk_level == 'low':
                                # Low risk - can increase position
                                position_size_multiplier *= 1.2
                                print(f"RISK ENHANCEMENT: Low risk detected - increasing position size by 20%")

                            # ENSEMBLE VOTING SYSTEM
                            if ml_votes:
                                buy_score = sum(vote['confidence'] * vote['weight'] for vote in ml_votes if vote['action'] == 'buy')
                                sell_score = sum(vote['confidence'] * vote['weight'] for vote in ml_votes if vote['action'] == 'sell')

                                confidence_score = max(buy_score, sell_score)

                                # ACCOUNT PROTECTION: Check daily loss limit before trading
                                daily_loss_limit = -5.0  # Maximum $5 loss per day
                                if self.session_stats['total_profit'] <= daily_loss_limit:
                                    logger.warning(f"DAILY LOSS LIMIT REACHED: ${self.session_stats['total_profit']:.2f} <= ${daily_loss_limit}")
                                    continue  # Skip trading for this symbol

                                if confidence_score > 0.75:  # RAISED confidence threshold to prevent losses
                                    should_trade = True
                                    side = "Buy" if buy_score > sell_score else "Sell"

                                    # Build comprehensive trade reason
                                    vote_sources = [vote['source'] for vote in ml_votes]
                                    trade_reason = f"ML Ensemble: {side} (confidence: {confidence_score:.2f}) - Sources: {', '.join(vote_sources)}"

                                    print(f"ENSEMBLE DECISION: {side} | Confidence: {confidence_score:.2f} | Votes: {len(ml_votes)}")
                                else:
                                    print(f"ENSEMBLE DECISION: HOLD | Confidence too low: {confidence_score:.2f}")

                            # Fallback to basic price movement if no ML signals
                            elif abs(price_change) > threshold:
                                should_trade = True
                                side = "Buy" if price_change > 0 else "Sell"
                                confidence_score = min(0.7, abs(price_change) * 100)
                                trade_reason = f"Price Movement: {price_change*100:.4f}% > {threshold*100:.4f}% threshold"
                                estimated_profit = abs(price_change) * 100  # Basic profit estimation
                                print(f"FALLBACK DECISION: {side} | Price movement: {price_change*100:.4f}%")

                            if should_trade:
                                try:
                                    print(f"TRADING DECISION: {trade_reason}")

                                    # ADVANCED ML-DRIVEN POSITION SIZING
                                    margin_ratio = account_info.get('marginRatio', 0)
                                    total_equity = account_info.get('totalEquity', 0)
                                    margin_balance = account_info.get('totalMarginBalance', 0)
                                    available_margin = margin_balance - account_info.get('totalInitialMargin', 0)

                                    # DERIVATIVES TRADING POSITION SIZING WITH LEVERAGE
                                    # Use actual balance: 114.57 EUR = ~$125 USD

                                    # ULTRA CONSERVATIVE POSITION SIZING WITH MARGIN SAFETY MANAGER
                                    if self.margin_safety_manager:
                                        # Get maximum allowed position value from margin safety manager
                                        max_allowed_position = self.margin_safety_manager.get_max_position_value()
                                        recommended_leverage = self.margin_safety_manager.get_recommended_leverage()

                                        if risk_assessment and hasattr(risk_assessment, 'max_position_size'):
                                            base_position_usd = min(risk_assessment.max_position_size, max_allowed_position)
                                        else:
                                            # ULTRA CONSERVATIVE POSITION SIZING - REDUCED FOR LOSS PREVENTION
                                            base_position_usd = min(max_allowed_position, 5.0)  # Max $5 position to prevent losses

                                        # Use recommended leverage from margin safety manager
                                        leverage_multiplier = float(recommended_leverage)
                                        print(f"MARGIN SAFE LEVERAGE: {leverage_multiplier}x")

                                    else:
                                        # Fallback to very conservative sizing if margin safety manager not available
                                        base_position_usd = 2.0  # EXTREMELY conservative $2 maximum to prevent losses
                                        leverage_multiplier = 1.0  # No leverage

                                    # Apply ML confidence and position size multipliers
                                    max_position_usd = base_position_usd * position_size_multiplier * confidence_score

                                    # MINIMUM $0.1 TRADE ENFORCEMENT - NO SMALLER TRADES
                                    min_trade_usd = 0.10  # Absolute minimum $0.1 per trade
                                    estimated_profit = abs(price_change) * max_position_usd  # Basic profit estimation

                                    if max_position_usd < min_trade_usd:
                                        max_position_usd = min_trade_usd

                                    # BYBIT MINIMUM ORDER VALUE ENFORCEMENT
                                    if symbol == "BTC/USDT":
                                        min_order_usd = current_price * 0.001  # 0.001 BTC minimum (~$118)
                                    elif symbol == "ETH/USDT":
                                        min_order_usd = current_price * 0.01   # 0.01 ETH minimum (~$38)
                                    else:
                                        min_order_usd = 10.0  # $10 minimum for other symbols

                                    if max_position_usd < min_order_usd:
                                        max_position_usd = min_order_usd

                                    # Calculate initial quantity
                                    quantity = max_position_usd / current_price

                                    # Apply REAL Bybit minimum requirements
                                    if symbol == "BTC/USDT":
                                        min_quantity = 0.001  # 0.001 BTC minimum
                                        qty_precision = 5
                                        min_notional = 1.0
                                    elif symbol == "ETH/USDT":
                                        min_quantity = 0.01   # 0.01 ETH minimum
                                        qty_precision = 4
                                        min_notional = 1.0
                                    elif symbol == "SOLUSDT":
                                        min_quantity = 0.1    # 0.1 SOL minimum
                                        qty_precision = 3
                                        min_notional = 1.0
                                    else:
                                        min_quantity = 1.0    # Default minimum
                                        qty_precision = 6
                                        min_notional = 1.0

                                    # Ensure minimum quantity
                                    if quantity < min_quantity:
                                        quantity = min_quantity
                                        print(f"ADJUSTED TO MINIMUM QTY: {quantity} {symbol.replace('USDT', '')}")

                                    # Ensure minimum notional value
                                    notional_value = quantity * current_price
                                    if notional_value < min_notional:
                                        quantity = min_notional / current_price

                                    # Round to correct precision
                                    quantity = round(quantity, qty_precision)

                                    position_size_usd = quantity * current_price

                                    # REAL BYBIT V5 API ORDER EXECUTION WITH MPC SECURITY - NO FAKE DATA
                                    # MPC SECURITY: Secure order execution with threshold signatures
                                    order_result = None
                                    if hasattr(self, 'mpc_active') and self.mpc_active and self.mpc_wallet:
                                        try:
                                            # Create secure order transaction with MPC
                                            order_data = {
                                                'symbol': symbol,
                                                'side': side,
                                                'order_type': "Market",
                                                'qty': str(quantity),
                                                'category': "linear",
                                                'timestamp': time.time(),
                                                'party_id': 1
                                            }

                                            # Execute order through MPC wallet with distributed signing
                                            order_result = await self.mpc_wallet.execute_secure_order(order_data)

                                            if order_result:
                                                logger.info(f"MPC SECURE ORDER EXECUTED: {symbol} {side} | Qty: {quantity} | MPC Wallet ID: {order_result.get('mpc_transaction_id', 'N/A')}")
                                            else:
                                                # Fallback to regular order if MPC fails
                                                logger.warning("MPC order execution failed, falling back to regular order")
                                                order_result = await self.bybit_client.place_order(
                                                    symbol=symbol,
                                                    side=side,
                                                    order_type="Market",
                                                    qty=str(quantity),
                                                    category="linear"
                                                )
                                        except Exception as mpc_error:
                                            logger.error(f"MPC order execution error: {mpc_error}")
                                            # Fallback to regular order
                                            order_result = await self.bybit_client.place_order(
                                                symbol=symbol,
                                                side=side,
                                                order_type="Market",
                                                qty=str(quantity),
                                                category="linear"
                                            )
                                    else:
                                        # Regular order execution if MPC not available
                                        order_result = await self.bybit_client.place_order(
                                            symbol=symbol,
                                            side=side,
                                            order_type="Market",
                                            qty=str(quantity),
                                            category="linear"
                                        )

                                    # REAL TRADING OPERATION - LIVE DATA ONLY

                                    if order_result:
                                        trade_count += 1

                                        # REAL PROFIT TRACKING SYSTEM - NO FAKE DATA
                                        async def get_real_profit_from_position(symbol: str, order_id: str) -> float:
                                            """Get REAL profit from Bybit API position data"""
                                            try:
                                                positions = await self.bybit_client.get_positions(symbol=symbol)
                                                if positions and 'list' in positions:
                                                    for position in positions['list']:
                                                        if (position.get('symbol') == symbol and
                                                            float(position.get('size', 0)) > 0):
                                                            unrealized_pnl = float(position.get('unrealisedPnl', 0))
                                                            logger.info(f"REAL P&L FROM BYBIT API: {symbol} = ${unrealized_pnl:.4f}")
                                                            return unrealized_pnl
                                                return 0.0
                                            except Exception as e:
                                                logger.error(f"Error getting real P&L: {e}")
                                                return 0.0

                                        # Get REAL profit from Bybit API
                                        real_profit = await get_real_profit_from_position(symbol, order_result.get('orderId', ''))

                                        # Update session stats with REAL profit only
                                        if real_profit != 0.0:
                                            real_trade_result = {
                                                'profit': real_profit,
                                                'symbol': symbol,
                                                'order_id': order_result.get('orderId', ''),
                                                'real_api_data': True
                                            }
                                            await self.update_session_stats(real_trade_result)

                                        # TRACK REAL PROFIT ONLY - NO FAKE DATA
                                        if self.ultra_profit_amplifier and real_profit != 0.0:
                                            await self.ultra_profit_amplifier.track_profit(
                                                profit_amount=real_profit,
                                                strategy_name="main_trading",
                                                confidence=confidence_score
                                            )

                                        # TRACK REAL PROFIT FOR TARGET ENFORCEMENT - NO FAKE DATA
                                        if self.profit_target_enforcer and real_profit != 0.0:
                                            await self.profit_target_enforcer.track_profit(real_profit, "main_trading")

                                        # REAL LOGGING - LIVE TRADING ONLY

                                        logger.info(f"REAL TRADE EXECUTED: {symbol} {side} | Size: ${position_size_usd:.2f} | Qty: {quantity:.6f} | Price: ${current_price:.4f} | Order ID: {order_result}")

                                        # REAL POSITION MONITORING - Track actual Bybit position
                                        if order_result.get('orderId'):
                                            # Store order for real P&L tracking
                                            self.active_orders[order_result['orderId']] = {
                                                'symbol': symbol,
                                                'side': side,
                                                'quantity': quantity,
                                                'entry_price': current_price,
                                                'order_id': order_result['orderId'],
                                                'timestamp': time.time(),
                                                'stop_loss': stop_loss_price,
                                                'take_profit': take_profit_price
                                            }

                                            # Schedule real P&L update in 30 seconds
                                            asyncio.create_task(self._update_real_pnl_delayed(order_result['orderId'], 30))

                                        # Update session statistics - REAL TRADE DATA ONLY
                                        trade_result = {
                                            'profit': 0.0,  # Will be updated with real P&L from Bybit API
                                            'strategy': 'main_trading',
                                            'symbol': symbol,
                                            'side': side,
                                            'quantity': quantity,
                                            'price': current_price,
                                            'position_size_usd': position_size_usd,
                                            'order_id': order_result.get('orderId', 'unknown')
                                        }
                                        await self.update_session_stats(trade_result)

                                        # COMPREHENSIVE ML LEARNING - ALL COMPONENTS LEARN + TIER 1 OPTIMIZATIONS
                                        trade_record = {
                                            'symbol': symbol,
                                            'side': side,
                                            'quantity': quantity,
                                            'price': current_price,
                                            'exit_price': current_price,  # Will be updated when position closes
                                            'position_size_usd': position_size_usd,
                                            'margin_ratio': margin_ratio,
                                            'real_profit': real_profit,
                                            'profit_loss': real_profit,
                                            'confidence_score': confidence_score,
                                            'trade_reason': trade_reason,
                                            'timestamp': time.time(),
                                            'market_data': market_data[-5:],  # Last 5 candles
                                            'account_info': account_info,
                                            'strategy': 'ensemble_voting',
                                            'strategy_parameters': {
                                                'position_size_multiplier': position_size_multiplier,
                                                'confidence_threshold': confidence_score,
                                                'margin_ratio': margin_ratio
                                            },
                                            'market_conditions': {
                                                'volatility': abs(price_change) / 100,
                                                'volume_ratio': float(market_data[-1].get('volume', 0)) / max(float(market_data[-2].get('volume', 1)), 1) if len(market_data) > 1 else 1.0,
                                                'price_change_24h': price_change
                                            },
                                            'environmental_context': {}
                                        }

                                        # Create environmental context for enhanced learning
                                        if hasattr(self, 'advanced_data_integration_engine') and self.advanced_data_integration_engine:
                                            try:
                                                env_context = await self.advanced_data_integration_engine.create_environmental_context(symbol)
                                                trade_record['environmental_context'] = env_context
                                            except Exception as e:
                                                print(f"WARNING: Failed to create environmental context: {e}")

                                        # TIER 1 LEARNING: Process through Tier 1 optimization pipeline
                                        if hasattr(self, 'tier1_integration_manager') and self.tier1_integration_manager:
                                            try:
                                                tier1_result = await self.tier1_integration_manager.process_trade_outcome(trade_record)

                                                # Apply optimized parameters for future trades
                                                if tier1_result.optimized_parameters:
                                                    print(f"TIER 1 OPTIMIZATION: {symbol} -> Improvement: {tier1_result.performance_improvement:.2f}%, Confidence: {tier1_result.learning_confidence:.3f}")

                                                    # Store optimized parameters for next trade
                                                    if not hasattr(self, 'tier1_optimized_params'):
                                                        self.tier1_optimized_params = {}
                                                    self.tier1_optimized_params[symbol] = tier1_result.optimized_parameters

                                            except Exception as e:
                                                print(f"WARNING: Tier 1 learning failed: {e}")

                                        # ENHANCED MULTI-TIMEFRAME LEARNING: Process through enhanced learning system
                                        if hasattr(self, 'enhanced_multi_timeframe_learner') and self.enhanced_multi_timeframe_learner:
                                            try:
                                                await self.enhanced_multi_timeframe_learner.learn_from_trade_with_environment(
                                                    symbol=symbol,
                                                    profit_loss=real_profit,
                                                    environmental_context=trade_record.get('environmental_context', {}),
                                                    strategy_parameters=trade_record.get('strategy_parameters', {})
                                                )
                                                print(f"ENHANCED LEARNING: Multi-timeframe learning applied for {symbol}")
                                            except Exception as e:
                                                print(f"WARNING: Enhanced multi-timeframe learning failed: {e}")

                                        # 1. Intelligent ML System Learning
                                        if self.intelligent_ml and ml_signal:
                                            trade_record['predicted_return'] = ml_signal.expected_return
                                            trade_record['ml_confidence'] = ml_signal.confidence
                                            trade_record['strategy'] = 'intelligent_ml'
                                            await self.intelligent_ml.learn_from_trade(trade_record)

                                        # 2. Market Predictor Learning
                                        if self.market_predictor and market_prediction:
                                            prediction_record = trade_record.copy()
                                            prediction_record['predicted_price'] = market_prediction.predicted_price
                                            prediction_record['prediction_confidence'] = market_prediction.confidence
                                            prediction_record['prediction_direction'] = market_prediction.direction
                                            await self.market_predictor.learn_from_prediction(
                                                symbol=symbol,
                                                actual_price=current_price,
                                                prediction_time=time.time()
                                            )
                                            print(f"MARKET PREDICTOR: Prediction outcome recorded")

                                        # 3. Risk Manager Learning
                                        if self.risk_manager and risk_assessment:
                                            risk_record = trade_record.copy()
                                            risk_record['risk_level'] = risk_assessment.risk_level
                                            risk_record['recommended_size'] = risk_assessment.max_position_size
                                            await self.risk_manager.learn_from_outcome(symbol, risk_record)

                                        # 4. Strategy Manager Learning
                                        if self.strategy_manager and strategy_signals:
                                            for signal in strategy_signals:
                                                strategy_record = trade_record.copy()
                                                strategy_record['strategy_name'] = signal.strategy
                                                strategy_record['signal_confidence'] = signal.confidence
                                                strategy_record['signal_strength'] = signal.strength
                                                await self.strategy_manager.learn_from_strategy_outcome(signal.strategy, strategy_record)
                                            print(f"STRATEGY MANAGER: {len(strategy_signals)} strategy outcomes recorded")

                                        # 5. Performance Analyzer Learning
                                        if self.performance_analyzer:
                                            await self.performance_analyzer.record_trade(trade_record)
                                            print(f"PERFORMANCE ANALYZER: Trade performance recorded")

                                        # 6. Meta-Cognition Learning
                                        if self.meta_cognition:
                                            meta_record = trade_record.copy()
                                            meta_record['ml_votes'] = ml_votes
                                            meta_record['ensemble_confidence'] = confidence_score
                                            await self.meta_cognition.learn_from_decision_outcome(meta_record)
                                            print(f"META-COGNITION: Decision outcome recorded for self-reflection")

                                        # 7. Advanced Memory System Learning
                                        if hasattr(self, 'strategy_manager') and self.strategy_manager:
                                            try:
                                                # Determine strategy used (from memory recommendation or best strategy)
                                                strategy_used = "ensemble_voting"
                                                memory_vote = next((vote for vote in ml_votes if 'Memory-' in vote['source']), None)
                                                if memory_vote:
                                                    strategy_used = memory_vote['source'].replace('Memory-', '')
                                                elif strategy_signals:
                                                    best_strategy = max(strategy_signals, key=lambda s: s.confidence * s.strength)
                                                    strategy_used = best_strategy.strategy

                                                # Store outcome in memory with REAL profit data
                                                outcome = {
                                                    'success': real_profit > 0,
                                                    'profit': real_profit,
                                                    'confidence_achieved': confidence_score,
                                                    'positions': {'symbol': symbol, 'side': side, 'quantity': quantity},
                                                    'parameters': {'position_size_usd': position_size_usd, 'price': current_price},
                                                    'performance': {'profit': real_profit, 'trade_count': trade_count}
                                                }

                                                await self.strategy_manager.store_strategy_outcome(
                                                    symbol=symbol,
                                                    strategy_used=strategy_used,
                                                    action_taken=side.lower(),
                                                    outcome=outcome,
                                                    market_conditions=market_conditions
                                                )
                                                print(f"MEMORY SYSTEM: Strategy outcome stored for {strategy_used}")

                                            except Exception as e:
                                                logger.error(f"Error storing strategy outcome in memory: {e}")

                                        # Log milestones
                                        if trade_count % 10 == 0:
                                            # REAL LIVE TRADING MILESTONE - NO FAKE PROFIT LOGGING

                                            # Display ML performance metrics
                                            if self.intelligent_ml:
                                                ml_metrics = self.intelligent_ml.get_performance_metrics()
                                                print(f"ML PERFORMANCE: Accuracy: {ml_metrics.get('prediction_accuracy', {})}, Trades Learned: {ml_metrics.get('total_trades_learned', 0)}")

                                            # Display profit target status
                                            if self.profit_target_enforcer:
                                                await self.profit_target_enforcer.get_current_metrics()

                                except Exception as e:
                                    logger.error(f"Error placing real order for {symbol}: {e}")

                    except Exception as e:
                        logger.error(f"Error processing {symbol}: {e}")
                        continue

                # Real trading cycle - 5 seconds to allow for order processing
                await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"Main trading loop error: {e}")
                print(f"TRADING LOOP ERROR: {e}")
                await asyncio.sleep(30)

    async def _monitor_opportunities_during_hold(self):
        """Monitor and analyze opportunities during HOLD mode without executing trades"""
        try:

            # Get market data for analysis
            symbol = "BTC/USDT"
            market_data = await self.bybit_client.get_market_data(symbol, "1", 10)
            if not market_data:
                return

            current_price = float(market_data[-1]['close'])

            # Get account info for risk analysis
            account_info = await self.bybit_client.get_account_info()
            if not account_info:
                return

            # PLATFORM RISK ANALYSIS DURING HOLD
            if self.platform_risk_converter:
                try:
                    platform_opportunities = await self.platform_risk_converter.analyze_platform_risks({
                        'price': current_price,
                        'volume': market_data[-1].get('volume', 0),
                        'funding_rate': 0.0001,  # Would get real funding rate
                        'timestamp': time.time()
                    }, account_info)

                    if platform_opportunities:
                        for opp in platform_opportunities:
                            if opp.confidence > 0.7:  # High confidence opportunities
                                self.opportunities_found_during_hold.append({
                                    'opportunity': opp,
                                    'timestamp': time.time(),
                                    'price': current_price,
                                    'status': 'identified_during_hold'
                                })

                except Exception as e:
                    print(f"Platform risk analysis during hold error: {e}")

            # ML ANALYSIS DURING HOLD
            if self.intelligent_ml:
                try:
                    ml_signal = await self.intelligent_ml.analyze_market_data(symbol, {
                        'price': current_price,
                        'volume': float(market_data[-1].get('volume', 0)) if market_data else 0,
                        'timestamp': time.time()
                    })
                    if ml_signal and ml_signal.confidence > 0.8:
                        self.recovery_opportunities.append({
                            'signal': ml_signal,
                            'timestamp': time.time(),
                            'price': current_price,
                            'status': 'ml_signal_during_hold'
                        })
                        print(f"HOLD ML SIGNAL: {ml_signal.signal_type} | Confidence: {ml_signal.confidence:.2f}")

                except Exception as e:
                    print(f"ML analysis during hold error: {e}")

            # STRATEGY ANALYSIS DURING HOLD
            if self.strategy_manager:
                try:
                    strategy_signals = await self.strategy_manager.generate_signals([symbol])
                    for signal in strategy_signals:
                        if signal.confidence > 0.8:
                            self.recovery_opportunities.append({
                                'signal': signal,
                                'timestamp': time.time(),
                                'price': current_price,
                                'status': 'strategy_signal_during_hold'
                            })
                            print(f"HOLD STRATEGY SIGNAL: {signal.strategy} | Action: {signal.action} | Confidence: {signal.confidence:.2f}")

                except Exception as e:
                    print(f"Strategy analysis during hold error: {e}")

        except Exception as e:
            print(f"Error during hold monitoring: {e}")

    async def _execute_hold_opportunities(self):
        """Execute high-priority opportunities found during HOLD mode"""
        try:

            # Execute platform risk opportunities first (highest priority)
            executed_count = 0
            total_profit = 0.0

            for opp_data in self.opportunities_found_during_hold:
                if executed_count >= 3:  # Limit to 3 opportunities to avoid overexposure
                    break

                opportunity = opp_data['opportunity']
                if opportunity.confidence > 0.8 and opportunity.profit_potential > 100:
                    try:
                        if self.platform_exploit_strategies:
                            profit = 0.0

                            if opportunity.risk_type.value == "funding_rate_manipulation":
                                profit = await self.platform_exploit_strategies.execute_funding_rate_arbitrage(opportunity)
                            elif opportunity.risk_type.value == "liquidation_cascade":
                                profit = await self.platform_exploit_strategies.execute_liquidation_cascade_strategy(opportunity)
                            elif opportunity.risk_type.value == "margin_call_timing":
                                profit = await self.platform_exploit_strategies.execute_margin_call_front_running(opportunity)
                            elif opportunity.risk_type.value == "cross_margin_vulnerability":
                                profit = await self.platform_exploit_strategies.execute_cross_margin_correlation_exploit(opportunity)

                            if profit > 0:
                                total_profit += profit
                                executed_count += 1
                                self.profit_accumulated_during_hold += profit

                                # TRACK PROFIT FOR TARGET ENFORCEMENT
                                if self.profit_target_enforcer:
                                    await self.profit_target_enforcer.track_profit(profit, f"hold_{opportunity.risk_type.value}")

                                # Record the successful execution
                                if self.platform_risk_converter:
                                    self.platform_risk_converter.record_profit_from_opportunity(opportunity, profit)

                    except Exception as e:
                        print(f"Error executing hold opportunity: {e}")

            # Execute recovery signals (ML and strategy signals)
            for signal_data in self.recovery_opportunities[:2]:  # Limit to 2 recovery trades
                if executed_count >= 5:  # Total limit of 5 trades
                    break

                try:
                    signal = signal_data['signal']
                    if hasattr(signal, 'confidence') and signal.confidence > 0.8:
                        # Execute recovery trade based on signal
                        symbol = "BTC/USDT"

                        # Get current account info for position sizing
                        account_info = await self.bybit_client.get_account_info()
                        if account_info:
                            margin_ratio = float(account_info.get('marginRatio', 0))
                            available_margin = float(account_info.get('availableMargin', 0))

                            # Conservative position sizing for recovery trades
                            if margin_ratio < 70:  # Safe margin level
                                position_size_usd = min(available_margin * 0.05, 500)  # 5% of available margin, max $500

                                if hasattr(signal, 'signal_type'):
                                    side = "Buy" if signal.signal_type == "buy" else "Sell"
                                elif hasattr(signal, 'action'):
                                    side = "Buy" if signal.action.lower() == "buy" else "Sell"
                                else:
                                    continue

                                # Execute recovery trade
                                order_result = await self.bybit_client.place_order(
                                    symbol=symbol,
                                    side=side,
                                    order_type="Market",
                                    qty=str(position_size_usd),
                                    category="spot"
                                )
                                if order_result:
                                    executed_count += 1

                except Exception as e:
                    print(f"Error executing recovery signal: {e}")

            # Clear the opportunities lists after execution
            self.opportunities_found_during_hold.clear()
            self.recovery_opportunities.clear()

        except Exception as e:
            print(f"Error executing hold opportunities: {e}")

    async def generate_4hour_profit_report(self):
        """Generate brief 4-hour profit report"""
        try:
            current_time = datetime.now(timezone.utc)
            runtime_hours = (current_time - self.start_time).total_seconds() / 3600

            # Get current balance if possible
            current_balance = self.session_stats['current_balance']
            if self.bybit_client:
                try:
                    account_info = await self.bybit_client.get_account_info()
                    if account_info and 'totalEquity' in account_info:
                        current_balance = float(account_info['totalEquity'])
                        self.session_stats['current_balance'] = current_balance
                except Exception as e:
                    self.logger.warning(f"Could not get account balance: {e}")
                    # Continue with last known balance

            profit_since_start = current_balance - self.session_stats['start_balance']
            profit_per_hour = profit_since_start / max(runtime_hours, 0.01)

            print("=" * 60)
            print("=" * 60)
            # REAL RUNTIME AND TRADING STATS - NO FAKE PROFIT LOGGING
            print("=" * 60)

            # REAL 4H REPORT - NO FAKE PROFIT LOGGING

        except Exception as e:
            logger.error(f"Error generating 4-hour report: {e}")

    async def generate_final_detailed_report(self):
        """Generate comprehensive final report"""
        try:
            current_time = datetime.now(timezone.utc)
            total_runtime = (current_time - self.start_time).total_seconds()
            runtime_hours = total_runtime / 3600
            runtime_days = total_runtime / 86400

            # Get final balance
            final_balance = self.session_stats['current_balance']
            if self.bybit_client:
                try:
                    account_info = await self.bybit_client.get_account_info()
                    if account_info and 'totalEquity' in account_info:
                        final_balance = float(account_info['totalEquity'])
                except Exception as e:
                    self.logger.warning(f"Could not get final account balance: {e}")
                    # Use last known balance for final calculation

            total_profit = final_balance - self.session_stats['start_balance']
            profit_per_hour = total_profit / max(runtime_hours, 0.01)
            profit_per_day = total_profit / max(runtime_days, 0.01)

            print("\n" + "=" * 80)
            print("FINAL DETAILED TRADING SESSION REPORT")
            print("=" * 80)
            print(f"Session Duration: {runtime_hours:.2f} hours ({runtime_days:.3f} days)")
            print(f"Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"End Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 80)
            # REAL FINANCIAL PERFORMANCE - NO FAKE PROFIT LOGGING
            print("-" * 80)
            print("TRADING STATISTICS:")
            print(f"  Total Trades: {self.session_stats['total_trades']}")
            print(f"  Successful Trades: {self.session_stats['successful_trades']}")
            print(f"  Failed Trades: {self.session_stats['failed_trades']}")
            success_rate = (self.session_stats['successful_trades'] / max(self.session_stats['total_trades'], 1)) * 100
            print(f"  Success Rate: {success_rate:.2f}%")
            print("-" * 80)
            print("STRATEGIES PERFORMANCE:")
            for strategy, count in self.session_stats['strategies_used'].items():
                print(f"  {strategy}: {count} trades")
            print("-" * 80)
            print("SYMBOLS TRADED:")
            for symbol in sorted(self.session_stats['symbols_traded']):
                print(f"  {symbol}")
            print("-" * 80)
            print("PERFORMANCE TARGETS:")
            target_daily = 45000
            target_hourly = 1875
            print("=" * 80)

            # Log the summary
            logger.info("=" * 80)
            logger.info("FINAL SESSION REPORT")
            # REAL FINAL SESSION REPORT - NO FAKE PROFIT LOGGING
            logger.info("=" * 80)

        except Exception as e:
            logger.error(f"Error generating final report: {e}")

    async def _update_real_pnl_delayed(self, order_id: str, delay_seconds: int) -> None:
        """Update real P&L from Bybit API after delay to allow order execution"""
        try:
            await asyncio.sleep(delay_seconds)

            if order_id not in self.active_orders:
                return

            order_info = self.active_orders[order_id]
            symbol = order_info['symbol']

            # Get real position data from Bybit
            positions = await self.bybit_client.get_positions(symbol=symbol)

            if positions and 'list' in positions:
                for position in positions['list']:
                    if position.get('symbol') == symbol and float(position.get('size', 0)) > 0:
                        # Found active position - get real unrealized P&L
                        unrealized_pnl = float(position.get('unrealisedPnl', 0))

                        # Update session stats with REAL P&L
                        real_trade_result = {
                            'profit': unrealized_pnl,
                            'symbol': symbol,
                            'order_id': order_id,
                            'real_pnl_update': True
                        }
                        await self.update_session_stats(real_trade_result)

                        logger.info(f"REAL P&L UPDATE: {symbol} Order {order_id} = ${unrealized_pnl:.4f}")

                        # Update order tracking
                        order_info['real_pnl'] = unrealized_pnl
                        order_info['last_update'] = time.time()
                        break

        except Exception as e:
            logger.error(f"Error updating real P&L for order {order_id}: {e}")

    async def update_session_stats(self, trade_result):
        """Update session statistics with trade result and hourly profit tracking"""
        try:
            self.session_stats['total_trades'] += 1
            self.total_trades += 1

            # Update hourly trade count
            self.hourly_stats['current_hour_trades'] += 1

            if 'profit' in trade_result:
                profit = float(trade_result['profit'])
                self.session_stats['total_profit'] += profit
                self.total_profit += profit

                # Update hourly profit tracking
                self.hourly_stats['current_hour_profit'] += profit

                if profit > 0:
                    self.session_stats['successful_trades'] += 1
                    self.session_stats['max_profit'] = max(self.session_stats['max_profit'], profit)
                else:
                    self.session_stats['failed_trades'] += 1
                    self.session_stats['max_loss'] = min(self.session_stats['max_loss'], profit)

                # Update profit velocity and target performance in real-time
                runtime_minutes = (datetime.now(timezone.utc) - self.start_time).total_seconds() / 60
                if runtime_minutes > 0:
                    self.session_stats['profit_velocity'] = self.total_profit / runtime_minutes
                    self.session_stats['target_performance'] = (self.session_stats['profit_velocity'] / self.profit_targets['profit_per_minute']) * 100

                # Update ML performance multiplier based on target achievement
                if self.profit_targets['target_achievement_rate'] > 0:
                    if self.profit_targets['target_achievement_rate'] >= 120:  # Exceeding target by 20%
                        self.profit_targets['performance_multiplier'] = 1.2
                    elif self.profit_targets['target_achievement_rate'] >= 100:  # Meeting target
                        self.profit_targets['performance_multiplier'] = 1.0
                    elif self.profit_targets['target_achievement_rate'] >= 80:  # 80% of target
                        self.profit_targets['performance_multiplier'] = 0.9
                    else:  # Below 80% of target
                        self.profit_targets['performance_multiplier'] = 0.8

                # Log profit update with target tracking
                # REAL PROFIT UPDATE - NO FAKE PROFIT LOGGING

            if 'strategy' in trade_result:
                strategy = trade_result['strategy']
                self.session_stats['strategies_used'][strategy] = self.session_stats['strategies_used'].get(strategy, 0) + 1

            if 'symbol' in trade_result:
                self.session_stats['symbols_traded'].add(trade_result['symbol'])

        except Exception as e:
            logger.error(f"Error updating session stats: {e}")

    async def run(self):
        """Main run method"""
        try:
            logger.info("=" * 80)
            logger.info("BYBIT TRADING BOT - MAXIMUM PROFIT MODE ACTIVATED")
            logger.info("=" * 80)
            # REAL TARGET LOGGING - NO FAKE PROFIT LOGGING
            logger.info("=" * 80)

            # Initialize all systems
            if not await self.initialize_all_systems():
                logger.error("System initialization failed. Exiting.")
                print("ERROR: System initialization failed!")
                return False

            logger.info("Starting all trading engines - CONTINUOUS OPERATION MODE")

            # Start all trading engines and keep running
            await self.start_all_engines()

            # If we reach here, it means all engines stopped (shouldn't happen in normal operation)
            logger.warning("All trading engines stopped unexpectedly")
            print("WARNING: All trading engines stopped unexpectedly")

        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
            print("SHUTDOWN: User requested shutdown")
            self.is_running = False
        except Exception as e:
            logger.error(f"System error: {e}")
            print(f"FATAL ERROR: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            print("SHUTTING DOWN SYSTEM...")
            await self.shutdown()

    async def shutdown(self):
        """Graceful shutdown of all systems"""
        logger.info("Shutting down all systems...")
        self.is_running = False

        # Shutdown Performance Scaling System first
        if hasattr(self, 'performance_scaling_system') and self.performance_scaling_system:
            try:
                await self.performance_scaling_system.shutdown()
                logger.info("Performance Scaling System shutdown complete")
            except Exception as e:
                logger.error(f"Error shutting down Performance Scaling System: {e}")

        # Close all components with proper session cleanup
        for name, component in self.components.items():
            try:
                if hasattr(component, 'close'):
                    await component.close()
                elif hasattr(component, 'stop'):
                    await component.stop()
                elif hasattr(component, 'shutdown'):
                    await component.shutdown()
                logger.info(f"Closed {name}")
            except Exception as e:
                logger.error(f"Error closing {name}: {e}")

        # Close market data crawler specifically
        if hasattr(self, 'market_data_crawler') and self.market_data_crawler:
            try:
                await self.market_data_crawler.close()
                logger.info("Market data crawler closed")
            except Exception as e:
                logger.error(f"Error closing market data crawler: {e}")

        # Close enhanced bybit client
        if hasattr(self, 'bybit_client') and self.bybit_client:
            try:
                await self.bybit_client.close()
                logger.info("Enhanced Bybit client closed")
            except Exception as e:
                logger.error(f"Error closing Enhanced Bybit client: {e}")

        # Close any remaining aiohttp sessions
        try:
            # Force garbage collection to close any remaining sessions
            import gc
            gc.collect()
            logger.info("Forced garbage collection for session cleanup")
        except Exception as e:
            logger.error(f"Error during garbage collection: {e}")

        # Generate final detailed report
        await self.generate_final_detailed_report()

        # Final runtime report
        runtime = (datetime.now(timezone.utc) - self.start_time).total_seconds()
        logger.info("=" * 80)
        logger.info("SYSTEM SHUTDOWN COMPLETE")
        logger.info(f"Total Runtime: {runtime:.1f} seconds")
        logger.info("=" * 80)

    async def _run_mpc_security_monitor(self):
        """
        Monitor MPC security layer and distributed key management
        SECURITY: Ensure MPC protocols are functioning correctly
        """
        try:
            logger.info("Starting MPC Security Monitor...")
            while self.is_running:
                if hasattr(self, 'mpc_security') and self.mpc_security:
                    try:
                        # Get security status
                        security_status = await self.mpc_security.get_security_status()

                        # Log security metrics
                        if security_status['active_channels'] > 0:
                            logger.info(f"MPC SECURITY: {security_status['active_channels']} active channels, "
                                      f"{len(security_status['blocked_parties'])} blocked parties")

                        # Check for security violations
                        if len(security_status['blocked_parties']) > 0:
                            logger.warning(f"MPC SECURITY ALERT: {len(security_status['blocked_parties'])} parties blocked")

                        # Monitor MPC wallet status
                        if hasattr(self, 'mpc_wallet') and self.mpc_wallet:
                            wallet_status = await self.mpc_wallet.get_wallet_status()
                            if wallet_status.get('is_operational'):
                                logger.info(f"MPC WALLET: Operational - Balance: ${wallet_status.get('balance', 0):.2f}")
                            else:
                                logger.warning("MPC WALLET: Not operational")

                        # Monitor threshold signature manager
                        if hasattr(self, 'threshold_manager') and self.threshold_manager:
                            threshold_status = await self.threshold_manager.get_manager_status()
                            if threshold_status.get('active_sessions', 0) > 0:
                                logger.info(f"THRESHOLD SIGNATURES: {threshold_status.get('active_sessions', 0)} active signing sessions")

                    except Exception as monitor_error:
                        logger.error(f"MPC security monitoring error: {monitor_error}")

                # Monitor every 60 seconds
                await asyncio.sleep(60)

        except Exception as e:
            logger.error(f"MPC Security Monitor error: {e}")

    async def cleanup(self):
        """Cleanup method for graceful shutdown"""
        try:
            print("Cleaning up trading bot systems...")
            self.is_running = False

            # Cleanup enhanced learning systems
            if hasattr(self, 'enhanced_multi_timeframe_learner') and self.enhanced_multi_timeframe_learner:
                # Enhanced learning systems don't need explicit cleanup
                pass

            if hasattr(self, 'tier1_integration_manager') and self.tier1_integration_manager:
                # Tier 1 systems don't need explicit cleanup
                pass

            if hasattr(self, 'advanced_data_integration_engine') and self.advanced_data_integration_engine:
                # Data integration engine doesn't need explicit cleanup
                pass

            # Cleanup optimization systems (ALL 8 TIERS)
            if hasattr(self, 'optimization_manager') and self.optimization_manager:
                try:
                    await self.optimization_manager.shutdown()
                    print("Optimization Manager (8 Tiers) cleaned up successfully")
                    self.optimization_active = False
                except Exception as e:
                    print(f"Optimization Manager cleanup error: {e}")

            # Clear individual optimization component references
            optimization_components = [
                'streaming_processor', 'compression_engine', 'multi_agent_rl',
                'graph_neural_networks', 'edge_computing', 'quantum_engine',
                'feature_pipeline', 'adaptive_learning'
            ]

            for component_name in optimization_components:
                if hasattr(self, component_name):
                    setattr(self, component_name, None)

            # Cleanup MPC security systems
            if hasattr(self, 'mpc_security') and self.mpc_security:
                try:
                    await self.mpc_security.cleanup()
                    print("MPC Security Layer cleaned up")
                except Exception as e:
                    print(f"MPC Security cleanup error: {e}")

            if hasattr(self, 'mpc_wallet') and self.mpc_wallet:
                try:
                    await self.mpc_wallet.cleanup()
                    print("MPC Wallet cleaned up")
                except Exception as e:
                    print(f"MPC Wallet cleanup error: {e}")

            if hasattr(self, 'threshold_manager') and self.threshold_manager:
                try:
                    await self.threshold_manager.cleanup()
                    print("Threshold Signature Manager cleaned up")
                except Exception as e:
                    print(f"Threshold Manager cleanup error: {e}")

            if hasattr(self, 'mpc_engine') and self.mpc_engine:
                try:
                    await self.mpc_engine.cleanup()
                    print("MPC Cryptographic Engine cleaned up")
                except Exception as e:
                    print(f"MPC Engine cleanup error: {e}")

            print("Cleanup completed successfully")
            return True

        except Exception as e:
            print(f"Cleanup failed: {e}")
            return False

async def main():
    """Main entry point - ALL FUNCTIONS ACTIVE"""
    print("DEBUG: About to create BybitTradingBotSystem()")
    bot_system = BybitTradingBotSystem()
    print("DEBUG: About to call bot_system.run()")
    await bot_system.run()

def sync_main():
    """Synchronous wrapper for main"""
    try:
        print("DEBUG: About to start asyncio.run(main())")
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nSHUTDOWN: Shutdown requested by user")
    except Exception as e:
        print(f"FATAL ERROR in sync_main: {e}")
        print(f"ERROR TYPE: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    import sys

    # Check for test mode argument
    test_mode = "--test" in sys.argv or "--test-mode" in sys.argv

    if test_mode:
        print("RUNNING IN TEST MODE - LIMITED EXECUTION TIME")
        print("=" * 50)

        # Run system validation only
        async def test_main():
            bot_system = BybitTradingBotSystem()
            print("System created successfully")

            # Test initialization only
            if await bot_system.initialize_all_systems():
                print("SUCCESS: All systems initialized")
                print("Account status check...")

                # Quick account status check
                try:
                    account_info = await bot_system.enhanced_bybit_client.get_account_info()
                    total_equity = account_info.get('totalEquity', 0)
                    unrealized_pnl = account_info.get('unrealizedPnl', 0)
                    print(f"Total Equity: {total_equity}")
                    print(f"Unrealized P&L: {unrealized_pnl}")

                    if float(unrealized_pnl) < -5:
                        print("WARNING: Significant unrealized losses detected!")
                        print("System should be reviewed before live trading")

                except Exception as e:
                    print(f"Account check failed: {e}")

                print("TEST MODE COMPLETED - System is functional")
                return True
            else:
                print("ERROR: System initialization failed")
                return False

        try:
            result = asyncio.run(test_main())
            sys.exit(0 if result else 1)
        except Exception as e:
            print(f"TEST MODE ERROR: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    else:
        print("DEBUG: About to call sync_main()")
        try:
            sync_main()
        except Exception as e:
            print(f"ERROR in sync_main(): {e}")
            import traceback
            traceback.print_exc()
