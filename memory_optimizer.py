#!/usr/bin/env python3
"""
Memory Optimization for Trading Bot System
"""

import gc
import os
import sys
import psutil
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any

class MemoryOptimizer:
    """Optimize memory usage for the trading bot system"""
    
    def __init__(self):
        self.logger = logging.getLogger("memory_optimizer")
        self.process = psutil.Process()
        self.initial_memory = self.get_memory_usage()
        
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics"""
        memory_info = self.process.memory_info()
        memory_percent = self.process.memory_percent()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
            'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            'percent': memory_percent,
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }
    
    def optimize_garbage_collection(self):
        """Optimize garbage collection settings"""
        print("OPTIMIZING GARBAGE COLLECTION")
        print("-" * 40)
        
        # Force garbage collection
        collected = gc.collect()
        print(f"Collected {collected} objects")
        
        # Get garbage collection stats
        stats = gc.get_stats()
        for i, stat in enumerate(stats):
            print(f"Generation {i}: {stat}")
        
        # Set more aggressive garbage collection thresholds
        gc.set_threshold(700, 10, 10)  # More frequent collection
        print("Set aggressive GC thresholds: (700, 10, 10)")
        
        return collected
    
    def clear_module_caches(self):
        """Clear Python module caches"""
        print("\nCLEARING MODULE CACHES")
        print("-" * 40)
        
        # Clear import cache
        sys.modules.clear()
        print("Cleared sys.modules cache")
        
        # Clear __pycache__ directories
        cache_cleared = 0
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                pycache_dir = os.path.join(root, '__pycache__')
                try:
                    for file in os.listdir(pycache_dir):
                        os.remove(os.path.join(pycache_dir, file))
                    os.rmdir(pycache_dir)
                    cache_cleared += 1
                except Exception as e:
                    print(f"Warning: Could not clear {pycache_dir}: {e}")
        
        print(f"Cleared {cache_cleared} __pycache__ directories")
        return cache_cleared
    
    def optimize_data_structures(self):
        """Optimize data structures in memory"""
        print("\nOPTIMIZING DATA STRUCTURES")
        print("-" * 40)
        
        optimizations = []
        
        # Enable memory-efficient data structures
        try:
            import array
            optimizations.append("Array module available for efficient numeric arrays")
        except ImportError:
            pass
        
        try:
            import collections
            optimizations.append("Collections module available for efficient containers")
        except ImportError:
            pass
        
        # Suggest using __slots__ for classes
        optimizations.append("Recommend using __slots__ in data classes")
        optimizations.append("Use generators instead of lists where possible")
        optimizations.append("Use numpy arrays for large numeric datasets")
        
        for opt in optimizations:
            print(f"- {opt}")
        
        return len(optimizations)
    
    def monitor_large_objects(self):
        """Monitor and report large objects in memory"""
        print("\nMONITORING LARGE OBJECTS")
        print("-" * 40)
        
        # Get all objects
        all_objects = gc.get_objects()
        
        # Find large objects
        large_objects = []
        for obj in all_objects:
            try:
                size = sys.getsizeof(obj)
                if size > 1024 * 1024:  # Objects larger than 1MB
                    large_objects.append((type(obj).__name__, size / 1024 / 1024))
            except:
                continue
        
        # Sort by size
        large_objects.sort(key=lambda x: x[1], reverse=True)
        
        print(f"Found {len(large_objects)} objects larger than 1MB:")
        for obj_type, size_mb in large_objects[:10]:  # Show top 10
            print(f"  {obj_type}: {size_mb:.2f} MB")
        
        return len(large_objects)
    
    def set_memory_limits(self):
        """Set memory limits for the process"""
        print("\nSETTING MEMORY LIMITS")
        print("-" * 40)
        
        try:
            import resource
            
            # Get current memory limit
            soft, hard = resource.getrlimit(resource.RLIMIT_AS)
            print(f"Current memory limit: {soft / 1024 / 1024:.0f} MB")
            
            # Set reasonable memory limit (4GB)
            new_limit = 4 * 1024 * 1024 * 1024  # 4GB
            resource.setrlimit(resource.RLIMIT_AS, (new_limit, hard))
            print(f"Set memory limit to: {new_limit / 1024 / 1024 / 1024:.0f} GB")
            
            return True
            
        except ImportError:
            print("Resource module not available (Windows)")
            return False
        except Exception as e:
            print(f"Could not set memory limit: {e}")
            return False
    
    def optimize_system(self) -> Dict[str, Any]:
        """Run complete memory optimization"""
        print("MEMORY OPTIMIZATION STARTED")
        print("=" * 50)
        print(f"Time: {datetime.now()}")
        
        # Get initial memory usage
        initial_memory = self.get_memory_usage()
        print(f"Initial memory usage: {initial_memory['rss_mb']:.1f} MB ({initial_memory['percent']:.1f}%)")
        
        results = {}
        
        # Run optimizations
        results['gc_collected'] = self.optimize_garbage_collection()
        results['caches_cleared'] = self.clear_module_caches()
        results['optimizations'] = self.optimize_data_structures()
        results['large_objects'] = self.monitor_large_objects()
        results['memory_limit_set'] = self.set_memory_limits()
        
        # Get final memory usage
        final_memory = self.get_memory_usage()
        memory_saved = initial_memory['rss_mb'] - final_memory['rss_mb']
        
        print("\nOPTIMIZATION RESULTS")
        print("=" * 50)
        print(f"Initial memory: {initial_memory['rss_mb']:.1f} MB")
        print(f"Final memory: {final_memory['rss_mb']:.1f} MB")
        print(f"Memory saved: {memory_saved:.1f} MB")
        print(f"Memory usage: {final_memory['percent']:.1f}%")
        print(f"Available memory: {final_memory['available_mb']:.1f} MB")
        
        results['initial_memory'] = initial_memory
        results['final_memory'] = final_memory
        results['memory_saved'] = memory_saved
        
        return results

def main():
    """Main optimization function"""
    optimizer = MemoryOptimizer()
    results = optimizer.optimize_system()
    
    print("\nOPTIMIZATION COMPLETE")
    print("Recommendations:")
    print("- Monitor memory usage regularly")
    print("- Use memory profilers for detailed analysis")
    print("- Consider using memory-mapped files for large datasets")
    print("- Implement data streaming for large operations")
    
    return results

if __name__ == "__main__":
    main()
