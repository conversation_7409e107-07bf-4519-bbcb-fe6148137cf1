#!/usr/bin/env python3
"""
Comprehensive dependency checker for main.py
Verifies all imported modules exist and are accessible
"""

import os
import sys
import ast
import importlib
from pathlib import Path

def check_dependencies():
    """Check all dependencies in main.py and bybit_bot modules"""
    
    print("DEPENDENCY CHECKER")
    print("=" * 50)
    
    # Change to correct directory
    os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
    sys.path.insert(0, '.')
    
    # Read main.py and extract imports
    main_py_path = Path("bybit_bot/main.py")
    
    if not main_py_path.exists():
        print("❌ main.py not found")
        return False
    
    print("✅ main.py found")
    
    # Parse the file to extract imports
    with open(main_py_path, 'r', encoding='utf-8') as f:
        source_code = f.read()
    
    try:
        tree = ast.parse(source_code)
    except SyntaxError as e:
        print(f"❌ Syntax error in main.py: {e}")
        return False
    
    print("✅ main.py syntax is valid")
    
    # Extract all imports
    imports = []
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                imports.append(node.module)
                for alias in node.names:
                    if alias.name != '*':
                        full_name = f"{node.module}.{alias.name}"
                        imports.append(full_name)
    
    print(f"Found {len(imports)} import statements")
    
    # Check each import
    missing_imports = []
    available_imports = []
    bybit_bot_imports = []
    
    for import_name in sorted(set(imports)):
        try:
            if import_name.startswith('bybit_bot'):
                bybit_bot_imports.append(import_name)
                # Check if the module file exists
                module_path = import_name.replace('.', '/')
                py_file = Path(f"{module_path}.py")
                init_file = Path(f"{module_path}/__init__.py")
                
                if py_file.exists() or init_file.exists():
                    available_imports.append(import_name)
                    print(f"✅ {import_name}")
                else:
                    missing_imports.append(import_name)
                    print(f"❌ {import_name} (file not found)")
            else:
                # Try to import standard/external modules
                importlib.import_module(import_name.split('.')[0])
                available_imports.append(import_name)
                print(f"✅ {import_name}")
        except ImportError:
            missing_imports.append(import_name)
            print(f"❌ {import_name} (import failed)")
        except Exception as e:
            missing_imports.append(import_name)
            print(f"⚠️  {import_name} (error: {e})")
    
    print(f"\nDEPENDENCY SUMMARY:")
    print(f"  Total imports: {len(set(imports))}")
    print(f"  Available: {len(available_imports)}")
    print(f"  Missing: {len(missing_imports)}")
    print(f"  bybit_bot modules: {len(bybit_bot_imports)}")
    
    if missing_imports:
        print(f"\nMISSING DEPENDENCIES:")
        for imp in missing_imports:
            print(f"  - {imp}")
    
    # Check critical bybit_bot modules
    critical_modules = [
        'bybit_bot.core.optimization_manager',
        'bybit_bot.exchange.bybit_client',
        'bybit_bot.ai.advanced_market_predictor',
        'bybit_bot.profit_maximization.advanced_profit_engine',
        'bybit_bot.risk.risk_manager',
        'bybit_bot.mcp.mcp_client'
    ]
    
    print(f"\nCRITICAL MODULE CHECK:")
    critical_missing = []
    
    for module in critical_modules:
        module_path = module.replace('.', '/')
        py_file = Path(f"{module_path}.py")
        init_file = Path(f"{module_path}/__init__.py")
        
        if py_file.exists() or init_file.exists():
            print(f"✅ {module}")
        else:
            critical_missing.append(module)
            print(f"❌ {module}")
    
    # Check external dependencies
    external_deps = [
        'asyncio', 'logging', 'pathlib', 'dotenv', 'typing', 'datetime',
        'numpy', 'pandas', 'websockets', 'aiohttp', 'sqlite3'
    ]
    
    print(f"\nEXTERNAL DEPENDENCIES CHECK:")
    external_missing = []
    
    for dep in external_deps:
        try:
            importlib.import_module(dep)
            print(f"✅ {dep}")
        except ImportError:
            external_missing.append(dep)
            print(f"❌ {dep}")
    
    # Final assessment
    print(f"\nFINAL ASSESSMENT:")
    
    if len(missing_imports) == 0 and len(critical_missing) == 0 and len(external_missing) == 0:
        print("🎉 ALL DEPENDENCIES AVAILABLE")
        print("✅ main.py should run without import errors")
        return True
    else:
        print("⚠️  SOME DEPENDENCIES MISSING")
        if critical_missing:
            print(f"❌ Critical modules missing: {len(critical_missing)}")
        if external_missing:
            print(f"❌ External dependencies missing: {len(external_missing)}")
        return False

if __name__ == "__main__":
    success = check_dependencies()
    if success:
        print("\n✅ DEPENDENCY CHECK PASSED")
    else:
        print("\n❌ DEPENDENCY CHECK FAILED")
