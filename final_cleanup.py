#!/usr/bin/env python3
"""
Final cleanup script for backup files
"""

import os
import glob
import time
from datetime import datetime

def cleanup_backup_files():
    """Clean up backup files"""
    print("FINAL BACKUP CLEANUP")
    print("=" * 30)
    print(f"Started: {datetime.now()}")
    
    # Change to correct directory
    target_dir = r'E:\The_real_deal_copy\Bybit_Bot\BOT'
    if os.path.exists(target_dir):
        os.chdir(target_dir)
        print(f"Working in: {os.getcwd()}")
    else:
        print(f"ERROR: Directory not found: {target_dir}")
        return False
    
    # Find all backup files
    backup_patterns = [
        "*.backup_*",
        "**/*.backup_*",
        "**/main.py.backup_*",
        "**/autonomous_sync_manager.py.backup_*"
    ]
    
    total_removed = 0
    total_found = 0
    
    for pattern in backup_patterns:
        print(f"\nSearching for: {pattern}")
        
        try:
            backup_files = glob.glob(pattern, recursive=True)
            total_found += len(backup_files)
            print(f"Found {len(backup_files)} files")
            
            if len(backup_files) > 3:
                # Sort by modification time, keep 3 most recent
                backup_files.sort(key=lambda x: os.path.getmtime(x))
                to_remove = backup_files[:-3]  # Keep 3 most recent
                
                # Remove files (limit to 100 at a time)
                for i, file_path in enumerate(to_remove[:100]):
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            print(f"Removed: {file_path}")
                            total_removed += 1
                        
                        # Small delay to prevent overwhelming
                        if i % 10 == 0:
                            time.sleep(0.1)
                            
                    except Exception as e:
                        print(f"Failed to remove {file_path}: {e}")
            
        except Exception as e:
            print(f"Error with pattern {pattern}: {e}")
    
    print(f"\nCLEANUP SUMMARY")
    print(f"Total files found: {total_found}")
    print(f"Total files removed: {total_removed}")
    print(f"Files remaining: {total_found - total_removed}")
    print("Cleanup completed successfully")
    
    return True

def check_directory_size():
    """Check directory size before and after cleanup"""
    print("\nDIRECTORY SIZE CHECK")
    print("-" * 20)
    
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            try:
                file_path = os.path.join(root, file)
                size = os.path.getsize(file_path)
                total_size += size
                file_count += 1
            except:
                continue
    
    size_mb = total_size / (1024 * 1024)
    print(f"Total files: {file_count}")
    print(f"Total size: {size_mb:.1f} MB")
    
    return size_mb, file_count

def main():
    """Main cleanup function"""
    print("STARTING FINAL CLEANUP")
    print("=" * 40)
    
    # Check initial size
    initial_size, initial_files = check_directory_size()
    
    # Run cleanup
    success = cleanup_backup_files()
    
    if success:
        # Check final size
        final_size, final_files = check_directory_size()
        
        print(f"\nFINAL RESULTS")
        print(f"Initial: {initial_files} files, {initial_size:.1f} MB")
        print(f"Final: {final_files} files, {final_size:.1f} MB")
        print(f"Saved: {initial_size - final_size:.1f} MB")
        print(f"Removed: {initial_files - final_files} files")
    
    print("\nCLEANUP COMPLETE")
    return success

if __name__ == "__main__":
    main()
