#!/usr/bin/env python3
"""
Comprehensive main.py validator
Tests main.py functionality without environment issues
"""

import os
import sys
import subprocess
import time
import threading
import queue
from pathlib import Path
from datetime import datetime

class MainPyValidator:
    def __init__(self):
        self.output_queue = queue.Queue()
        self.error_queue = queue.Queue()
        self.warnings = []
        self.errors = []
        self.info_messages = []
        
    def setup_environment(self):
        """Setup proper environment for testing"""
        print("COMPREHENSIVE MAIN.PY VALIDATOR")
        print("=" * 50)
        
        # Change to correct directory
        target_dir = r'E:\The_real_deal_copy\Bybit_Bot\BOT'
        if os.path.exists(target_dir):
            os.chdir(target_dir)
            print(f"✅ Changed to directory: {target_dir}")
        else:
            print(f"❌ Directory not found: {target_dir}")
            return False
            
        # Verify main.py exists
        main_py = Path("bybit_bot/main.py")
        if main_py.exists():
            print(f"✅ main.py found: {main_py}")
        else:
            print(f"❌ main.py not found: {main_py}")
            return False
            
        return True
    
    def test_syntax_compilation(self):
        """Test Python syntax compilation"""
        print("\n🔍 TESTING SYNTAX COMPILATION")
        print("-" * 30)
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'py_compile', 'bybit_bot/main.py'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Syntax compilation PASSED")
                return True
            else:
                print("❌ Syntax compilation FAILED")
                print(f"Error: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print("⚠️  Syntax compilation TIMEOUT")
            return False
        except Exception as e:
            print(f"❌ Syntax compilation ERROR: {e}")
            return False
    
    def test_import_validation(self):
        """Test import statements"""
        print("\n🔍 TESTING IMPORT VALIDATION")
        print("-" * 30)
        
        test_script = '''
import sys
import os
sys.path.insert(0, '.')

try:
    # Test critical imports
    print("Testing websockets compatibility...")
    from websockets_compatibility import create_comprehensive_websockets_compatibility
    print("✅ websockets_compatibility import OK")
    
    print("Testing bybit_bot imports...")
    import bybit_bot
    print("✅ bybit_bot package import OK")
    
    print("Testing core modules...")
    from bybit_bot.core.optimization_manager import OptimizationComponentsManager
    print("✅ optimization_manager import OK")
    
    from bybit_bot.exchange.bybit_client import BybitClient
    print("✅ bybit_client import OK")
    
    print("ALL CRITICAL IMPORTS SUCCESSFUL")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ General error: {e}")
'''
        
        try:
            result = subprocess.run([
                sys.executable, '-c', test_script
            ], capture_output=True, text=True, timeout=60)
            
            print("Import test output:")
            print(result.stdout)
            
            if result.stderr:
                print("Import test errors:")
                print(result.stderr)
            
            if "ALL CRITICAL IMPORTS SUCCESSFUL" in result.stdout:
                print("✅ Import validation PASSED")
                return True
            else:
                print("❌ Import validation FAILED")
                return False
                
        except subprocess.TimeoutExpired:
            print("⚠️  Import validation TIMEOUT")
            return False
        except Exception as e:
            print(f"❌ Import validation ERROR: {e}")
            return False
    
    def test_main_execution(self, timeout=120):
        """Test main.py execution with comprehensive monitoring"""
        print("\n🔍 TESTING MAIN.PY EXECUTION")
        print("-" * 30)
        
        try:
            # Run main.py in test mode
            process = subprocess.Popen([
                sys.executable, 'bybit_bot/main.py', '--test'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
               text=True, bufsize=1, universal_newlines=True)
            
            # Monitor output with timeout
            start_time = time.time()
            output_lines = []
            error_lines = []
            
            while True:
                # Check if process is still running
                if process.poll() is not None:
                    break
                    
                # Check timeout
                if time.time() - start_time > timeout:
                    print(f"⚠️  Execution timeout after {timeout} seconds")
                    process.terminate()
                    break
                
                # Read available output
                try:
                    line = process.stdout.readline()
                    if line:
                        output_lines.append(line.strip())
                        print(f"OUT: {line.strip()}")
                        
                        # Categorize messages
                        if any(keyword in line.lower() for keyword in ['error', 'failed', 'exception']):
                            self.errors.append(line.strip())
                        elif any(keyword in line.lower() for keyword in ['warning', 'warn']):
                            self.warnings.append(line.strip())
                        else:
                            self.info_messages.append(line.strip())
                except:
                    pass
                
                # Read available errors
                try:
                    line = process.stderr.readline()
                    if line:
                        error_lines.append(line.strip())
                        print(f"ERR: {line.strip()}")
                        self.errors.append(line.strip())
                except:
                    pass
                
                time.sleep(0.1)
            
            # Get remaining output
            remaining_out, remaining_err = process.communicate()
            if remaining_out:
                output_lines.extend(remaining_out.strip().split('\n'))
            if remaining_err:
                error_lines.extend(remaining_err.strip().split('\n'))
            
            # Analysis
            print(f"\n📊 EXECUTION ANALYSIS:")
            print(f"  Output lines: {len(output_lines)}")
            print(f"  Error lines: {len(error_lines)}")
            print(f"  Warnings: {len(self.warnings)}")
            print(f"  Errors: {len(self.errors)}")
            print(f"  Info messages: {len(self.info_messages)}")
            print(f"  Exit code: {process.returncode}")
            
            return process.returncode == 0
            
        except Exception as e:
            print(f"❌ Execution test ERROR: {e}")
            return False
    
    def generate_report(self):
        """Generate comprehensive validation report"""
        print("\n📋 VALIDATION REPORT")
        print("=" * 50)
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings[:10]:  # Show first 10
                print(f"  - {warning}")
            if len(self.warnings) > 10:
                print(f"  ... and {len(self.warnings) - 10} more")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors[:10]:  # Show first 10
                print(f"  - {error}")
            if len(self.errors) > 10:
                print(f"  ... and {len(self.errors) - 10} more")
        
        if self.info_messages:
            print(f"\n✅ INFO MESSAGES ({len(self.info_messages)}):")
            for info in self.info_messages[:5]:  # Show first 5
                print(f"  - {info}")
            if len(self.info_messages) > 5:
                print(f"  ... and {len(self.info_messages) - 5} more")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if len(self.errors) == 0:
            print("✅ NO CRITICAL ERRORS DETECTED")
            if len(self.warnings) == 0:
                print("✅ NO WARNINGS DETECTED")
                print("🎉 MAIN.PY VALIDATION SUCCESSFUL")
                return True
            else:
                print(f"⚠️  {len(self.warnings)} warnings detected but system functional")
                return True
        else:
            print(f"❌ {len(self.errors)} errors detected - requires attention")
            return False

def main():
    """Main validation function"""
    validator = MainPyValidator()
    
    # Setup
    if not validator.setup_environment():
        print("❌ Environment setup failed")
        return False
    
    # Run tests
    syntax_ok = validator.test_syntax_compilation()
    imports_ok = validator.test_import_validation()
    execution_ok = validator.test_main_execution()
    
    # Generate report
    overall_ok = validator.generate_report()
    
    print(f"\n🏁 FINAL RESULT:")
    print(f"  Syntax: {'✅' if syntax_ok else '❌'}")
    print(f"  Imports: {'✅' if imports_ok else '❌'}")
    print(f"  Execution: {'✅' if execution_ok else '❌'}")
    print(f"  Overall: {'✅' if overall_ok else '❌'}")
    
    return overall_ok

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 VALIDATION COMPLETED SUCCESSFULLY")
    else:
        print("\n❌ VALIDATION FAILED - ISSUES DETECTED")
