#!/usr/bin/env python3
"""
Run main.py and capture ALL errors for analysis
"""

import sys
import traceback
import asyncio
import logging
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging to capture everything
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('error_capture.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def run_main_system():
    """Run the main system and capture all errors"""
    try:
        logger.info("Starting main system error capture...")
        
        # Import and run main system
        from bybit_bot.main import BybitTradingBotSystem
        
        logger.info("Creating system instance...")
        system = BybitTradingBotSystem()
        
        logger.info("Initializing components...")
        await system.initialize_components()
        
        logger.info("Starting main run (limited time for error capture)...")
        # Run for a short time to capture initialization errors
        await asyncio.wait_for(system.run(), timeout=30.0)
        
    except asyncio.TimeoutError:
        logger.info("Timeout reached - this is expected for error capture")
        return True
    except Exception as e:
        logger.error(f"ERROR CAPTURED: {type(e).__name__}: {e}")
        logger.error(f"Full traceback:\n{traceback.format_exc()}")
        return False
    finally:
        try:
            if 'system' in locals():
                logger.info("Shutting down system...")
                await system.shutdown()
        except Exception as shutdown_error:
            logger.error(f"Shutdown error: {shutdown_error}")

def main():
    """Main function"""
    try:
        logger.info("="*80)
        logger.info("MAIN.PY ERROR CAPTURE SESSION")
        logger.info(f"Started at: {datetime.now()}")
        logger.info("="*80)
        
        # Run the async main function
        result = asyncio.run(run_main_system())
        
        logger.info("="*80)
        logger.info("ERROR CAPTURE COMPLETE")
        logger.info("Check error_capture.log for detailed output")
        logger.info("="*80)
        
        return result
        
    except Exception as e:
        logger.error(f"CRITICAL ERROR in main: {e}")
        logger.error(f"Full traceback:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
