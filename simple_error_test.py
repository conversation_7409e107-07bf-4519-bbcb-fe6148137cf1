#!/usr/bin/env python3
"""
Simple Error Test - Find specific errors in main.py execution
"""

import sys
import traceback
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_import():
    """Test basic import of main module"""
    print("TESTING BASIC IMPORT")
    print("-" * 40)
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        print("SUCCESS: BybitTradingBotSystem import successful")
        return True
    except Exception as e:
        print(f"ERROR: Import failed - {e}")
        traceback.print_exc()
        return False

def test_system_creation():
    """Test creating system instance"""
    print("\nTESTING SYSTEM CREATION")
    print("-" * 40)
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        system = BybitTradingBotSystem()
        print("SUCCESS: System instance created")
        return True, system
    except Exception as e:
        print(f"ERROR: System creation failed - {e}")
        traceback.print_exc()
        return False, None

async def test_system_initialization(system):
    """Test system initialization"""
    print("\nTESTING SYSTEM INITIALIZATION")
    print("-" * 40)
    
    try:
        # Test component initialization first
        result = await system.initialize_components()
        if result:
            print("SUCCESS: Component initialization completed")
        else:
            print("WARNING: Component initialization returned False")
        
        # Test full system initialization
        result = await system.initialize_all_systems()
        if result:
            print("SUCCESS: Full system initialization completed")
            return True
        else:
            print("ERROR: Full system initialization failed")
            return False
            
    except Exception as e:
        print(f"ERROR: System initialization failed - {e}")
        traceback.print_exc()
        return False

def test_individual_imports():
    """Test individual module imports"""
    print("\nTESTING INDIVIDUAL IMPORTS")
    print("-" * 40)
    
    imports_to_test = [
        ("bybit_bot.core.config", "EnhancedBotConfig"),
        ("bybit_bot.database.connection", "DatabaseManager"),
        ("bybit_bot.exchange.enhanced_bybit_client", "EnhancedBybitClient"),
        ("bybit_bot.ai.ai_folder_activation_manager", "activate_ai_folder"),
        ("bybit_bot.core.enhanced_time_manager", "EnhancedTimeManager"),
        ("bybit_bot.agents.agent_orchestrator", "AgentOrchestrator"),
        ("bybit_bot.performance.scaling_engine", "PerformanceScalingSystem"),
        ("bybit_bot.ai.intelligent_ml_system", "IntelligentMLSystem"),
    ]
    
    failed_imports = []
    successful_imports = []
    
    for module_path, class_name in imports_to_test:
        try:
            exec(f"from {module_path} import {class_name}")
            successful_imports.append((module_path, class_name))
            print(f"SUCCESS: {module_path}.{class_name}")
        except Exception as e:
            failed_imports.append((module_path, class_name, str(e)))
            print(f"ERROR: {module_path}.{class_name} - {e}")
    
    print(f"\nIMPORT SUMMARY:")
    print(f"Successful: {len(successful_imports)}")
    print(f"Failed: {len(failed_imports)}")
    
    return failed_imports

async def main():
    """Main test function"""
    print("COMPREHENSIVE ERROR DETECTION FOR MAIN.PY")
    print("=" * 60)
    
    # Test 1: Basic import
    import_success = test_basic_import()
    
    if not import_success:
        print("\nCRITICAL: Basic import failed - cannot proceed")
        return False
    
    # Test 2: System creation
    creation_success, system = test_system_creation()
    
    if not creation_success:
        print("\nCRITICAL: System creation failed - cannot proceed")
        return False
    
    # Test 3: Individual imports
    failed_imports = test_individual_imports()
    
    # Test 4: System initialization
    if system:
        init_success = await test_system_initialization(system)
    else:
        init_success = False
    
    # Final summary
    print("\n" + "=" * 60)
    print("FINAL ERROR DETECTION SUMMARY")
    print("=" * 60)
    
    print(f"Basic Import: {'SUCCESS' if import_success else 'FAILED'}")
    print(f"System Creation: {'SUCCESS' if creation_success else 'FAILED'}")
    print(f"Failed Imports: {len(failed_imports)}")
    print(f"System Initialization: {'SUCCESS' if init_success else 'FAILED'}")
    
    if failed_imports:
        print("\nFAILED IMPORTS DETAILS:")
        for i, (module, cls, error) in enumerate(failed_imports, 1):
            print(f"{i}. {module}.{cls}")
            print(f"   Error: {error}")
    
    total_errors = len(failed_imports) + (0 if import_success else 1) + (0 if creation_success else 1) + (0 if init_success else 1)
    
    if total_errors == 0:
        print("\nSUCCESS: NO ERRORS FOUND - SYSTEM IS READY")
        return True
    else:
        print(f"\nERRORS DETECTED: {total_errors} issues need to be fixed")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        traceback.print_exc()
        sys.exit(1)
