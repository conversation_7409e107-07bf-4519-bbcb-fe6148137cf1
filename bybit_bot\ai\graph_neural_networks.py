#!/usr/bin/env python3
"""
Temporal Graph Neural Networks for Market Microstructure Analysis
Implements cutting-edge GNN architectures for modeling asset correlations and market dependencies

Features:
- Temporal Graph Convolutional Networks (T-GCN)
- Dynamic graph construction from market data
- Multi-scale temporal attention mechanisms
- Cross-asset correlation modeling
- Market microstructure pattern recognition
- Real-time graph updates and inference
"""

import asyncio
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, TransformerConv
from torch_geometric.data import Data, Batch
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import deque, defaultdict
import time
import logging
from datetime import datetime, timedelta
import networkx as nx
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
import scipy.stats as stats

logger = logging.getLogger(__name__)

@dataclass
class GraphConfig:
    """Configuration for graph neural network"""
    node_features: int = 64
    edge_features: int = 16
    hidden_dim: int = 128
    num_layers: int = 3
    num_heads: int = 8
    dropout: float = 0.1
    learning_rate: float = 0.001
    temporal_window: int = 100
    correlation_threshold: float = 0.3
    update_frequency: int = 10  # Update graph every N ticks

@dataclass
class MarketNode:
    """Represents a market entity (asset, exchange, etc.)"""
    node_id: str
    node_type: str  # 'asset', 'exchange', 'sector'
    features: np.ndarray
    last_update: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MarketEdge:
    """Represents relationship between market entities"""
    source: str
    target: str
    edge_type: str  # 'correlation', 'arbitrage', 'flow'
    weight: float
    features: np.ndarray
    last_update: float

class TemporalGraphBuilder:
    """Builds and maintains dynamic market graphs"""
    
    def __init__(self, config: GraphConfig):
        self.config = config
        self.nodes = {}  # node_id -> MarketNode
        self.edges = {}  # (source, target) -> MarketEdge
        self.price_history = defaultdict(lambda: deque(maxlen=config.temporal_window))
        self.volume_history = defaultdict(lambda: deque(maxlen=config.temporal_window))
        self.correlation_matrix = {}
        self.last_graph_update = 0
        
    def add_market_data(self, symbol: str, price: float, volume: float, timestamp: float, 
                       additional_features: Optional[Dict[str, float]] = None):
        """Add market data and update graph structure"""
        
        # Update price and volume history
        self.price_history[symbol].append(price)
        self.volume_history[symbol].append(volume)
        
        # Create or update node
        if symbol not in self.nodes:
            self.nodes[symbol] = MarketNode(
                node_id=symbol,
                node_type='asset',
                features=np.zeros(self.config.node_features),
                last_update=timestamp
            )
        
        # Update node features
        self._update_node_features(symbol, price, volume, timestamp, additional_features)
        
        # Update graph structure if needed
        if timestamp - self.last_graph_update > self.config.update_frequency:
            self._update_graph_structure(timestamp)
            self.last_graph_update = timestamp
    
    def _update_node_features(self, symbol: str, price: float, volume: float, 
                            timestamp: float, additional_features: Optional[Dict[str, float]]):
        """Update node features with latest market data"""
        node = self.nodes[symbol]
        
        # Calculate technical features
        prices = list(self.price_history[symbol])
        volumes = list(self.volume_history[symbol])
        
        features = []
        
        if len(prices) >= 2:
            # Price-based features
            returns = np.diff(np.log(prices))
            features.extend([
                price,  # Current price
                np.mean(returns[-10:]) if len(returns) >= 10 else 0,  # Recent return
                np.std(returns[-20:]) if len(returns) >= 20 else 0,   # Volatility
                np.mean(prices[-5:]) / np.mean(prices[-20:]) if len(prices) >= 20 else 1,  # Price momentum
                (price - np.min(prices[-14:])) / (np.max(prices[-14:]) - np.min(prices[-14:])) if len(prices) >= 14 and np.max(prices[-14:]) != np.min(prices[-14:]) else 0.5,  # Stochastic %K
            ])
            
            # Volume-based features
            features.extend([
                volume,  # Current volume
                np.mean(volumes[-10:]) if len(volumes) >= 10 else volume,  # Average volume
                volume / np.mean(volumes[-20:]) if len(volumes) >= 20 and np.mean(volumes[-20:]) > 0 else 1,  # Volume ratio
                np.corrcoef(prices[-10:], volumes[-10:])[0, 1] if len(prices) >= 10 else 0,  # Price-volume correlation
            ])
            
            # Microstructure features
            if len(returns) >= 5:
                features.extend([
                    stats.skew(returns[-20:]) if len(returns) >= 20 else 0,  # Return skewness
                    stats.kurtosis(returns[-20:]) if len(returns) >= 20 else 0,  # Return kurtosis
                    np.sum(np.sign(returns[-10:])) / 10 if len(returns) >= 10 else 0,  # Trend strength
                ])
        else:
            features = [price, 0, 0, 1, 0.5, volume, volume, 1, 0, 0, 0, 0]
        
        # Add additional features if provided
        if additional_features:
            features.extend(list(additional_features.values()))
        
        # Pad or truncate to match node_features size
        while len(features) < self.config.node_features:
            features.append(0.0)
        features = features[:self.config.node_features]
        
        node.features = np.array(features, dtype=np.float32)
        node.last_update = timestamp
    
    def _update_graph_structure(self, timestamp: float):
        """Update graph edges based on correlations and relationships"""
        symbols = list(self.nodes.keys())
        
        if len(symbols) < 2:
            return
        
        # Calculate correlation matrix
        correlation_data = {}
        for symbol in symbols:
            if len(self.price_history[symbol]) >= 20:
                prices = list(self.price_history[symbol])
                returns = np.diff(np.log(prices))
                correlation_data[symbol] = returns
        
        # Update edges based on correlations
        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols):
                if i >= j or symbol1 not in correlation_data or symbol2 not in correlation_data:
                    continue
                
                # Calculate correlation
                corr = np.corrcoef(correlation_data[symbol1], correlation_data[symbol2])[0, 1]
                
                if not np.isnan(corr) and abs(corr) > self.config.correlation_threshold:
                    edge_key = (symbol1, symbol2)
                    
                    # Create edge features
                    edge_features = self._calculate_edge_features(symbol1, symbol2, corr)
                    
                    self.edges[edge_key] = MarketEdge(
                        source=symbol1,
                        target=symbol2,
                        edge_type='correlation',
                        weight=abs(corr),
                        features=edge_features,
                        last_update=timestamp
                    )
    
    def _calculate_edge_features(self, symbol1: str, symbol2: str, correlation: float) -> np.ndarray:
        """Calculate edge features between two assets"""
        features = [correlation]
        
        # Price ratio features
        if (self.price_history[symbol1] and self.price_history[symbol2] and 
            len(self.price_history[symbol1]) >= 10 and len(self.price_history[symbol2]) >= 10):
            
            prices1 = list(self.price_history[symbol1])
            prices2 = list(self.price_history[symbol2])
            
            # Price ratio and its volatility
            ratios = [p1/p2 for p1, p2 in zip(prices1, prices2) if p2 != 0]
            if ratios:
                features.extend([
                    ratios[-1],  # Current ratio
                    np.mean(ratios),  # Average ratio
                    np.std(ratios),   # Ratio volatility
                    (ratios[-1] - np.mean(ratios)) / np.std(ratios) if np.std(ratios) > 0 else 0,  # Z-score
                ])
            else:
                features.extend([1.0, 1.0, 0.0, 0.0])
            
            # Volume correlation
            volumes1 = list(self.volume_history[symbol1])
            volumes2 = list(self.volume_history[symbol2])
            if len(volumes1) >= 10 and len(volumes2) >= 10:
                vol_corr = np.corrcoef(volumes1[-10:], volumes2[-10:])[0, 1]
                features.append(vol_corr if not np.isnan(vol_corr) else 0.0)
            else:
                features.append(0.0)
        else:
            features.extend([1.0, 1.0, 0.0, 0.0, 0.0])
        
        # Pad to edge_features size
        while len(features) < self.config.edge_features:
            features.append(0.0)
        
        return np.array(features[:self.config.edge_features], dtype=np.float32)
    
    def get_graph_data(self) -> Data:
        """Convert current graph to PyTorch Geometric Data object"""
        if not self.nodes:
            return Data(x=torch.empty(0, self.config.node_features), 
                       edge_index=torch.empty(2, 0, dtype=torch.long))
        
        # Node features
        node_list = list(self.nodes.keys())
        node_features = torch.stack([
            torch.tensor(self.nodes[node_id].features, dtype=torch.float32)
            for node_id in node_list
        ])
        
        # Edge indices and features
        edge_indices = []
        edge_features = []
        
        for (source, target), edge in self.edges.items():
            if source in node_list and target in node_list:
                source_idx = node_list.index(source)
                target_idx = node_list.index(target)
                
                # Add both directions for undirected graph
                edge_indices.extend([[source_idx, target_idx], [target_idx, source_idx]])
                edge_features.extend([edge.features, edge.features])
        
        if edge_indices:
            edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()
            edge_attr = torch.stack([torch.tensor(feat, dtype=torch.float32) for feat in edge_features])
        else:
            edge_index = torch.empty(2, 0, dtype=torch.long)
            edge_attr = torch.empty(0, self.config.edge_features, dtype=torch.float32)
        
        return Data(x=node_features, edge_index=edge_index, edge_attr=edge_attr)

class TemporalGraphAttention(nn.Module):
    """Temporal attention mechanism for graph neural networks"""
    
    def __init__(self, hidden_dim: int, num_heads: int = 8):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        self.query = nn.Linear(hidden_dim, hidden_dim)
        self.key = nn.Linear(hidden_dim, hidden_dim)
        self.value = nn.Linear(hidden_dim, hidden_dim)
        self.output = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, x, temporal_mask=None):
        """Apply temporal attention"""
        batch_size, seq_len, _ = x.size()
        
        # Compute queries, keys, values
        Q = self.query(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.key(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.value(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Attention scores
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)
        
        # Apply temporal mask if provided
        if temporal_mask is not None:
            scores = scores.masked_fill(temporal_mask == 0, -1e9)
        
        # Softmax and apply to values
        attention_weights = F.softmax(scores, dim=-1)
        attended = torch.matmul(attention_weights, V)
        
        # Concatenate heads and apply output projection
        attended = attended.transpose(1, 2).contiguous().view(batch_size, seq_len, self.hidden_dim)
        output = self.output(attended)
        
        return output, attention_weights

class TemporalGraphConvNet(nn.Module):
    """Temporal Graph Convolutional Network for market analysis"""
    
    def __init__(self, config: GraphConfig):
        super().__init__()
        self.config = config
        
        # Graph convolution layers
        self.conv_layers = nn.ModuleList([
            TransformerConv(
                in_channels=config.node_features if i == 0 else config.hidden_dim,
                out_channels=config.hidden_dim,
                heads=config.num_heads,
                dropout=config.dropout,
                edge_dim=config.edge_features
            )
            for i in range(config.num_layers)
        ])
        
        # Temporal attention
        self.temporal_attention = TemporalGraphAttention(config.hidden_dim, config.num_heads)
        
        # Output layers
        self.output_layers = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 1)  # Predict price movement
        )
        
        # Normalization layers
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(config.hidden_dim) for _ in range(config.num_layers)
        ])
        
    def forward(self, data, temporal_sequence=None):
        """Forward pass through temporal graph network"""
        x, edge_index, edge_attr = data.x, data.edge_index, data.edge_attr
        
        # Graph convolution layers
        for i, (conv, norm) in enumerate(zip(self.conv_layers, self.layer_norms)):
            x_new = conv(x, edge_index, edge_attr)
            x = norm(x_new + x if x.size() == x_new.size() else x_new)  # Residual connection
            x = F.relu(x)
            x = F.dropout(x, p=self.config.dropout, training=self.training)
        
        # Apply temporal attention if sequence is provided
        if temporal_sequence is not None:
            x_temporal, attention_weights = self.temporal_attention(temporal_sequence)
            x = x + x_temporal.mean(dim=1)  # Combine spatial and temporal information
        
        # Output prediction
        output = self.output_layers(x)
        
        return output, x  # Return predictions and node embeddings

class MarketGraphAnalyzer:
    """Main analyzer for market graph neural networks"""
    
    def __init__(self, config: GraphConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Initialize components
        self.graph_builder = TemporalGraphBuilder(config)
        self.model = TemporalGraphConvNet(config).to(self.device)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config.learning_rate)
        
        # Training state
        self.training_losses = deque(maxlen=1000)
        self.prediction_history = deque(maxlen=1000)
        
        # Performance tracking
        self.node_embeddings = {}
        self.graph_metrics = {
            'num_nodes': 0,
            'num_edges': 0,
            'avg_clustering': 0.0,
            'avg_path_length': 0.0
        }
        
        logger.info(f"Market Graph Analyzer initialized on {self.device}")
    
    async def process_market_tick(self, symbol: str, price: float, volume: float, 
                                timestamp: float, features: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """Process market tick and update graph"""
        
        # Add data to graph builder
        self.graph_builder.add_market_data(symbol, price, volume, timestamp, features)
        
        # Get current graph
        graph_data = self.graph_builder.get_graph_data()
        
        if graph_data.x.size(0) == 0:
            return {'predictions': {}, 'embeddings': {}, 'graph_metrics': self.graph_metrics}
        
        # Move to device
        graph_data = graph_data.to(self.device)
        
        # Forward pass
        with torch.no_grad():
            predictions, embeddings = self.model(graph_data)
        
        # Convert to results
        node_list = list(self.graph_builder.nodes.keys())
        results = {
            'predictions': {
                node_list[i]: float(predictions[i].item()) 
                for i in range(len(node_list))
            },
            'embeddings': {
                node_list[i]: embeddings[i].cpu().numpy()
                for i in range(len(node_list))
            },
            'graph_metrics': self._calculate_graph_metrics(graph_data)
        }
        
        # Update stored embeddings
        self.node_embeddings.update(results['embeddings'])
        
        return results
    
    def _calculate_graph_metrics(self, graph_data: Data) -> Dict[str, float]:
        """Calculate graph topology metrics"""
        try:
            # Convert to NetworkX for analysis
            edge_index = graph_data.edge_index.cpu().numpy()
            G = nx.Graph()
            
            # Add nodes
            G.add_nodes_from(range(graph_data.x.size(0)))
            
            # Add edges
            if edge_index.size(1) > 0:
                edges = [(edge_index[0, i], edge_index[1, i]) for i in range(edge_index.size(1))]
                G.add_edges_from(edges)
            
            # Calculate metrics
            metrics = {
                'num_nodes': G.number_of_nodes(),
                'num_edges': G.number_of_edges(),
                'density': nx.density(G),
                'avg_clustering': nx.average_clustering(G) if G.number_of_nodes() > 0 else 0.0,
            }
            
            # Average path length (for connected components)
            if nx.is_connected(G):
                metrics['avg_path_length'] = nx.average_shortest_path_length(G)
            else:
                # Calculate for largest connected component
                largest_cc = max(nx.connected_components(G), key=len) if G.number_of_nodes() > 0 else set()
                if len(largest_cc) > 1:
                    subgraph = G.subgraph(largest_cc)
                    metrics['avg_path_length'] = nx.average_shortest_path_length(subgraph)
                else:
                    metrics['avg_path_length'] = 0.0
            
            self.graph_metrics = metrics
            return metrics
            
        except Exception as e:
            logger.warning(f"Error calculating graph metrics: {e}")
            return self.graph_metrics
    
    def get_asset_correlations(self) -> Dict[Tuple[str, str], float]:
        """Get current asset correlations from graph"""
        correlations = {}
        for (source, target), edge in self.graph_builder.edges.items():
            if edge.edge_type == 'correlation':
                correlations[(source, target)] = edge.weight
        return correlations
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        return {
            'graph_metrics': self.graph_metrics,
            'num_assets': len(self.graph_builder.nodes),
            'num_correlations': len(self.graph_builder.edges),
            'avg_training_loss': np.mean(self.training_losses) if self.training_losses else 0.0,
            'model_parameters': sum(p.numel() for p in self.model.parameters()),
            'device': str(self.device)
        }

# Example usage
async def main():
    """Example usage of graph neural network system"""
    config = GraphConfig(
        node_features=64,
        edge_features=16,
        hidden_dim=128,
        num_layers=3,
        temporal_window=100
    )
    
    analyzer = MarketGraphAnalyzer(config)
    
    # Simulate market data for multiple assets
    symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT']
    base_prices = {'BTCUSDT': 50000, 'ETHUSDT': 3000, 'ADAUSDT': 1.5, 'DOTUSDT': 25}
    
    for i in range(1000):
        timestamp = time.time()
        
        for symbol in symbols:
            # Simulate correlated price movements
            price_change = np.random.normal(0, 0.001) * base_prices[symbol]
            if symbol != 'BTCUSDT':
                # Add correlation with BTC
                btc_influence = np.random.normal(0, 0.0005) * base_prices[symbol]
                price_change += btc_influence
            
            price = base_prices[symbol] + price_change
            volume = np.random.exponential(0.1)
            
            # Process tick
            results = await analyzer.process_market_tick(symbol, price, volume, timestamp)
            
            base_prices[symbol] = price
        
        if i % 100 == 0:
            metrics = analyzer.get_performance_metrics()
            correlations = analyzer.get_asset_correlations()
            print(f"Step {i}: {len(correlations)} correlations detected")
            print(f"Graph metrics: {metrics['graph_metrics']}")

if __name__ == "__main__":
    asyncio.run(main())
