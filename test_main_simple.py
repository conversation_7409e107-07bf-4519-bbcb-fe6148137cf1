#!/usr/bin/env python3
"""
Simple test of main.py in test mode
"""

import os
import sys
import subprocess

print("TESTING MAIN.PY IN TEST MODE")
print("=" * 50)

# Change to correct directory
os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')

try:
    # Run main.py in test mode
    print("Running main.py --test...")
    
    result = subprocess.run(
        [sys.executable, "bybit_bot/main.py", "--test"],
        capture_output=True,
        text=True,
        timeout=60,  # 60 second timeout
        cwd="."
    )
    
    print(f"Return code: {result.returncode}")
    
    if result.stdout:
        print("\nSTDOUT:")
        print("-" * 30)
        print(result.stdout)
        print("-" * 30)
        
    if result.stderr:
        print("\nSTDERR:")
        print("-" * 30)
        print(result.stderr)
        print("-" * 30)
        
    # Analyze output
    if result.returncode == 0:
        print("\n✅ TEST MODE SUCCESSFUL")
    else:
        print(f"\n❌ TEST MODE FAILED (code: {result.returncode})")
        
    # Check for specific patterns
    output_text = result.stdout + result.stderr
    
    if "SUCCESS: All systems initialized" in output_text:
        print("✅ System initialization successful")
    elif "ERROR: System initialization failed" in output_text:
        print("❌ System initialization failed")
    elif "ERROR" in output_text:
        print("⚠️  Errors detected in output")
    elif "WARNING" in output_text:
        print("⚠️  Warnings detected in output")
    else:
        print("ℹ️  No clear status indicators found")
        
except subprocess.TimeoutExpired:
    print("❌ Process timed out after 60 seconds")
    
except Exception as e:
    print(f"❌ Error running test: {e}")
    
print("\nTest complete")
