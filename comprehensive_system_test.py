#!/usr/bin/env python3
"""
COMPREHENSIVE SYSTEM TEST - NO UNICODE CHARACTERS
Tests all components of the Bybit trading bot system
"""

import asyncio
import sys
import os
import time
import traceback
from datetime import datetime, timezone
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_status(message, status="INFO"):
    """Print status message with ASCII characters only"""
    timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
    print(f"[{timestamp}] {status}: {message}")

def print_separator(title=""):
    """Print separator with ASCII characters"""
    if title:
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    else:
        print(f"{'='*60}")

async def test_imports():
    """Test all critical imports"""
    print_status("Testing imports...")
    
    try:
        # Core imports
        from bybit_bot.core.config import BotConfig
        from bybit_bot.core.logger import TradingBotLogger
        from bybit_bot.database.connection import DatabaseManager
        print_status("Core imports: SUCCESS")
        
        # Exchange imports
        from bybit_bot.exchange.bybit_client import BybitClient
        print_status("Exchange imports: SUCCESS")
        
        # Main system import
        from bybit_bot.main import BybitTradingBotSystem
        print_status("Main system import: SUCCESS")
        
        return True
        
    except Exception as e:
        print_status(f"Import failed: {e}", "ERROR")
        print_status(f"Traceback: {traceback.format_exc()}", "ERROR")
        return False

async def test_config():
    """Test configuration loading"""
    print_status("Testing configuration...")
    
    try:
        from bybit_bot.core.config import BotConfig
        config = BotConfig()
        
        # Test basic config attributes
        assert hasattr(config, 'exchange'), "Config missing exchange section"
        assert hasattr(config, 'database'), "Config missing database section"
        assert hasattr(config, 'trading'), "Config missing trading section"
        
        print_status("Configuration test: SUCCESS")
        return True
        
    except Exception as e:
        print_status(f"Configuration test failed: {e}", "ERROR")
        return False

async def test_database():
    """Test database connection and operations"""
    print_status("Testing database...")
    
    try:
        from bybit_bot.core.config import BotConfig
        from bybit_bot.database.connection import DatabaseManager
        
        config = BotConfig()
        db_manager = DatabaseManager(config)
        
        # Initialize database
        await db_manager.initialize()
        print_status("Database initialization: SUCCESS")
        
        # Test connection
        await db_manager.test_connection()
        print_status("Database connection test: SUCCESS")
        
        # Test basic query
        result = await db_manager.execute("SELECT 1 as test_value")
        print_status("Database query test: SUCCESS")
        
        # Close connection
        await db_manager.close()
        print_status("Database close: SUCCESS")
        
        return True
        
    except Exception as e:
        print_status(f"Database test failed: {e}", "ERROR")
        print_status(f"Traceback: {traceback.format_exc()}", "ERROR")
        return False

async def test_main_system_initialization():
    """Test main system initialization without running"""
    print_status("Testing main system initialization...")
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        from bybit_bot.core.config import BotConfig
        
        config = BotConfig()
        
        # Create system instance
        system = BybitTradingBotSystem(config)
        print_status("System instance creation: SUCCESS")
        
        # Test initialization (but don't run)
        await system.initialize()
        print_status("System initialization: SUCCESS")
        
        # Test shutdown
        await system.shutdown()
        print_status("System shutdown: SUCCESS")
        
        return True
        
    except Exception as e:
        print_status(f"Main system test failed: {e}", "ERROR")
        print_status(f"Traceback: {traceback.format_exc()}", "ERROR")
        return False

async def run_comprehensive_test():
    """Run all tests"""
    print_separator("COMPREHENSIVE SYSTEM TEST")
    print_status("Starting comprehensive system test...")
    
    test_results = {}
    
    # Test 1: Imports
    print_separator("TEST 1: IMPORTS")
    test_results['imports'] = await test_imports()
    
    # Test 2: Configuration
    print_separator("TEST 2: CONFIGURATION")
    test_results['config'] = await test_config()
    
    # Test 3: Database
    print_separator("TEST 3: DATABASE")
    test_results['database'] = await test_database()
    
    # Test 4: Main System
    print_separator("TEST 4: MAIN SYSTEM")
    test_results['main_system'] = await test_main_system_initialization()
    
    # Summary
    print_separator("TEST RESULTS SUMMARY")
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results.items():
        status = "PASS" if result else "FAIL"
        print_status(f"{test_name.upper()}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print_separator()
    print_status(f"TOTAL TESTS: {len(test_results)}")
    print_status(f"PASSED: {passed}")
    print_status(f"FAILED: {failed}")
    
    if failed == 0:
        print_status("ALL TESTS PASSED - SYSTEM READY", "SUCCESS")
        return True
    else:
        print_status(f"{failed} TESTS FAILED - SYSTEM NEEDS ATTENTION", "WARNING")
        return False

if __name__ == "__main__":
    print_status("Starting comprehensive system test...")
    
    try:
        # Run the test
        result = asyncio.run(run_comprehensive_test())
        
        if result:
            print_status("COMPREHENSIVE TEST COMPLETED SUCCESSFULLY", "SUCCESS")
            sys.exit(0)
        else:
            print_status("COMPREHENSIVE TEST COMPLETED WITH FAILURES", "WARNING")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print_status("Test interrupted by user", "WARNING")
        sys.exit(1)
    except Exception as e:
        print_status(f"Test failed with exception: {e}", "ERROR")
        print_status(f"Traceback: {traceback.format_exc()}", "ERROR")
        sys.exit(1)
