#!/usr/bin/env python3
"""
Comprehensive System Check - Verify ALL errors are resolved
"""
import sys
import os
import asyncio
import traceback
import sqlite3
from pathlib import Path
from datetime import datetime

def check_python_syntax():
    """Check Python syntax compilation"""
    print("🔍 CHECKING PYTHON SYNTAX...")
    try:
        import py_compile
        py_compile.compile('bybit_bot/main.py', doraise=True)
        print("   ✅ main.py compiles without syntax errors")
        return True
    except Exception as e:
        print(f"   ❌ Syntax error: {e}")
        return False

def check_imports():
    """Check all critical imports"""
    print("🔍 CHECKING CRITICAL IMPORTS...")
    
    # Add current directory to path
    sys.path.append('.')
    
    critical_imports = [
        ('bybit_bot.main', 'BybitTradingBotSystem'),
        ('bybit_bot.core.config', 'EnhancedBotConfig'),
        ('bybit_bot.database.connection', 'DatabaseManager'),
        ('bybit_bot.ai.enhanced_multi_timeframe_learner', 'EnhancedMultiTimeframeLearner'),
        ('bybit_bot.ai.tier1_integration_manager', 'Tier1IntegrationManager'),
        ('bybit_bot.mcp.mpc_engine', 'MPCCryptographicEngine'),
    ]
    
    failed_imports = []
    for module_path, class_name in critical_imports:
        try:
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"   ✅ {module_path}.{class_name}")
        except Exception as e:
            print(f"   ❌ {module_path}.{class_name} - {e}")
            failed_imports.append((module_path, class_name, str(e)))
    
    return len(failed_imports) == 0, failed_imports

def check_class_structure():
    """Check BybitTradingBotSystem class structure"""
    print("🔍 CHECKING CLASS STRUCTURE...")
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        bot = BybitTradingBotSystem()
        
        # Check required methods
        required_methods = [
            'initialize_all_systems',
            'run',
            '_run_mpc_security_monitor',
            'cleanup',
            'start_trading_loop',
            'generate_final_detailed_report'
        ]
        
        missing_methods = []
        for method in required_methods:
            if hasattr(bot, method) and callable(getattr(bot, method)):
                print(f"   ✅ {method} method exists")
            else:
                print(f"   ❌ {method} method missing")
                missing_methods.append(method)
        
        return len(missing_methods) == 0, missing_methods
        
    except Exception as e:
        print(f"   ❌ Class structure error: {e}")
        return False, [str(e)]

def check_database_schema():
    """Check database tables and schema"""
    print("🔍 CHECKING DATABASE SCHEMA...")
    
    try:
        db_path = Path("bybit_bot/bybit_trading_bot.db")
        if not db_path.exists():
            print("   ❌ Database file does not exist")
            return False, ["Database file missing"]
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check required tables
        required_tables = [
            'strategy_memories',
            'trading_memories',
            'recursive_loops',
            'anomaly_remediations',
            'trades',
            'positions',
            'market_data'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"   ✅ {table} table exists")
            else:
                print(f"   ❌ {table} table missing")
                missing_tables.append(table)
        
        conn.close()
        return len(missing_tables) == 0, missing_tables
        
    except Exception as e:
        print(f"   ❌ Database check error: {e}")
        return False, [str(e)]

def check_file_structure():
    """Check for duplicate functions and proper structure"""
    print("🔍 CHECKING FILE STRUCTURE...")
    
    try:
        with open('bybit_bot/main.py', 'r') as f:
            content = f.read()
        
        issues = []
        
        # Check for duplicate sync_main functions
        sync_main_count = content.count('def sync_main(')
        if sync_main_count == 1:
            print("   ✅ Single sync_main function")
        else:
            print(f"   ❌ Found {sync_main_count} sync_main functions")
            issues.append(f"Duplicate sync_main functions: {sync_main_count}")
        
        # Check for duplicate main blocks
        main_block_count = content.count('if __name__ == "__main__":')
        if main_block_count == 1:
            print("   ✅ Single main execution block")
        else:
            print(f"   ❌ Found {main_block_count} main execution blocks")
            issues.append(f"Duplicate main blocks: {main_block_count}")
        
        # Check for methods inside main block (should not exist)
        lines = content.split('\n')
        in_main_block = False
        method_in_main = []
        
        for i, line in enumerate(lines):
            if 'if __name__ == "__main__":' in line:
                in_main_block = True
            elif in_main_block and line.strip().startswith('async def '):
                method_in_main.append(f"Line {i+1}: {line.strip()}")
        
        if len(method_in_main) == 0:
            print("   ✅ No methods inside main block")
        else:
            print(f"   ❌ Found {len(method_in_main)} methods inside main block")
            issues.extend(method_in_main)
        
        return len(issues) == 0, issues
        
    except Exception as e:
        print(f"   ❌ File structure check error: {e}")
        return False, [str(e)]

def check_async_functionality():
    """Test async functionality with timeout"""
    print("🔍 CHECKING ASYNC FUNCTIONALITY...")
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        bot = BybitTradingBotSystem()
        
        async def test_async_methods():
            try:
                # Test initialization (with short timeout)
                print("   Testing initialize_all_systems...")
                result = await asyncio.wait_for(bot.initialize_all_systems(), timeout=30.0)
                print(f"   ✅ Initialization completed: {result}")
                
                # Test cleanup
                print("   Testing cleanup...")
                cleanup_result = await bot.cleanup()
                print(f"   ✅ Cleanup completed: {cleanup_result}")
                
                return True, []
                
            except asyncio.TimeoutError:
                return False, ["Async methods timed out"]
            except Exception as e:
                return False, [f"Async error: {str(e)}"]
        
        # Run with overall timeout
        result, errors = asyncio.run(asyncio.wait_for(test_async_methods(), timeout=60.0))
        return result, errors
        
    except Exception as e:
        print(f"   ❌ Async functionality error: {e}")
        return False, [str(e)]

def main():
    print("🚀 COMPREHENSIVE SYSTEM CHECK")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print("=" * 60)
    
    all_checks_passed = True
    all_issues = []
    
    # Run all checks
    checks = [
        ("Python Syntax", check_python_syntax),
        ("Critical Imports", check_imports),
        ("Class Structure", check_class_structure),
        ("Database Schema", check_database_schema),
        ("File Structure", check_file_structure),
        ("Async Functionality", check_async_functionality),
    ]
    
    for check_name, check_func in checks:
        print(f"\n{check_name.upper()}:")
        try:
            if check_name in ["Python Syntax"]:
                # Simple boolean return
                result = check_func()
                if not result:
                    all_checks_passed = False
                    all_issues.append(f"{check_name} failed")
            else:
                # Tuple return (success, issues)
                success, issues = check_func()
                if not success:
                    all_checks_passed = False
                    all_issues.extend([f"{check_name}: {issue}" for issue in issues])
        except Exception as e:
            print(f"   ❌ {check_name} check failed: {e}")
            all_checks_passed = False
            all_issues.append(f"{check_name} check exception: {str(e)}")
    
    # Final report
    print("\n" + "=" * 60)
    print("🎯 FINAL SYSTEM STATUS")
    print("=" * 60)
    
    if all_checks_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("✅ System is ERROR-FREE and ready for operation")
        print("✅ All critical functionality verified")
        print("✅ Database schema complete")
        print("✅ Async operations working")
        print("✅ No syntax or structural issues")
        return True
    else:
        print("❌ ISSUES DETECTED:")
        for issue in all_issues:
            print(f"   • {issue}")
        print(f"\nTotal issues: {len(all_issues)}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nExit code: {0 if success else 1}")
    sys.exit(0 if success else 1)
