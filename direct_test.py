#!/usr/bin/env python3
"""
Direct test execution - NO UNICODE
"""

import os
import sys
import ast
import traceback
from pathlib import Path

def main():
    print("DIRECT MAIN.PY TEST")
    print("=" * 25)
    
    # Change to correct directory
    target_dir = r'E:\The_real_deal_copy\Bybit_Bot\BOT'
    if os.path.exists(target_dir):
        os.chdir(target_dir)
        print(f"Directory: {os.getcwd()}")
    else:
        print(f"ERROR: Directory not found: {target_dir}")
        return False
    
    # Add to path
    sys.path.insert(0, '.')
    
    # Test 1: Check file exists
    main_file = "bybit_bot/main.py"
    if not os.path.exists(main_file):
        print(f"ERROR: {main_file} not found")
        return False
    print(f"SUCCESS: {main_file} exists")
    
    # Test 2: Syntax check
    print("\nTesting syntax...")
    try:
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse with AST
        ast.parse(content, filename=main_file)
        print("SUCCESS: Syntax is valid")
        
        # Compile
        compile(content, main_file, 'exec')
        print("SUCCESS: Compilation successful")
        
    except SyntaxError as e:
        print(f"SYNTAX ERROR: {e}")
        print(f"  Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"ERROR: {e}")
        return False
    
    # Test 3: Import bybit_bot package
    print("\nTesting package import...")
    try:
        import bybit_bot
        print("SUCCESS: bybit_bot package imported")
    except Exception as e:
        print(f"ERROR: Package import failed - {e}")
        traceback.print_exc()
        return False
    
    # Test 4: Import main module
    print("\nTesting main module import...")
    try:
        from bybit_bot import main
        print("SUCCESS: main module imported")
    except Exception as e:
        print(f"ERROR: Main module import failed - {e}")
        traceback.print_exc()
        return False
    
    # Test 5: Import main class
    print("\nTesting main class import...")
    try:
        from bybit_bot.main import BybitTradingBotSystem
        print("SUCCESS: BybitTradingBotSystem imported")
    except Exception as e:
        print(f"ERROR: Main class import failed - {e}")
        traceback.print_exc()
        return False
    
    # Test 6: Create instance
    print("\nTesting instance creation...")
    try:
        system = BybitTradingBotSystem()
        print("SUCCESS: Instance created")
        print(f"  is_running: {system.is_running}")
        print(f"  start_time: {system.start_time}")
        print(f"  total_profit: {system.total_profit}")
    except Exception as e:
        print(f"ERROR: Instance creation failed - {e}")
        traceback.print_exc()
        return False
    
    print("\nALL TESTS PASSED")
    print("main.py basic functionality works")
    return True

# Execute directly
if __name__ == "__main__":
    try:
        result = main()
        if result:
            print("\nFINAL RESULT: SUCCESS")
        else:
            print("\nFINAL RESULT: FAILED")
    except Exception as e:
        print(f"\nFINAL RESULT: EXCEPTION - {e}")
        traceback.print_exc()

# Also execute when imported
try:
    result = main()
    print(f"Import execution result: {result}")
except Exception as e:
    print(f"Import execution error: {e}")
