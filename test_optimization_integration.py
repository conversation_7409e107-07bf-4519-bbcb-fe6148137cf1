#!/usr/bin/env python3
"""
Comprehensive Test Suite for Optimization Integration
Tests all 8 optimization tiers integrated into main.py system

Test Categories:
1. Component Initialization Tests
2. Market Data Processing Tests  
3. Optimization Pipeline Tests
4. Performance Benchmarking Tests
5. Error Handling Tests
6. Integration Tests
"""

import asyncio
import time
import sys
import os
from pathlib import Path
import traceback
from typing import Dict, Any, List

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_optimization_manager_initialization():
    """Test 1: Optimization Manager Initialization"""
    print("\n" + "="*60)
    print("TEST 1: OPTIMIZATION MANAGER INITIALIZATION")
    print("="*60)
    
    try:
        from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
        
        # Test configuration creation
        config = OptimizationConfig(
            enable_streaming=True,
            enable_compression=True,
            enable_multi_agent_rl=True,
            enable_graph_nn=True,
            enable_edge_computing=True,
            enable_quantum=True,
            enable_feature_engineering=True,
            enable_adaptive_learning=True
        )
        
        print("✓ OptimizationConfig created successfully")
        
        # Test manager creation
        manager = OptimizationComponentsManager(config)
        print("✓ OptimizationComponentsManager created successfully")
        
        # Test component initialization
        start_time = time.time()
        success = await manager.initialize_all_components()
        init_time = time.time() - start_time
        
        print(f"✓ Component initialization completed in {init_time:.3f}s")
        print(f"✓ Initialization success rate: {success}")
        
        # Test health check
        health = await manager.health_check()
        healthy_components = sum(1 for status in health.values() if status)
        total_components = len(health)
        
        print(f"✓ Health check: {healthy_components}/{total_components} components healthy")
        
        # Test performance metrics
        metrics = await manager.get_performance_metrics()
        print(f"✓ Performance metrics collected: {len(metrics)} categories")
        
        # Cleanup
        await manager.shutdown()
        print("✓ Manager shutdown completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        traceback.print_exc()
        return False

async def test_market_data_processing():
    """Test 2: Market Data Processing Pipeline"""
    print("\n" + "="*60)
    print("TEST 2: MARKET DATA PROCESSING PIPELINE")
    print("="*60)
    
    try:
        from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
        
        config = OptimizationConfig()
        manager = OptimizationComponentsManager(config)
        
        # Initialize components
        await manager.initialize_all_components()
        
        # Test market data processing
        test_market_data = {
            'symbol': 'BTC/USDT',
            'ohlcv': {
                'open': 50000.0,
                'high': 50500.0,
                'low': 49800.0,
                'close': 50200.0,
                'volume': 1000.0
            },
            'timestamp': time.time(),
            'historical_data': [
                {'open': 49900, 'high': 50100, 'low': 49800, 'close': 50000, 'volume': 900},
                {'open': 50000, 'high': 50500, 'low': 49800, 'close': 50200, 'volume': 1000}
            ]
        }
        
        # Process data multiple times to test performance
        processing_times = []
        results_list = []
        
        for i in range(5):
            start_time = time.time()
            results = await manager.process_market_data(test_market_data)
            processing_time = time.time() - start_time
            
            processing_times.append(processing_time)
            results_list.append(results)
            
            print(f"  Processing run {i+1}: {processing_time:.4f}s")
        
        # Analyze results
        avg_processing_time = sum(processing_times) / len(processing_times)
        print(f"✓ Average processing time: {avg_processing_time:.4f}s")
        
        # Check result consistency
        if results_list:
            sample_result = results_list[0]
            print(f"✓ Sample result keys: {list(sample_result.keys())}")
            
            if 'processing_time' in sample_result:
                print(f"✓ Processing time recorded: {sample_result['processing_time']:.4f}s")
            
            if 'features' in sample_result:
                print(f"✓ Features extracted: {len(sample_result.get('features', []))}")
            
            if 'compression_ratio' in sample_result:
                print(f"✓ Compression ratio: {sample_result['compression_ratio']:.2f}")
        
        await manager.shutdown()
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        traceback.print_exc()
        return False

async def test_main_system_integration():
    """Test 3: Main System Integration"""
    print("\n" + "="*60)
    print("TEST 3: MAIN SYSTEM INTEGRATION")
    print("="*60)
    
    try:
        # Test importing main system
        from bybit_bot.main import BybitTradingBotSystem
        print("✓ Main system import successful")
        
        # Test system creation
        system = BybitTradingBotSystem()
        print("✓ Trading bot system created")
        
        # Test component initialization
        init_success = await system.initialize_components()
        print(f"✓ Component initialization: {init_success}")
        
        # Check if optimization attributes exist
        optimization_attrs = [
            'optimization_manager', 'streaming_processor', 'compression_engine',
            'multi_agent_rl', 'graph_neural_networks', 'edge_computing',
            'quantum_engine', 'feature_pipeline', 'adaptive_learning'
        ]
        
        for attr in optimization_attrs:
            has_attr = hasattr(system, attr)
            print(f"  {attr}: {'✓' if has_attr else '✗'}")
        
        # Test cleanup
        await system.cleanup()
        print("✓ System cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        traceback.print_exc()
        return False

async def test_performance_benchmarks():
    """Test 4: Performance Benchmarking"""
    print("\n" + "="*60)
    print("TEST 4: PERFORMANCE BENCHMARKING")
    print("="*60)
    
    try:
        from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
        
        # Test with different configurations
        configs = [
            ("Minimal", OptimizationConfig(
                enable_streaming=False,
                enable_compression=True,
                enable_multi_agent_rl=False,
                enable_graph_nn=False,
                enable_edge_computing=False,
                enable_quantum=False,
                enable_feature_engineering=True,
                enable_adaptive_learning=False
            )),
            ("Standard", OptimizationConfig(
                enable_streaming=True,
                enable_compression=True,
                enable_multi_agent_rl=True,
                enable_graph_nn=False,
                enable_edge_computing=False,
                enable_quantum=False,
                enable_feature_engineering=True,
                enable_adaptive_learning=True
            )),
            ("Full", OptimizationConfig(
                enable_streaming=True,
                enable_compression=True,
                enable_multi_agent_rl=True,
                enable_graph_nn=True,
                enable_edge_computing=True,
                enable_quantum=True,
                enable_feature_engineering=True,
                enable_adaptive_learning=True
            ))
        ]
        
        benchmark_results = {}
        
        for config_name, config in configs:
            print(f"\n  Testing {config_name} Configuration:")
            
            manager = OptimizationComponentsManager(config)
            
            # Benchmark initialization
            start_time = time.time()
            success = await manager.initialize_all_components()
            init_time = time.time() - start_time
            
            print(f"    Initialization: {init_time:.3f}s (Success: {success})")
            
            # Benchmark processing
            test_data = {
                'ohlcv': {'open': 50000, 'high': 50100, 'low': 49900, 'close': 50050, 'volume': 1000},
                'timestamp': time.time()
            }
            
            processing_times = []
            for _ in range(3):
                start_time = time.time()
                await manager.process_market_data(test_data)
                processing_times.append(time.time() - start_time)
            
            avg_processing = sum(processing_times) / len(processing_times)
            print(f"    Avg Processing: {avg_processing:.4f}s")
            
            # Get performance metrics
            metrics = await manager.get_performance_metrics()
            uptime = metrics['manager_metrics']['uptime_seconds']
            processed_samples = metrics['manager_metrics']['total_processed_samples']
            
            print(f"    Uptime: {uptime:.1f}s")
            print(f"    Processed Samples: {processed_samples}")
            
            benchmark_results[config_name] = {
                'init_time': init_time,
                'avg_processing': avg_processing,
                'success': success
            }
            
            await manager.shutdown()
        
        # Summary
        print(f"\n  Benchmark Summary:")
        for config_name, results in benchmark_results.items():
            print(f"    {config_name}: Init={results['init_time']:.3f}s, Process={results['avg_processing']:.4f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        traceback.print_exc()
        return False

async def test_error_handling():
    """Test 5: Error Handling and Resilience"""
    print("\n" + "="*60)
    print("TEST 5: ERROR HANDLING AND RESILIENCE")
    print("="*60)
    
    try:
        from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
        
        # Test with invalid data
        config = OptimizationConfig()
        manager = OptimizationComponentsManager(config)
        
        await manager.initialize_all_components()
        
        # Test with malformed market data
        invalid_data_tests = [
            {},  # Empty data
            {'invalid': 'data'},  # Wrong structure
            {'ohlcv': {}},  # Empty OHLCV
            {'ohlcv': {'open': 'invalid'}},  # Invalid types
        ]
        
        for i, invalid_data in enumerate(invalid_data_tests):
            try:
                result = await manager.process_market_data(invalid_data)
                print(f"  ✓ Invalid data test {i+1}: Handled gracefully")
                if 'error' in result:
                    print(f"    Error recorded: {result['error'][:50]}...")
            except Exception as e:
                print(f"  ✗ Invalid data test {i+1}: Unhandled exception: {e}")
        
        # Test component health after errors
        health = await manager.health_check()
        healthy_count = sum(1 for status in health.values() if status)
        print(f"  ✓ Components still healthy after errors: {healthy_count}/{len(health)}")
        
        await manager.shutdown()
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        traceback.print_exc()
        return False

async def run_all_tests():
    """Run all optimization integration tests"""
    print("OPTIMIZATION INTEGRATION TEST SUITE")
    print("="*80)
    print("Testing all 8 optimization tiers integrated into main.py")
    print("="*80)
    
    tests = [
        ("Optimization Manager Initialization", test_optimization_manager_initialization),
        ("Market Data Processing Pipeline", test_market_data_processing),
        ("Main System Integration", test_main_system_integration),
        ("Performance Benchmarking", test_performance_benchmarks),
        ("Error Handling and Resilience", test_error_handling)
    ]
    
    results = {}
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\nRunning: {test_name}")
        try:
            result = await test_func()
            results[test_name] = result
            status = "PASS" if result else "FAIL"
            print(f"Result: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"Result: FAIL (Exception: {e})")
    
    total_time = time.time() - start_time
    
    # Final summary
    print("\n" + "="*80)
    print("TEST SUITE SUMMARY")
    print("="*80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{status:4} | {test_name}")
    
    print("-" * 80)
    print(f"TOTAL: {passed}/{total} tests passed ({passed/total:.1%})")
    print(f"TIME:  {total_time:.1f} seconds")
    
    if passed == total:
        print("✓ ALL TESTS PASSED - OPTIMIZATION INTEGRATION SUCCESSFUL")
        return True
    else:
        print("✗ SOME TESTS FAILED - REVIEW INTEGRATION")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nTest suite interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test suite failed with exception: {e}")
        traceback.print_exc()
        sys.exit(1)
