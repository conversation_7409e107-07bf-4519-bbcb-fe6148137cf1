#!/usr/bin/env python3
"""
Enhanced Multi-Timeframe Learning System
Learns from individual trades and multiple timeframe P&L with comprehensive environmental data
"""

import asyncio
import logging
import statistics
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from ..core.config import BotConfig
from ..database.connection import DatabaseManager


class TimeframeType(Enum):
    """Learning timeframes"""
    TRADE = "individual_trade"
    HOURLY = "1h"
    FOUR_HOURLY = "4h"
    EIGHT_HOURLY = "8h"
    DAILY = "24h"
    WEEKLY = "7d"


@dataclass
class EnvironmentalContext:
    """Comprehensive environmental data for learning"""
    # Market Data
    price: float
    volume: float
    volatility: float
    spread: float
    market_cap: float
    
    # Technical Indicators
    rsi: float
    macd: float
    bollinger_position: float
    moving_avg_20: float
    moving_avg_50: float
    moving_avg_200: float
    
    # Market Microstructure
    order_book_imbalance: float
    trade_intensity: float
    large_order_flow: float
    funding_rate: float
    open_interest: float
    
    # Sentiment Data
    news_sentiment: float
    social_sentiment: float
    fear_greed_index: float
    
    # Economic Context
    market_regime: str
    volatility_regime: str
    correlation_btc: float
    correlation_eth: float
    
    # Time Context
    hour_of_day: int
    day_of_week: int
    month: int
    is_weekend: bool
    is_holiday: bool
    
    # System State
    position_size: float
    leverage: float
    account_balance: float
    daily_pnl: float
    win_rate_24h: float


@dataclass
class MultiTimeframeLearningResult:
    """Result of multi-timeframe learning"""
    timeframe: TimeframeType
    profit_loss: float
    reward_score: float
    environmental_context: EnvironmentalContext
    adaptations: Dict[str, Any]
    learning_insights: Dict[str, Any]
    confidence_change: float
    risk_adjustment: float


class EnhancedMultiTimeframeLearner:
    """Enhanced learning system with multi-timeframe and environmental data integration"""
    
    def __init__(self, config: BotConfig, db_path: str = "bybit_trading_bot.db"):
        self.config = config
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.database_manager = DatabaseManager(db_path)
        
        # Multi-timeframe tracking
        self.timeframe_pnl = {
            TimeframeType.HOURLY: 0.0,
            TimeframeType.FOUR_HOURLY: 0.0,
            TimeframeType.EIGHT_HOURLY: 0.0,
            TimeframeType.DAILY: 0.0,
            TimeframeType.WEEKLY: 0.0
        }
        
        # Environmental learning weights
        self.environmental_weights = {
            'market_data': 0.25,
            'technical_indicators': 0.20,
            'microstructure': 0.15,
            'sentiment': 0.15,
            'economic_context': 0.10,
            'time_context': 0.10,
            'system_state': 0.05
        }
        
        # Adaptive learning parameters
        self.learning_rates = {
            TimeframeType.TRADE: 0.1,
            TimeframeType.HOURLY: 0.08,
            TimeframeType.FOUR_HOURLY: 0.06,
            TimeframeType.EIGHT_HOURLY: 0.04,
            TimeframeType.DAILY: 0.02,
            TimeframeType.WEEKLY: 0.01
        }
        
        # Reward scaling factors for different timeframes
        self.reward_scaling = {
            TimeframeType.TRADE: 1.0,
            TimeframeType.HOURLY: 1.5,
            TimeframeType.FOUR_HOURLY: 2.0,
            TimeframeType.EIGHT_HOURLY: 2.5,
            TimeframeType.DAILY: 3.0,
            TimeframeType.WEEKLY: 4.0
        }
        
        # Environmental feature importance tracking
        self.feature_importance = {}
        self.learning_history = []
        
    async def learn_from_trade_with_environment(self, trade_data: Dict[str, Any], 
                                              environmental_context: EnvironmentalContext) -> MultiTimeframeLearningResult:
        """Learn from individual trade with full environmental context"""
        try:
            profit_loss = float(trade_data.get('profit_loss', 0))
            
            # Calculate enhanced reward score with environmental factors
            base_reward = self._calculate_enhanced_reward_score(profit_loss, environmental_context)
            
            # Apply timeframe scaling
            scaled_reward = base_reward * self.reward_scaling[TimeframeType.TRADE]
            
            # Analyze environmental impact
            environmental_impact = await self._analyze_environmental_impact(
                profit_loss, environmental_context
            )
            
            # Generate adaptations based on environmental learning
            adaptations = await self._generate_environmental_adaptations(
                profit_loss, environmental_context, environmental_impact
            )
            
            # Update feature importance
            await self._update_feature_importance(environmental_context, profit_loss)
            
            # Store learning result
            learning_result = MultiTimeframeLearningResult(
                timeframe=TimeframeType.TRADE,
                profit_loss=profit_loss,
                reward_score=scaled_reward,
                environmental_context=environmental_context,
                adaptations=adaptations,
                learning_insights=environmental_impact,
                confidence_change=adaptations.get('confidence_change', 0.0),
                risk_adjustment=adaptations.get('risk_adjustment', 0.0)
            )
            
            await self._store_learning_result(learning_result)
            
            self.logger.info(f"ENHANCED TRADE LEARNING: ${profit_loss:.2f} -> Reward: {scaled_reward:.2f}")
            self.logger.info(f"Environmental factors: {len(environmental_impact)} analyzed")
            
            return learning_result
            
        except Exception as e:
            self.logger.error(f"Error in enhanced trade learning: {e}")
            raise
    
    async def learn_from_timeframe_pnl(self, timeframe: TimeframeType, 
                                     pnl: float, 
                                     environmental_context: EnvironmentalContext) -> MultiTimeframeLearningResult:
        """Learn from timeframe-specific P&L with environmental context"""
        try:
            # Update timeframe tracking
            self.timeframe_pnl[timeframe] = pnl
            
            # Calculate timeframe-specific reward
            base_reward = self._calculate_enhanced_reward_score(pnl, environmental_context)
            scaled_reward = base_reward * self.reward_scaling[timeframe]
            
            # Analyze timeframe-specific patterns
            timeframe_patterns = await self._analyze_timeframe_patterns(timeframe, pnl, environmental_context)
            
            # Generate timeframe-specific adaptations
            adaptations = await self._generate_timeframe_adaptations(
                timeframe, pnl, environmental_context, timeframe_patterns
            )
            
            # Cross-timeframe correlation analysis
            correlation_insights = await self._analyze_cross_timeframe_correlations()
            
            learning_result = MultiTimeframeLearningResult(
                timeframe=timeframe,
                profit_loss=pnl,
                reward_score=scaled_reward,
                environmental_context=environmental_context,
                adaptations=adaptations,
                learning_insights={**timeframe_patterns, **correlation_insights},
                confidence_change=adaptations.get('confidence_change', 0.0),
                risk_adjustment=adaptations.get('risk_adjustment', 0.0)
            )
            
            await self._store_learning_result(learning_result)
            
            self.logger.info(f"TIMEFRAME LEARNING [{timeframe.value}]: ${pnl:.2f} -> Reward: {scaled_reward:.2f}")
            
            return learning_result
            
        except Exception as e:
            self.logger.error(f"Error in timeframe learning: {e}")
            raise
    
    def _calculate_enhanced_reward_score(self, profit_loss: float, 
                                       environmental_context: EnvironmentalContext) -> float:
        """Calculate enhanced reward score with environmental factors"""
        if profit_loss == 0:
            return 0.0
        
        # Base exponential reward
        if profit_loss > 0:
            base_reward = profit_loss ** 0.5
        else:
            base_reward = -(abs(profit_loss) ** 0.5)
        
        # Environmental multipliers
        env_multiplier = 1.0
        
        # Market condition multiplier
        if environmental_context.volatility > 0.05:  # High volatility
            env_multiplier *= 1.2 if profit_loss > 0 else 0.8
        
        # Sentiment multiplier
        if environmental_context.news_sentiment > 0.6:  # Positive sentiment
            env_multiplier *= 1.1 if profit_loss > 0 else 0.9
        
        # Technical indicator multiplier
        if environmental_context.rsi > 70 or environmental_context.rsi < 30:  # Extreme RSI
            env_multiplier *= 1.15 if profit_loss > 0 else 0.85
        
        # Time-based multiplier
        if environmental_context.hour_of_day in [9, 10, 14, 15]:  # High activity hours
            env_multiplier *= 1.1
        
        enhanced_reward = base_reward * env_multiplier
        
        # Cap rewards
        return max(-25.0, min(25.0, enhanced_reward))
    
    async def _analyze_environmental_impact(self, profit_loss: float, 
                                          environmental_context: EnvironmentalContext) -> Dict[str, Any]:
        """Analyze which environmental factors contributed to the outcome"""
        impact_analysis = {
            'primary_factors': [],
            'secondary_factors': [],
            'correlation_scores': {},
            'feature_contributions': {}
        }
        
        # Analyze each environmental category
        categories = {
            'volatility': environmental_context.volatility,
            'sentiment': environmental_context.news_sentiment,
            'rsi': environmental_context.rsi,
            'volume': environmental_context.volume,
            'spread': environmental_context.spread,
            'funding_rate': environmental_context.funding_rate,
            'hour_of_day': environmental_context.hour_of_day,
            'market_regime': 1.0 if environmental_context.market_regime == 'bullish' else 0.0
        }
        
        # Calculate correlations with profit/loss
        for factor, value in categories.items():
            if isinstance(value, (int, float)):
                # Simple correlation approximation
                correlation = abs(value * profit_loss) / (abs(profit_loss) + 1)
                impact_analysis['correlation_scores'][factor] = correlation
                
                if correlation > 0.5:
                    impact_analysis['primary_factors'].append(factor)
                elif correlation > 0.3:
                    impact_analysis['secondary_factors'].append(factor)
        
        return impact_analysis
    
    async def _generate_environmental_adaptations(self, profit_loss: float,
                                                environmental_context: EnvironmentalContext,
                                                environmental_impact: Dict[str, Any]) -> Dict[str, Any]:
        """Generate adaptations based on environmental learning"""
        adaptations = {
            'confidence_change': 0.0,
            'risk_adjustment': 0.0,
            'position_size_adjustment': 0.0,
            'strategy_weight_changes': {},
            'environmental_preferences': {}
        }
        
        # Adapt based on primary environmental factors
        for factor in environmental_impact['primary_factors']:
            if factor == 'volatility' and profit_loss > 0:
                adaptations['position_size_adjustment'] += 0.1  # Increase size in profitable volatility
            elif factor == 'sentiment' and profit_loss > 0:
                adaptations['confidence_change'] += 0.05  # Boost confidence with positive sentiment
            elif factor == 'rsi' and profit_loss < 0:
                adaptations['risk_adjustment'] -= 0.1  # Reduce risk in extreme RSI conditions
        
        # Time-based adaptations
        if environmental_context.hour_of_day in [9, 10, 14, 15] and profit_loss > 0:
            adaptations['environmental_preferences']['preferred_hours'] = [9, 10, 14, 15]
        
        return adaptations

    async def _analyze_timeframe_patterns(self, timeframe: TimeframeType,
                                        pnl: float,
                                        environmental_context: EnvironmentalContext) -> Dict[str, Any]:
        """Analyze patterns specific to timeframe"""
        patterns = {
            'timeframe_performance': {},
            'environmental_correlations': {},
            'optimal_conditions': {},
            'risk_patterns': {}
        }

        # Timeframe-specific analysis
        if timeframe == TimeframeType.HOURLY:
            patterns['timeframe_performance']['hourly_trend'] = 'positive' if pnl > 0 else 'negative'
            patterns['optimal_conditions']['hour'] = environmental_context.hour_of_day
        elif timeframe == TimeframeType.DAILY:
            patterns['timeframe_performance']['daily_consistency'] = abs(pnl) / max(environmental_context.daily_pnl, 1)
            patterns['optimal_conditions']['day_type'] = 'weekend' if environmental_context.is_weekend else 'weekday'

        return patterns

    async def _generate_timeframe_adaptations(self, timeframe: TimeframeType,
                                            pnl: float,
                                            environmental_context: EnvironmentalContext,
                                            patterns: Dict[str, Any]) -> Dict[str, Any]:
        """Generate timeframe-specific adaptations"""
        adaptations = {
            'confidence_change': 0.0,
            'risk_adjustment': 0.0,
            'timeframe_weights': {},
            'strategy_adjustments': {}
        }

        # Timeframe-specific learning rates
        learning_rate = self.learning_rates[timeframe]

        if pnl > 0:
            # Positive timeframe performance
            adaptations['confidence_change'] = learning_rate * 0.1
            adaptations['timeframe_weights'][timeframe.value] = 1.0 + (learning_rate * 0.2)
        else:
            # Negative timeframe performance
            adaptations['confidence_change'] = -learning_rate * 0.1
            adaptations['risk_adjustment'] = -learning_rate * 0.15

        return adaptations

    async def _analyze_cross_timeframe_correlations(self) -> Dict[str, Any]:
        """Analyze correlations between different timeframes"""
        correlations = {
            'timeframe_alignment': {},
            'cascade_effects': {},
            'divergence_signals': {}
        }

        # Check alignment between timeframes
        timeframes = [TimeframeType.HOURLY, TimeframeType.FOUR_HOURLY, TimeframeType.DAILY]
        for i, tf1 in enumerate(timeframes):
            for tf2 in timeframes[i+1:]:
                pnl1 = self.timeframe_pnl.get(tf1, 0)
                pnl2 = self.timeframe_pnl.get(tf2, 0)

                if (pnl1 > 0 and pnl2 > 0) or (pnl1 < 0 and pnl2 < 0):
                    correlations['timeframe_alignment'][f"{tf1.value}_{tf2.value}"] = 'aligned'
                else:
                    correlations['divergence_signals'][f"{tf1.value}_{tf2.value}"] = 'divergent'

        return correlations

    async def _update_feature_importance(self, environmental_context: EnvironmentalContext,
                                       profit_loss: float):
        """Update importance scores for environmental features"""
        features = {
            'volatility': environmental_context.volatility,
            'rsi': environmental_context.rsi,
            'sentiment': environmental_context.news_sentiment,
            'volume': environmental_context.volume,
            'spread': environmental_context.spread,
            'hour': environmental_context.hour_of_day,
            'funding_rate': environmental_context.funding_rate
        }

        for feature, value in features.items():
            if feature not in self.feature_importance:
                self.feature_importance[feature] = {'total_impact': 0.0, 'count': 0}

            # Update importance based on correlation with profit/loss
            impact = abs(profit_loss) * abs(value) if isinstance(value, (int, float)) else abs(profit_loss)
            self.feature_importance[feature]['total_impact'] += impact
            self.feature_importance[feature]['count'] += 1

    async def _store_learning_result(self, result: MultiTimeframeLearningResult):
        """Store learning result in database"""
        try:
            query = """
            INSERT INTO multi_timeframe_learning
            (timeframe, profit_loss, reward_score, environmental_context,
             adaptations, learning_insights, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            await self.database_manager.execute(query, (
                result.timeframe.value,
                result.profit_loss,
                result.reward_score,
                json.dumps(result.environmental_context.__dict__),
                json.dumps(result.adaptations),
                json.dumps(result.learning_insights),
                datetime.now(timezone.utc).isoformat()
            ))

        except Exception as e:
            self.logger.error(f"Error storing learning result: {e}")

    async def get_learning_insights(self) -> Dict[str, Any]:
        """Get comprehensive learning insights"""
        insights = {
            'feature_importance_ranking': {},
            'timeframe_performance': {},
            'environmental_preferences': {},
            'optimization_recommendations': []
        }

        # Rank feature importance
        for feature, data in self.feature_importance.items():
            if data['count'] > 0:
                avg_importance = data['total_impact'] / data['count']
                insights['feature_importance_ranking'][feature] = avg_importance

        # Sort by importance
        insights['feature_importance_ranking'] = dict(
            sorted(insights['feature_importance_ranking'].items(),
                  key=lambda x: x[1], reverse=True)
        )

        # Timeframe performance summary
        for timeframe, pnl in self.timeframe_pnl.items():
            insights['timeframe_performance'][timeframe.value] = {
                'current_pnl': pnl,
                'performance_rating': 'positive' if pnl > 0 else 'negative'
            }

        return insights

    async def optimize_learning_parameters(self):
        """Optimize learning parameters based on performance"""
        insights = await self.get_learning_insights()

        # Adjust learning rates based on timeframe performance
        for timeframe in TimeframeType:
            if timeframe in self.timeframe_pnl:
                pnl = self.timeframe_pnl[timeframe]
                if pnl > 0:
                    # Increase learning rate for profitable timeframes
                    self.learning_rates[timeframe] = min(0.15, self.learning_rates[timeframe] * 1.1)
                else:
                    # Decrease learning rate for unprofitable timeframes
                    self.learning_rates[timeframe] = max(0.01, self.learning_rates[timeframe] * 0.9)

        # Adjust environmental weights based on feature importance
        top_features = list(insights['feature_importance_ranking'].keys())[:3]
        for category in self.environmental_weights:
            if any(feature in category for feature in top_features):
                self.environmental_weights[category] = min(0.4, self.environmental_weights[category] * 1.1)

        self.logger.info("Learning parameters optimized based on performance data")
