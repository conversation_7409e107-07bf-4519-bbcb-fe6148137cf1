#!/usr/bin/env python3
"""
COMPREHENSIVE DEEP PROCESS ERROR CHECK
Focus on time-related issues and backup cleanup
"""

import sys
import os
import time
import asyncio
import glob
import datetime
from pathlib import Path
import psutil
import threading
import traceback

print("🔍 DEEP PROCESS ERROR CHECK - STARTING")
print("=" * 80)

class DeepProcessChecker:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.backup_files = []
        self.time_issues = []
        
    def log_error(self, error_msg):
        self.errors.append(f"❌ ERROR: {error_msg}")
        print(f"❌ ERROR: {error_msg}")
        
    def log_warning(self, warning_msg):
        self.warnings.append(f"⚠️  WARNING: {warning_msg}")
        print(f"⚠️  WARNING: {warning_msg}")
        
    def log_info(self, info_msg):
        print(f"ℹ️  INFO: {info_msg}")

    def check_backup_files_crisis(self):
        """Check for backup file explosion"""
        print("\n🗂️  CHECKING BACKUP FILES CRISIS...")
        
        # Find all backup files
        backup_patterns = [
            "**/*.backup_*",
            "**/*_backup_*", 
            "**/*.bak",
            "**/*~"
        ]
        
        total_backups = 0
        for pattern in backup_patterns:
            backup_files = list(Path("bybit_bot").glob(pattern))
            total_backups += len(backup_files)
            self.backup_files.extend(backup_files)
            
        self.log_info(f"Found {total_backups} backup files")
        
        if total_backups > 50:
            self.log_error(f"BACKUP EXPLOSION: {total_backups} backup files found!")
            
        # Check specific problematic files
        main_backups = list(Path("bybit_bot").glob("main.py.backup_*"))
        if len(main_backups) > 10:
            self.log_error(f"main.py has {len(main_backups)} backup files!")
            
        # Check backup file sizes
        total_backup_size = 0
        for backup_file in self.backup_files:
            try:
                total_backup_size += backup_file.stat().st_size
            except:
                pass
                
        size_mb = total_backup_size / (1024 * 1024)
        self.log_info(f"Total backup files size: {size_mb:.2f} MB")
        
        if size_mb > 100:
            self.log_warning(f"Backup files consuming {size_mb:.2f} MB of disk space!")

    def check_time_synchronization(self):
        """Check for time-related issues"""
        print("\n⏰ CHECKING TIME SYNCHRONIZATION...")
        
        try:
            # Check system time
            system_time = time.time()
            datetime_now = datetime.datetime.now()
            utc_now = datetime.datetime.utcnow()
            
            self.log_info(f"System timestamp: {system_time}")
            self.log_info(f"Local time: {datetime_now}")
            self.log_info(f"UTC time: {utc_now}")
            
            # Check timezone offset
            timezone_offset = (datetime_now - utc_now).total_seconds()
            self.log_info(f"Timezone offset: {timezone_offset} seconds")
            
            # Check for time drift
            start_time = time.time()
            time.sleep(0.1)
            end_time = time.time()
            measured_duration = end_time - start_time
            
            if abs(measured_duration - 0.1) > 0.01:
                self.log_warning(f"Time drift detected: expected 0.1s, got {measured_duration:.3f}s")
                
        except Exception as e:
            self.log_error(f"Time synchronization check failed: {e}")

    def check_process_memory_leaks(self):
        """Check for memory leaks in processes"""
        print("\n🧠 CHECKING MEMORY LEAKS...")
        
        try:
            current_process = psutil.Process()
            memory_info = current_process.memory_info()
            
            self.log_info(f"Current process RSS: {memory_info.rss / 1024 / 1024:.2f} MB")
            self.log_info(f"Current process VMS: {memory_info.vms / 1024 / 1024:.2f} MB")
            
            # Check for Python processes
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                try:
                    if 'python' in proc.info['name'].lower():
                        python_processes.append(proc)
                except:
                    pass
                    
            self.log_info(f"Found {len(python_processes)} Python processes")
            
            for proc in python_processes[:5]:  # Check top 5
                try:
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    if memory_mb > 500:  # More than 500MB
                        self.log_warning(f"High memory Python process PID {proc.info['pid']}: {memory_mb:.2f} MB")
                except:
                    pass
                    
        except Exception as e:
            self.log_error(f"Memory leak check failed: {e}")

    def check_file_handle_leaks(self):
        """Check for file handle leaks"""
        print("\n📁 CHECKING FILE HANDLE LEAKS...")
        
        try:
            current_process = psutil.Process()
            open_files = current_process.open_files()
            
            self.log_info(f"Current process has {len(open_files)} open files")
            
            if len(open_files) > 100:
                self.log_warning(f"High number of open files: {len(open_files)}")
                
            # Check for duplicate file handles
            file_paths = [f.path for f in open_files]
            unique_paths = set(file_paths)
            
            if len(file_paths) != len(unique_paths):
                self.log_warning(f"Duplicate file handles detected: {len(file_paths) - len(unique_paths)} duplicates")
                
        except Exception as e:
            self.log_error(f"File handle check failed: {e}")

    def check_thread_leaks(self):
        """Check for thread leaks"""
        print("\n🧵 CHECKING THREAD LEAKS...")
        
        try:
            active_threads = threading.active_count()
            self.log_info(f"Active threads: {active_threads}")
            
            if active_threads > 20:
                self.log_warning(f"High number of active threads: {active_threads}")
                
            # List thread names
            for thread in threading.enumerate():
                if thread.is_alive():
                    self.log_info(f"Thread: {thread.name} (daemon: {thread.daemon})")
                    
        except Exception as e:
            self.log_error(f"Thread leak check failed: {e}")

    def check_asyncio_tasks(self):
        """Check for asyncio task leaks"""
        print("\n🔄 CHECKING ASYNCIO TASKS...")
        
        try:
            # This needs to be run in an async context
            loop = None
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                self.log_info("No running asyncio loop")
                return
                
            if loop:
                tasks = asyncio.all_tasks(loop)
                self.log_info(f"Active asyncio tasks: {len(tasks)}")
                
                if len(tasks) > 50:
                    self.log_warning(f"High number of asyncio tasks: {len(tasks)}")
                    
                # Check for stuck tasks
                for task in list(tasks)[:10]:  # Check first 10
                    if task.done():
                        continue
                    self.log_info(f"Task: {task.get_name()} - {task}")
                    
        except Exception as e:
            self.log_error(f"Asyncio task check failed: {e}")

    def check_database_connections(self):
        """Check database connection issues"""
        print("\n🗄️  CHECKING DATABASE CONNECTIONS...")
        
        try:
            db_file = Path("bybit_bot/bybit_trading_bot.db")
            if db_file.exists():
                size_mb = db_file.stat().st_size / 1024 / 1024
                self.log_info(f"Database size: {size_mb:.2f} MB")
                
                if size_mb > 1000:  # More than 1GB
                    self.log_warning(f"Large database file: {size_mb:.2f} MB")
            else:
                self.log_warning("Database file not found")
                
        except Exception as e:
            self.log_error(f"Database check failed: {e}")

    def generate_cleanup_script(self):
        """Generate backup cleanup script"""
        print("\n🧹 GENERATING CLEANUP SCRIPT...")
        
        cleanup_script = """#!/usr/bin/env python3
# BACKUP CLEANUP SCRIPT - AUTO-GENERATED

import os
import glob
from pathlib import Path

def cleanup_backups():
    print("Starting backup cleanup...")
    
    # Patterns to clean
    patterns = [
        "bybit_bot/**/*.backup_*",
        "bybit_bot/**/*_backup_*"
    ]
    
    total_removed = 0
    for pattern in patterns:
        files = glob.glob(pattern, recursive=True)
        for file_path in files:
            try:
                os.remove(file_path)
                print(f"Removed: {file_path}")
                total_removed += 1
            except Exception as e:
                print(f"Error removing {file_path}: {e}")
                
    print(f"Cleanup complete. Removed {total_removed} backup files.")

if __name__ == "__main__":
    cleanup_backups()
"""
        
        with open("cleanup_backups.py", "w") as f:
            f.write(cleanup_script)
            
        self.log_info("Generated cleanup_backups.py script")

    def run_all_checks(self):
        """Run all deep process checks"""
        print("🚀 STARTING COMPREHENSIVE CHECKS...")
        
        self.check_backup_files_crisis()
        self.check_time_synchronization()
        self.check_process_memory_leaks()
        self.check_file_handle_leaks()
        self.check_thread_leaks()
        self.check_asyncio_tasks()
        self.check_database_connections()
        self.generate_cleanup_script()
        
        # Final report
        print("\n" + "=" * 80)
        print("📊 DEEP PROCESS CHECK SUMMARY")
        print("=" * 80)
        
        print(f"❌ ERRORS: {len(self.errors)}")
        for error in self.errors:
            print(f"  {error}")
            
        print(f"\n⚠️  WARNINGS: {len(self.warnings)}")
        for warning in self.warnings:
            print(f"  {warning}")
            
        print(f"\n🗂️  BACKUP FILES: {len(self.backup_files)}")
        
        if len(self.errors) == 0:
            print("\n✅ NO CRITICAL ERRORS FOUND")
        else:
            print(f"\n🚨 {len(self.errors)} CRITICAL ERRORS NEED ATTENTION")
            
        return len(self.errors) == 0

if __name__ == "__main__":
    checker = DeepProcessChecker()
    success = checker.run_all_checks()
    
    if not success:
        sys.exit(1)
    else:
        print("\n🎉 DEEP PROCESS CHECK COMPLETED SUCCESSFULLY")
