#!/usr/bin/env python3
"""
Edge Computing Engine for Ultra-Low Latency Trading
Implements GPU/FPGA acceleration for real-time feature engineering and signal generation

Features:
- GPU-accelerated feature computation
- FPGA simulation for ultra-low latency
- Edge node orchestration
- Real-time signal generation
- Distributed computing coordination
- Hardware-optimized algorithms
"""

import asyncio
import numpy as np
import cupy as cp  # GPU acceleration
import numba
from numba import cuda, jit
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
import time
import logging
from datetime import datetime
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import psutil
import GPUtil

logger = logging.getLogger(__name__)

@dataclass
class EdgeNodeConfig:
    """Configuration for edge computing node"""
    node_id: str
    node_type: str  # 'gpu', 'fpga', 'cpu'
    max_latency_us: int = 100  # Maximum acceptable latency in microseconds
    batch_size: int = 1000
    buffer_size: int = 10000
    num_workers: int = 4
    gpu_memory_limit: float = 0.8  # Use 80% of GPU memory
    enable_cuda: bool = True
    enable_optimization: bool = True

@dataclass
class ComputeTask:
    """Represents a computation task"""
    task_id: str
    task_type: str
    data: Any
    priority: int = 1  # Higher number = higher priority
    timestamp: float = 0.0
    deadline_us: int = 1000  # Deadline in microseconds
    callback: Optional[Callable] = None

class GPUAcceleratedCompute:
    """GPU-accelerated computation engine"""
    
    def __init__(self, config: EdgeNodeConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() and config.enable_cuda else "cpu")
        self.gpu_available = torch.cuda.is_available() and config.enable_cuda
        
        # Initialize GPU memory pool
        if self.gpu_available:
            self.gpu_memory_pool = cp.get_default_memory_pool()
            self.gpu_memory_pool.set_limit(size=int(self._get_gpu_memory() * config.gpu_memory_limit))
        
        # Pre-compiled CUDA kernels
        self._compile_cuda_kernels()
        
        # Performance tracking
        self.computation_times = deque(maxlen=1000)
        self.gpu_utilization = deque(maxlen=100)
        
        logger.info(f"GPU Compute Engine initialized on {self.device}")
    
    def _get_gpu_memory(self) -> int:
        """Get available GPU memory in bytes"""
        if self.gpu_available:
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    return int(gpus[0].memoryTotal * 1024 * 1024)  # Convert MB to bytes
            except:
                pass
        return 8 * 1024 * 1024 * 1024  # Default 8GB
    
    def _compile_cuda_kernels(self):
        """Pre-compile CUDA kernels for common operations"""
        if not self.gpu_available:
            return
        
        # Technical indicator kernels
        @cuda.jit
        def sma_kernel(prices, window, result):
            """Simple Moving Average CUDA kernel"""
            idx = cuda.grid(1)
            if idx < len(result):
                if idx >= window - 1:
                    sum_val = 0.0
                    for i in range(window):
                        sum_val += prices[idx - i]
                    result[idx] = sum_val / window
                else:
                    result[idx] = prices[idx]
        
        @cuda.jit
        def ema_kernel(prices, alpha, result):
            """Exponential Moving Average CUDA kernel"""
            idx = cuda.grid(1)
            if idx < len(result):
                if idx == 0:
                    result[idx] = prices[idx]
                else:
                    result[idx] = alpha * prices[idx] + (1 - alpha) * result[idx - 1]
        
        @cuda.jit
        def rsi_kernel(prices, window, result):
            """RSI CUDA kernel"""
            idx = cuda.grid(1)
            if idx < len(result) and idx >= window:
                gains = 0.0
                losses = 0.0
                for i in range(1, window + 1):
                    change = prices[idx - i + 1] - prices[idx - i]
                    if change > 0:
                        gains += change
                    else:
                        losses -= change
                
                if losses == 0:
                    result[idx] = 100.0
                else:
                    rs = gains / losses
                    result[idx] = 100.0 - (100.0 / (1.0 + rs))
        
        self.sma_kernel = sma_kernel
        self.ema_kernel = ema_kernel
        self.rsi_kernel = rsi_kernel
    
    async def compute_technical_indicators(self, prices: np.ndarray, indicators: List[str]) -> Dict[str, np.ndarray]:
        """Compute technical indicators using GPU acceleration"""
        start_time = time.time()
        
        if not self.gpu_available:
            return self._compute_indicators_cpu(prices, indicators)
        
        try:
            # Transfer data to GPU
            gpu_prices = cp.asarray(prices, dtype=cp.float32)
            results = {}
            
            # Configure CUDA grid
            threads_per_block = 256
            blocks_per_grid = (len(prices) + threads_per_block - 1) // threads_per_block
            
            for indicator in indicators:
                if indicator.startswith('sma_'):
                    window = int(indicator.split('_')[1])
                    gpu_result = cp.zeros_like(gpu_prices)
                    self.sma_kernel[blocks_per_grid, threads_per_block](gpu_prices, window, gpu_result)
                    results[indicator] = cp.asnumpy(gpu_result)
                
                elif indicator.startswith('ema_'):
                    period = int(indicator.split('_')[1])
                    alpha = 2.0 / (period + 1)
                    gpu_result = cp.zeros_like(gpu_prices)
                    self.ema_kernel[blocks_per_grid, threads_per_block](gpu_prices, alpha, gpu_result)
                    results[indicator] = cp.asnumpy(gpu_result)
                
                elif indicator.startswith('rsi_'):
                    window = int(indicator.split('_')[1])
                    gpu_result = cp.zeros_like(gpu_prices)
                    self.rsi_kernel[blocks_per_grid, threads_per_block](gpu_prices, window, gpu_result)
                    results[indicator] = cp.asnumpy(gpu_result)
            
            # Track performance
            computation_time = (time.time() - start_time) * 1000000  # microseconds
            self.computation_times.append(computation_time)
            
            return results
            
        except Exception as e:
            logger.warning(f"GPU computation failed, falling back to CPU: {e}")
            return self._compute_indicators_cpu(prices, indicators)
    
    def _compute_indicators_cpu(self, prices: np.ndarray, indicators: List[str]) -> Dict[str, np.ndarray]:
        """Fallback CPU computation for technical indicators"""
        results = {}
        
        for indicator in indicators:
            if indicator.startswith('sma_'):
                window = int(indicator.split('_')[1])
                results[indicator] = self._sma_cpu(prices, window)
            elif indicator.startswith('ema_'):
                period = int(indicator.split('_')[1])
                results[indicator] = self._ema_cpu(prices, period)
            elif indicator.startswith('rsi_'):
                window = int(indicator.split('_')[1])
                results[indicator] = self._rsi_cpu(prices, window)
        
        return results
    
    @staticmethod
    @jit(nopython=True)
    def _sma_cpu(prices, window):
        """Numba-optimized SMA calculation"""
        result = np.zeros_like(prices)
        for i in range(len(prices)):
            if i >= window - 1:
                result[i] = np.mean(prices[i-window+1:i+1])
            else:
                result[i] = prices[i]
        return result
    
    @staticmethod
    @jit(nopython=True)
    def _ema_cpu(prices, period):
        """Numba-optimized EMA calculation"""
        alpha = 2.0 / (period + 1)
        result = np.zeros_like(prices)
        result[0] = prices[0]
        for i in range(1, len(prices)):
            result[i] = alpha * prices[i] + (1 - alpha) * result[i-1]
        return result
    
    @staticmethod
    @jit(nopython=True)
    def _rsi_cpu(prices, window):
        """Numba-optimized RSI calculation"""
        result = np.zeros_like(prices)
        for i in range(window, len(prices)):
            gains = 0.0
            losses = 0.0
            for j in range(1, window + 1):
                change = prices[i - j + 1] - prices[i - j]
                if change > 0:
                    gains += change
                else:
                    losses -= change
            
            if losses == 0:
                result[i] = 100.0
            else:
                rs = gains / losses
                result[i] = 100.0 - (100.0 / (1.0 + rs))
        return result
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get GPU performance metrics"""
        metrics = {
            'avg_computation_time_us': np.mean(self.computation_times) if self.computation_times else 0,
            'p95_computation_time_us': np.percentile(self.computation_times, 95) if self.computation_times else 0,
            'gpu_available': self.gpu_available,
            'device': str(self.device)
        }
        
        if self.gpu_available:
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]
                    metrics.update({
                        'gpu_utilization': gpu.load * 100,
                        'gpu_memory_used': gpu.memoryUsed,
                        'gpu_memory_total': gpu.memoryTotal,
                        'gpu_temperature': gpu.temperature
                    })
            except:
                pass
        
        return metrics

class FPGASimulator:
    """FPGA simulation for ultra-low latency operations"""
    
    def __init__(self, config: EdgeNodeConfig):
        self.config = config
        self.pipeline_stages = 4  # Simulated FPGA pipeline depth
        self.clock_frequency = 200_000_000  # 200 MHz
        self.latency_ns = 1000_000_000 // self.clock_frequency  # 5ns per clock cycle
        
        # Simulated hardware resources
        self.lut_usage = 0.0
        self.bram_usage = 0.0
        self.dsp_usage = 0.0
        
        # Performance tracking
        self.operation_latencies = deque(maxlen=1000)
        
    async def process_market_tick(self, price: float, volume: float, timestamp: int) -> Dict[str, float]:
        """Simulate FPGA processing of market tick"""
        start_time = time.time_ns()
        
        # Simulate FPGA pipeline processing
        await asyncio.sleep(self.latency_ns * self.pipeline_stages / 1_000_000_000)
        
        # Simulated FPGA operations (bit-level optimized)
        price_int = int(price * 10000)  # Fixed-point arithmetic
        volume_int = int(volume * 1000)
        
        # Simple technical indicators in fixed-point
        sma_5 = price_int  # Simplified
        momentum = price_int - price_int  # Simplified
        volume_ratio = volume_int // 1000 if volume_int > 0 else 1
        
        # Convert back to floating point
        results = {
            'fpga_sma_5': sma_5 / 10000.0,
            'fpga_momentum': momentum / 10000.0,
            'fpga_volume_ratio': volume_ratio / 1000.0,
            'fpga_signal': 1.0 if price > 50000 else -1.0  # Simple signal
        }
        
        # Track latency
        latency_ns = time.time_ns() - start_time
        self.operation_latencies.append(latency_ns)
        
        return results
    
    def get_resource_utilization(self) -> Dict[str, float]:
        """Get simulated FPGA resource utilization"""
        return {
            'lut_usage_percent': self.lut_usage,
            'bram_usage_percent': self.bram_usage,
            'dsp_usage_percent': self.dsp_usage,
            'avg_latency_ns': np.mean(self.operation_latencies) if self.operation_latencies else 0,
            'max_latency_ns': np.max(self.operation_latencies) if self.operation_latencies else 0
        }

class EdgeComputeOrchestrator:
    """Orchestrates edge computing nodes for optimal performance"""
    
    def __init__(self, configs: List[EdgeNodeConfig]):
        self.configs = configs
        self.nodes = {}
        self.task_queue = asyncio.PriorityQueue()
        self.result_callbacks = {}
        
        # Initialize compute nodes
        for config in configs:
            if config.node_type == 'gpu':
                self.nodes[config.node_id] = GPUAcceleratedCompute(config)
            elif config.node_type == 'fpga':
                self.nodes[config.node_id] = FPGASimulator(config)
        
        # Performance tracking
        self.task_completion_times = deque(maxlen=1000)
        self.node_utilization = defaultdict(lambda: deque(maxlen=100))
        
        # Start task processing
        self.running = True
        self.worker_tasks = []
        
    async def start(self):
        """Start the edge computing orchestrator"""
        # Start worker tasks for each node
        for node_id in self.nodes:
            task = asyncio.create_task(self._process_tasks(node_id))
            self.worker_tasks.append(task)
        
        logger.info(f"Edge orchestrator started with {len(self.nodes)} nodes")
    
    async def stop(self):
        """Stop the edge computing orchestrator"""
        self.running = False
        for task in self.worker_tasks:
            task.cancel()
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
    
    async def submit_task(self, task: ComputeTask) -> Optional[Any]:
        """Submit a computation task"""
        task.timestamp = time.time()
        
        # Priority queue uses negative priority for max-heap behavior
        await self.task_queue.put((-task.priority, task.timestamp, task))
        
        # If callback is provided, store it
        if task.callback:
            self.result_callbacks[task.task_id] = task.callback
    
    async def _process_tasks(self, node_id: str):
        """Process tasks for a specific node"""
        node = self.nodes[node_id]
        
        while self.running:
            try:
                # Get task from queue with timeout
                priority, timestamp, task = await asyncio.wait_for(
                    self.task_queue.get(), timeout=0.1
                )
                
                # Check if task has exceeded deadline
                current_time = time.time()
                elapsed_us = (current_time - task.timestamp) * 1_000_000
                
                if elapsed_us > task.deadline_us:
                    logger.warning(f"Task {task.task_id} exceeded deadline: {elapsed_us}us > {task.deadline_us}us")
                    continue
                
                # Process task based on type
                start_time = time.time()
                result = await self._execute_task(node, task)
                execution_time = (time.time() - start_time) * 1_000_000  # microseconds
                
                # Track performance
                self.task_completion_times.append(execution_time)
                self.node_utilization[node_id].append(execution_time)
                
                # Execute callback if provided
                if task.task_id in self.result_callbacks:
                    callback = self.result_callbacks.pop(task.task_id)
                    await callback(result)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing task on node {node_id}: {e}")
    
    async def _execute_task(self, node: Any, task: ComputeTask) -> Any:
        """Execute a task on a specific node"""
        if task.task_type == 'technical_indicators':
            if hasattr(node, 'compute_technical_indicators'):
                return await node.compute_technical_indicators(
                    task.data['prices'], 
                    task.data['indicators']
                )
        elif task.task_type == 'market_tick':
            if hasattr(node, 'process_market_tick'):
                return await node.process_market_tick(
                    task.data['price'],
                    task.data['volume'],
                    task.data['timestamp']
                )
        
        return None
    
    async def process_market_data_batch(self, market_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of market data across all nodes"""
        tasks = []
        results = []
        
        for i, data in enumerate(market_data):
            # Create tasks for different node types
            if 'gpu' in [config.node_type for config in self.configs]:
                gpu_task = ComputeTask(
                    task_id=f"gpu_batch_{i}",
                    task_type='technical_indicators',
                    data={
                        'prices': data.get('prices', np.array([])),
                        'indicators': data.get('indicators', [])
                    },
                    priority=2,
                    deadline_us=1000
                )
                tasks.append(self.submit_task(gpu_task))
            
            if 'fpga' in [config.node_type for config in self.configs]:
                fpga_task = ComputeTask(
                    task_id=f"fpga_tick_{i}",
                    task_type='market_tick',
                    data={
                        'price': data.get('price', 0.0),
                        'volume': data.get('volume', 0.0),
                        'timestamp': data.get('timestamp', 0)
                    },
                    priority=3,
                    deadline_us=100
                )
                tasks.append(self.submit_task(fpga_task))
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks)
        
        return results
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system performance metrics"""
        metrics = {
            'num_nodes': len(self.nodes),
            'avg_task_completion_us': np.mean(self.task_completion_times) if self.task_completion_times else 0,
            'p95_task_completion_us': np.percentile(self.task_completion_times, 95) if self.task_completion_times else 0,
            'p99_task_completion_us': np.percentile(self.task_completion_times, 99) if self.task_completion_times else 0,
            'node_metrics': {}
        }
        
        # Get metrics from each node
        for node_id, node in self.nodes.items():
            if hasattr(node, 'get_performance_metrics'):
                metrics['node_metrics'][node_id] = node.get_performance_metrics()
            elif hasattr(node, 'get_resource_utilization'):
                metrics['node_metrics'][node_id] = node.get_resource_utilization()
        
        return metrics

# Example usage
async def main():
    """Example usage of edge computing system"""
    
    # Configure edge nodes
    configs = [
        EdgeNodeConfig(
            node_id='gpu_node_1',
            node_type='gpu',
            max_latency_us=1000,
            enable_cuda=True
        ),
        EdgeNodeConfig(
            node_id='fpga_node_1',
            node_type='fpga',
            max_latency_us=100
        )
    ]
    
    # Initialize orchestrator
    orchestrator = EdgeComputeOrchestrator(configs)
    await orchestrator.start()
    
    try:
        # Simulate market data processing
        for i in range(100):
            market_data = [{
                'prices': np.random.randn(1000) + 50000,
                'indicators': ['sma_5', 'sma_20', 'ema_12', 'rsi_14'],
                'price': 50000 + np.random.randn() * 100,
                'volume': np.random.exponential(0.1),
                'timestamp': int(time.time() * 1_000_000)
            }]
            
            await orchestrator.process_market_data_batch(market_data)
            
            if i % 10 == 0:
                metrics = orchestrator.get_system_metrics()
                print(f"Batch {i}: Avg latency = {metrics['avg_task_completion_us']:.2f}us")
        
        # Final metrics
        final_metrics = orchestrator.get_system_metrics()
        print(f"Final Performance Metrics:")
        for key, value in final_metrics.items():
            if key != 'node_metrics':
                print(f"  {key}: {value}")
    
    finally:
        await orchestrator.stop()

if __name__ == "__main__":
    asyncio.run(main())
