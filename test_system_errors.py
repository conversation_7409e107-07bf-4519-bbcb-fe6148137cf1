#!/usr/bin/env python3
"""
Test script to identify remaining system errors
"""
import sys
import os
import asyncio
import traceback
from datetime import datetime

def main():
    print(f"SYSTEM ERROR DETECTION TEST - {datetime.now()}")
    print("=" * 60)
    
    # Set up environment
    sys.path.append('.')
    
    try:
        # Test 1: Basic imports
        print("TEST 1: Basic imports...")
        from bybit_bot.main import BybitTradingBotSystem
        print("✓ Main system imports OK")
        
        # Test 2: Bot creation
        print("TEST 2: Bot creation...")
        bot = BybitTradingBotSystem()
        print("✓ Bot creation OK")
        
        # Test 3: Database connection
        print("TEST 3: Database connection...")
        if hasattr(bot, 'database_manager') and bot.database_manager:
            print("✓ Database manager exists")
        else:
            print("✗ Database manager missing")
        
        # Test 4: Configuration
        print("TEST 4: Configuration...")
        if hasattr(bot, 'config') and bot.config:
            print("✓ Configuration exists")
        else:
            print("✗ Configuration missing")
        
        # Test 5: Async initialization test
        print("TEST 5: Async initialization test...")
        
        async def test_async():
            try:
                print("  Starting async test...")
                result = await bot.initialize_all_systems()
                print(f"  Initialization result: {result}")
                return True
            except Exception as async_error:
                print(f"  Async error: {async_error}")
                traceback.print_exc()
                return False
        
        # Run async test
        async_result = asyncio.run(test_async())
        if async_result:
            print("✓ Async test passed")
        else:
            print("✗ Async test failed")
        
        print("=" * 60)
        print("ERROR DETECTION TEST COMPLETED")
        return True
        
    except Exception as e:
        print(f"CRITICAL ERROR DETECTED: {e}")
        print("FULL TRACEBACK:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
