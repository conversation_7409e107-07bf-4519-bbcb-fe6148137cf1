#!/usr/bin/env python3
"""
Test main.py execution and capture all output
"""

import sys
import os
import traceback
import subprocess
import time

print("🧪 TESTING MAIN.PY EXECUTION")
print("=" * 60)

def test_import_only():
    """Test just importing main.py"""
    print("\n📦 TESTING IMPORT ONLY...")
    
    try:
        # Change to correct directory
        original_dir = os.getcwd()
        os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
        
        # Add to path
        sys.path.insert(0, '.')
        
        # Try import
        print("Importing BybitTradingBotSystem...")
        from bybit_bot.main import BybitTradingBotSystem
        print("✅ Import successful")
        
        # Try instance creation
        print("Creating instance...")
        system = BybitTradingBotSystem()
        print("✅ Instance creation successful")
        
        # Test basic attributes
        print(f"is_running: {system.is_running}")
        print(f"total_profit: {system.total_profit}")
        print(f"start_time: {system.start_time}")
        
        # Restore directory
        os.chdir(original_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        traceback.print_exc()
        return False

def test_main_execution():
    """Test running main.py as a script"""
    print("\n🚀 TESTING MAIN.PY EXECUTION...")
    
    try:
        # Change to correct directory
        os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
        
        # Run main.py with timeout
        print("Starting main.py with 30 second timeout...")
        
        process = subprocess.Popen(
            [sys.executable, 'bybit_bot/main.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd='.'
        )
        
        # Wait for 30 seconds or until completion
        try:
            stdout, stderr = process.communicate(timeout=30)
            
            print(f"Process completed with return code: {process.returncode}")
            
            if stdout:
                print("\n📤 STDOUT:")
                print(stdout)
                
            if stderr:
                print("\n📥 STDERR:")
                print(stderr)
                
            return process.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("⏰ Process timeout after 30 seconds")
            process.terminate()
            
            # Get partial output
            try:
                stdout, stderr = process.communicate(timeout=5)
                
                if stdout:
                    print("\n📤 PARTIAL STDOUT:")
                    print(stdout)
                    
                if stderr:
                    print("\n📥 PARTIAL STDERR:")
                    print(stderr)
                    
            except subprocess.TimeoutExpired:
                process.kill()
                print("🔪 Process killed")
                
            return True  # Timeout is acceptable for this test
            
    except Exception as e:
        print(f"❌ Execution test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 STARTING COMPREHENSIVE MAIN.PY TESTS")
    
    # Test 1: Import only
    import_success = test_import_only()
    
    # Test 2: Full execution
    execution_success = test_main_execution()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    print(f"📦 Import test: {'✅ PASSED' if import_success else '❌ FAILED'}")
    print(f"🚀 Execution test: {'✅ PASSED' if execution_success else '❌ FAILED'}")
    
    if import_success and execution_success:
        print("\n🎉 ALL TESTS PASSED")
        print("✅ Main.py is working correctly")
        return True
    elif import_success:
        print("\n⚠️  PARTIAL SUCCESS")
        print("✅ Main.py imports and creates instances correctly")
        print("⚠️  Execution may have issues (check output above)")
        return True
    else:
        print("\n❌ TESTS FAILED")
        print("🚨 Main.py has import or initialization issues")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ MAIN.PY TEST SUCCESSFUL")
        sys.exit(0)
    else:
        print("\n❌ MAIN.PY TEST FAILED")
        sys.exit(1)
