#!/usr/bin/env python3
"""
Tier 1 Experience Replay Buffer
CPU-efficient experience replay for learning from historical successful trades
"""

import numpy as np
import asyncio
import logging
import random
import sqlite3
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from collections import deque
import heapq

logger = logging.getLogger(__name__)

@dataclass
class TradeExperience:
    """Single trade experience for replay"""
    trade_id: str
    symbol: str
    side: str
    entry_price: float
    exit_price: float
    quantity: float
    profit_loss: float
    profit_pct: float
    timestamp: datetime
    market_conditions: Dict[str, Any]
    strategy_parameters: Dict[str, float]
    environmental_context: Dict[str, Any]
    success_score: float  # 0-1 score based on profitability
    priority: float  # Priority for sampling

@dataclass
class ReplayBatch:
    """Batch of experiences for learning"""
    experiences: List[TradeExperience]
    batch_size: int
    avg_success_score: float
    total_profit: float

class Tier1ExperienceReplayBuffer:
    """
    CPU-efficient experience replay buffer with prioritized sampling
    Stores and replays successful trading experiences for continuous learning
    """
    
    def __init__(self, config: Any, db_path: str = "bybit_trading_bot.db", max_size: int = 10000):
        self.config = config
        self.db_path = db_path
        self.max_size = max_size
        
        # Memory buffers
        self.experiences: deque = deque(maxlen=max_size)
        self.priority_heap: List[Tuple[float, str]] = []  # (priority, trade_id)
        self.experience_index: Dict[str, TradeExperience] = {}
        
        # Sampling parameters
        self.alpha = 0.6  # Prioritization exponent
        self.beta = 0.4   # Importance sampling exponent
        self.epsilon = 1e-6  # Small constant to avoid zero priorities
        
        # Performance tracking
        self.total_experiences = 0
        self.successful_experiences = 0
        self.replay_count = 0
        self.learning_improvement = 0.0
        
        # Buffer statistics
        self.buffer_stats = {
            'profitable_trades': 0,
            'losing_trades': 0,
            'avg_profit_pct': 0.0,
            'best_trade_profit': 0.0,
            'worst_trade_loss': 0.0,
            'symbol_distribution': {},
            'strategy_distribution': {}
        }
        
        logger.info("Tier 1 Experience Replay Buffer initialized")
    
    async def initialize(self):
        """Initialize the experience replay buffer"""
        try:
            # Create database table
            await self._create_tables()
            
            # Load existing experiences
            await self._load_experiences()
            
            # Update buffer statistics
            await self._update_buffer_stats()
            
            logger.info(f"Experience replay buffer initialized with {len(self.experiences)} experiences")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize experience replay buffer: {e}")
            return False
    
    async def _create_tables(self):
        """Create database tables for experience storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trade_experiences (
                    trade_id TEXT PRIMARY KEY,
                    symbol TEXT,
                    side TEXT,
                    entry_price REAL,
                    exit_price REAL,
                    quantity REAL,
                    profit_loss REAL,
                    profit_pct REAL,
                    timestamp TIMESTAMP,
                    market_conditions TEXT,
                    strategy_parameters TEXT,
                    environmental_context TEXT,
                    success_score REAL,
                    priority REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Index for efficient querying
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_success_score ON trade_experiences(success_score)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_symbol ON trade_experiences(symbol)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON trade_experiences(timestamp)")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to create experience tables: {e}")
            raise
    
    async def _load_experiences(self):
        """Load experiences from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Load most recent experiences up to max_size
            cursor.execute("""
                SELECT * FROM trade_experiences 
                ORDER BY timestamp DESC 
                LIMIT ?
            """, (self.max_size,))
            
            rows = cursor.fetchall()
            
            for row in rows:
                experience = TradeExperience(
                    trade_id=row[0],
                    symbol=row[1],
                    side=row[2],
                    entry_price=row[3],
                    exit_price=row[4],
                    quantity=row[5],
                    profit_loss=row[6],
                    profit_pct=row[7],
                    timestamp=datetime.fromisoformat(row[8]),
                    market_conditions=json.loads(row[9]),
                    strategy_parameters=json.loads(row[10]),
                    environmental_context=json.loads(row[11]),
                    success_score=row[12],
                    priority=row[13]
                )
                
                self.experiences.append(experience)
                self.experience_index[experience.trade_id] = experience
                heapq.heappush(self.priority_heap, (-experience.priority, experience.trade_id))
            
            conn.close()
            logger.info(f"Loaded {len(self.experiences)} experiences from database")
            
        except Exception as e:
            logger.error(f"Failed to load experiences: {e}")
    
    async def add_experience(self, trade_experience: TradeExperience):
        """Add new trade experience to buffer"""
        try:
            # Calculate success score and priority
            trade_experience.success_score = self._calculate_success_score(trade_experience)
            trade_experience.priority = self._calculate_priority(trade_experience)
            
            # Add to memory buffers
            self.experiences.append(trade_experience)
            self.experience_index[trade_experience.trade_id] = trade_experience
            heapq.heappush(self.priority_heap, (-trade_experience.priority, trade_experience.trade_id))
            
            # Save to database
            await self._save_experience(trade_experience)
            
            # Update statistics
            self.total_experiences += 1
            if trade_experience.profit_loss > 0:
                self.successful_experiences += 1
            
            await self._update_buffer_stats()
            
            logger.debug(f"Added experience: {trade_experience.symbol} P&L={trade_experience.profit_loss:.4f} Score={trade_experience.success_score:.3f}")
            
        except Exception as e:
            logger.error(f"Failed to add experience: {e}")
    
    def _calculate_success_score(self, experience: TradeExperience) -> float:
        """Calculate success score (0-1) based on trade profitability and conditions"""
        # Base score from profit percentage
        profit_score = np.tanh(experience.profit_pct * 10)  # Normalize to 0-1
        
        # Adjust for market conditions (volatility, volume, etc.)
        market_conditions = experience.market_conditions
        volatility = market_conditions.get('volatility', 0.02)
        volume_ratio = market_conditions.get('volume_ratio', 1.0)
        
        # Higher scores for profits in difficult conditions
        difficulty_multiplier = 1.0 + (volatility * 2) + (1.0 / max(volume_ratio, 0.1))
        
        if experience.profit_loss > 0:
            score = min(1.0, profit_score * difficulty_multiplier)
        else:
            score = max(0.0, 0.5 + profit_score * 0.5)  # Losing trades get lower scores
        
        return score
    
    def _calculate_priority(self, experience: TradeExperience) -> float:
        """Calculate sampling priority based on success score and recency"""
        # Higher priority for more successful and recent trades
        recency_weight = 1.0  # Recent trades get slight boost
        success_weight = experience.success_score ** self.alpha
        
        priority = success_weight * recency_weight + self.epsilon
        return priority
    
    async def _save_experience(self, experience: TradeExperience):
        """Save experience to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO trade_experiences 
                (trade_id, symbol, side, entry_price, exit_price, quantity, profit_loss, 
                 profit_pct, timestamp, market_conditions, strategy_parameters, 
                 environmental_context, success_score, priority)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                experience.trade_id,
                experience.symbol,
                experience.side,
                experience.entry_price,
                experience.exit_price,
                experience.quantity,
                experience.profit_loss,
                experience.profit_pct,
                experience.timestamp.isoformat(),
                json.dumps(experience.market_conditions),
                json.dumps(experience.strategy_parameters),
                json.dumps(experience.environmental_context),
                experience.success_score,
                experience.priority
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to save experience: {e}")
    
    async def sample_batch(self, batch_size: int = 32, symbol: Optional[str] = None) -> ReplayBatch:
        """Sample a batch of experiences for learning"""
        try:
            if len(self.experiences) < batch_size:
                # Return all experiences if buffer is small
                experiences = list(self.experiences)
            else:
                # Prioritized sampling
                experiences = self._prioritized_sample(batch_size, symbol)
            
            # Filter by symbol if specified
            if symbol:
                experiences = [exp for exp in experiences if exp.symbol == symbol]
            
            # Create replay batch
            batch = ReplayBatch(
                experiences=experiences,
                batch_size=len(experiences),
                avg_success_score=np.mean([exp.success_score for exp in experiences]) if experiences else 0.0,
                total_profit=sum([exp.profit_loss for exp in experiences])
            )
            
            self.replay_count += 1
            logger.debug(f"Sampled batch: size={batch.batch_size}, avg_score={batch.avg_success_score:.3f}")
            
            return batch
            
        except Exception as e:
            logger.error(f"Failed to sample batch: {e}")
            return ReplayBatch(experiences=[], batch_size=0, avg_success_score=0.0, total_profit=0.0)
    
    def _prioritized_sample(self, batch_size: int, symbol: Optional[str] = None) -> List[TradeExperience]:
        """Sample experiences based on priority"""
        sampled_experiences = []
        
        # Get all priorities
        priorities = [exp.priority for exp in self.experiences]
        total_priority = sum(priorities)
        
        if total_priority == 0:
            # Uniform sampling if no priorities
            sampled_experiences = random.sample(list(self.experiences), min(batch_size, len(self.experiences)))
        else:
            # Weighted sampling based on priorities
            probabilities = [p / total_priority for p in priorities]
            
            # Sample with replacement
            indices = np.random.choice(
                len(self.experiences), 
                size=min(batch_size, len(self.experiences)), 
                p=probabilities, 
                replace=True
            )
            
            sampled_experiences = [self.experiences[i] for i in indices]
        
        return sampled_experiences
    
    async def get_best_experiences(self, count: int = 10, symbol: Optional[str] = None) -> List[TradeExperience]:
        """Get the best experiences by success score"""
        experiences = list(self.experiences)
        
        if symbol:
            experiences = [exp for exp in experiences if exp.symbol == symbol]
        
        # Sort by success score (descending)
        experiences.sort(key=lambda x: x.success_score, reverse=True)
        
        return experiences[:count]
    
    async def get_worst_experiences(self, count: int = 10, symbol: Optional[str] = None) -> List[TradeExperience]:
        """Get the worst experiences for learning from mistakes"""
        experiences = list(self.experiences)
        
        if symbol:
            experiences = [exp for exp in experiences if exp.symbol == symbol]
        
        # Sort by success score (ascending)
        experiences.sort(key=lambda x: x.success_score)
        
        return experiences[:count]
    
    async def _update_buffer_stats(self):
        """Update buffer statistics"""
        if not self.experiences:
            return
        
        profitable_trades = [exp for exp in self.experiences if exp.profit_loss > 0]
        losing_trades = [exp for exp in self.experiences if exp.profit_loss <= 0]
        
        self.buffer_stats.update({
            'profitable_trades': len(profitable_trades),
            'losing_trades': len(losing_trades),
            'avg_profit_pct': np.mean([exp.profit_pct for exp in self.experiences]),
            'best_trade_profit': max([exp.profit_loss for exp in self.experiences]),
            'worst_trade_loss': min([exp.profit_loss for exp in self.experiences]),
            'symbol_distribution': self._get_symbol_distribution(),
            'strategy_distribution': self._get_strategy_distribution()
        })
    
    def _get_symbol_distribution(self) -> Dict[str, int]:
        """Get distribution of symbols in buffer"""
        distribution = {}
        for exp in self.experiences:
            distribution[exp.symbol] = distribution.get(exp.symbol, 0) + 1
        return distribution
    
    def _get_strategy_distribution(self) -> Dict[str, int]:
        """Get distribution of strategies in buffer"""
        distribution = {}
        for exp in self.experiences:
            strategy = exp.strategy_parameters.get('strategy_name', 'unknown')
            distribution[strategy] = distribution.get(strategy, 0) + 1
        return distribution
    
    async def get_buffer_stats(self) -> Dict[str, Any]:
        """Get comprehensive buffer statistics"""
        return {
            'total_experiences': len(self.experiences),
            'successful_experiences': self.successful_experiences,
            'success_rate': self.successful_experiences / max(self.total_experiences, 1),
            'replay_count': self.replay_count,
            'buffer_utilization': len(self.experiences) / self.max_size,
            **self.buffer_stats
        }
    
    async def clear_old_experiences(self, days: int = 30):
        """Clear experiences older than specified days"""
        try:
            cutoff_date = datetime.now(timezone.utc).timestamp() - (days * 24 * 3600)
            
            # Remove from memory
            self.experiences = deque([
                exp for exp in self.experiences 
                if exp.timestamp.timestamp() > cutoff_date
            ], maxlen=self.max_size)
            
            # Update index
            self.experience_index = {
                exp.trade_id: exp for exp in self.experiences
            }
            
            # Rebuild priority heap
            self.priority_heap = [
                (-exp.priority, exp.trade_id) for exp in self.experiences
            ]
            heapq.heapify(self.priority_heap)
            
            # Remove from database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                "DELETE FROM trade_experiences WHERE timestamp < ?",
                (datetime.fromtimestamp(cutoff_date).isoformat(),)
            )
            conn.commit()
            conn.close()
            
            logger.info(f"Cleared experiences older than {days} days")
            
        except Exception as e:
            logger.error(f"Failed to clear old experiences: {e}")
