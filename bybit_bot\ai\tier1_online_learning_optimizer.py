#!/usr/bin/env python3
"""
Tier 1 Online Learning Optimizer
CPU-efficient online learning with AdamW optimizer for real-time trading adaptation
"""

import numpy as np
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timezone
import json
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class OnlineLearningState:
    """State for online learning parameters"""
    parameters: Dict[str, float]
    gradients: Dict[str, float]
    momentum: Dict[str, float]
    velocity: Dict[str, float]
    learning_rate: float
    beta1: float = 0.9
    beta2: float = 0.999
    epsilon: float = 1e-8
    weight_decay: float = 0.01
    step_count: int = 0

@dataclass
class TradeOutcome:
    """Trade outcome for learning"""
    symbol: str
    side: str
    entry_price: float
    exit_price: float
    quantity: float
    profit_loss: float
    timestamp: datetime
    market_conditions: Dict[str, Any]
    strategy_parameters: Dict[str, float]

class Tier1OnlineLearningOptimizer:
    """
    CPU-efficient online learning optimizer using AdamW
    Implements real-time parameter adaptation with minimal computational overhead
    """
    
    def __init__(self, config: Any, db_path: str = "bybit_trading_bot.db"):
        self.config = config
        self.db_path = db_path
        
        # Online learning parameters
        self.learning_states: Dict[str, OnlineLearningState] = {}
        self.parameter_bounds: Dict[str, Tuple[float, float]] = {
            'position_size_multiplier': (0.1, 3.0),
            'stop_loss_pct': (0.005, 0.05),
            'take_profit_pct': (0.01, 0.1),
            'confidence_threshold': (0.1, 0.9),
            'risk_tolerance': (0.1, 1.0),
            'momentum_weight': (0.0, 1.0),
            'volatility_adjustment': (0.5, 2.0),
            'trend_following_strength': (0.0, 1.0)
        }
        
        # Initialize default parameters
        self.default_parameters = {
            'position_size_multiplier': 1.0,
            'stop_loss_pct': 0.02,
            'take_profit_pct': 0.04,
            'confidence_threshold': 0.6,
            'risk_tolerance': 0.5,
            'momentum_weight': 0.3,
            'volatility_adjustment': 1.0,
            'trend_following_strength': 0.5
        }
        
        # Performance tracking
        self.performance_history: List[Dict[str, Any]] = []
        self.adaptation_count = 0
        self.total_profit_improvement = 0.0
        
        logger.info("Tier 1 Online Learning Optimizer initialized")
    
    async def initialize(self):
        """Initialize the online learning optimizer"""
        try:
            # Create database table for storing learning states
            await self._create_tables()
            
            # Load existing learning states
            await self._load_learning_states()
            
            # Initialize learning states for all symbols if not exists
            await self._initialize_default_states()
            
            logger.info("Online learning optimizer initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize online learning optimizer: {e}")
            return False
    
    async def _create_tables(self):
        """Create database tables for online learning"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Learning states table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS online_learning_states (
                    symbol TEXT PRIMARY KEY,
                    parameters TEXT,
                    gradients TEXT,
                    momentum TEXT,
                    velocity TEXT,
                    learning_rate REAL,
                    beta1 REAL,
                    beta2 REAL,
                    epsilon REAL,
                    weight_decay REAL,
                    step_count INTEGER,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Performance tracking table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS online_learning_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT,
                    profit_loss REAL,
                    parameter_update TEXT,
                    improvement_pct REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to create online learning tables: {e}")
            raise
    
    async def _load_learning_states(self):
        """Load existing learning states from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM online_learning_states")
            rows = cursor.fetchall()
            
            for row in rows:
                symbol = row[0]
                state = OnlineLearningState(
                    parameters=json.loads(row[1]),
                    gradients=json.loads(row[2]),
                    momentum=json.loads(row[3]),
                    velocity=json.loads(row[4]),
                    learning_rate=row[5],
                    beta1=row[6],
                    beta2=row[7],
                    epsilon=row[8],
                    weight_decay=row[9],
                    step_count=row[10]
                )
                self.learning_states[symbol] = state
            
            conn.close()
            logger.info(f"Loaded {len(self.learning_states)} learning states")
            
        except Exception as e:
            logger.error(f"Failed to load learning states: {e}")
    
    async def _initialize_default_states(self):
        """Initialize default learning states for common symbols"""
        default_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
        
        for symbol in default_symbols:
            if symbol not in self.learning_states:
                state = OnlineLearningState(
                    parameters=self.default_parameters.copy(),
                    gradients={k: 0.0 for k in self.default_parameters.keys()},
                    momentum={k: 0.0 for k in self.default_parameters.keys()},
                    velocity={k: 0.0 for k in self.default_parameters.keys()},
                    learning_rate=0.001,  # Conservative learning rate
                    step_count=0
                )
                self.learning_states[symbol] = state
                await self._save_learning_state(symbol, state)
    
    async def _save_learning_state(self, symbol: str, state: OnlineLearningState):
        """Save learning state to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO online_learning_states 
                (symbol, parameters, gradients, momentum, velocity, learning_rate, 
                 beta1, beta2, epsilon, weight_decay, step_count)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                symbol,
                json.dumps(state.parameters),
                json.dumps(state.gradients),
                json.dumps(state.momentum),
                json.dumps(state.velocity),
                state.learning_rate,
                state.beta1,
                state.beta2,
                state.epsilon,
                state.weight_decay,
                state.step_count
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to save learning state for {symbol}: {e}")
    
    async def learn_from_trade_outcome(self, trade_outcome: TradeOutcome) -> Dict[str, float]:
        """
        Learn from trade outcome using AdamW optimizer
        Returns updated parameters for the symbol
        """
        try:
            symbol = trade_outcome.symbol
            
            # Get or create learning state for symbol
            if symbol not in self.learning_states:
                await self._initialize_default_states()
            
            state = self.learning_states[symbol]
            
            # Calculate reward signal (normalized profit/loss)
            reward = self._calculate_reward_signal(trade_outcome)
            
            # Calculate gradients based on reward and current parameters
            gradients = self._calculate_gradients(trade_outcome, reward, state)
            
            # Update parameters using AdamW optimizer
            updated_parameters = self._adamw_update(state, gradients)
            
            # Apply parameter bounds
            updated_parameters = self._apply_parameter_bounds(updated_parameters)
            
            # Update learning state
            state.parameters = updated_parameters
            state.step_count += 1
            
            # Save updated state
            await self._save_learning_state(symbol, state)
            
            # Track performance improvement
            improvement_pct = self._calculate_improvement(trade_outcome, reward)
            await self._track_performance(symbol, trade_outcome.profit_loss, updated_parameters, improvement_pct)
            
            self.adaptation_count += 1
            self.total_profit_improvement += improvement_pct
            
            logger.info(f"Online learning update for {symbol}: reward={reward:.4f}, improvement={improvement_pct:.2f}%")
            
            return updated_parameters
            
        except Exception as e:
            logger.error(f"Failed to learn from trade outcome: {e}")
            return self.default_parameters.copy()
    
    def _calculate_reward_signal(self, trade_outcome: TradeOutcome) -> float:
        """Calculate normalized reward signal from trade outcome"""
        # Normalize profit/loss by position size and market volatility
        position_value = trade_outcome.quantity * trade_outcome.entry_price
        normalized_pnl = trade_outcome.profit_loss / position_value
        
        # Apply exponential scaling for stronger learning signal
        if normalized_pnl > 0:
            reward = np.tanh(normalized_pnl * 10)  # Positive reward (0 to 1)
        else:
            reward = -np.tanh(abs(normalized_pnl) * 10)  # Negative reward (-1 to 0)
        
        return reward
    
    def _calculate_gradients(self, trade_outcome: TradeOutcome, reward: float, state: OnlineLearningState) -> Dict[str, float]:
        """Calculate gradients for parameter updates"""
        gradients = {}
        
        # Calculate gradients based on reward and parameter sensitivity
        for param_name, param_value in state.parameters.items():
            if param_name == 'position_size_multiplier':
                # Larger positions should be rewarded more for profits, penalized more for losses
                gradients[param_name] = reward * 0.1
            elif param_name == 'stop_loss_pct':
                # Tighter stop losses should be rewarded for preventing large losses
                gradients[param_name] = -reward * 0.05 if reward < 0 else reward * 0.02
            elif param_name == 'take_profit_pct':
                # Appropriate take profit levels should be rewarded
                gradients[param_name] = reward * 0.05
            elif param_name == 'confidence_threshold':
                # Higher confidence should be rewarded for profitable trades
                gradients[param_name] = reward * 0.03
            elif param_name == 'risk_tolerance':
                # Risk tolerance adjustment based on outcome
                gradients[param_name] = reward * 0.02
            else:
                # Default gradient calculation
                gradients[param_name] = reward * 0.01
        
        return gradients
    
    def _adamw_update(self, state: OnlineLearningState, gradients: Dict[str, float]) -> Dict[str, float]:
        """Apply AdamW optimizer update"""
        updated_parameters = {}
        
        for param_name in state.parameters.keys():
            # Get current values
            param = state.parameters[param_name]
            grad = gradients.get(param_name, 0.0)
            m = state.momentum.get(param_name, 0.0)
            v = state.velocity.get(param_name, 0.0)
            
            # Update biased first moment estimate
            m = state.beta1 * m + (1 - state.beta1) * grad
            
            # Update biased second raw moment estimate
            v = state.beta2 * v + (1 - state.beta2) * (grad ** 2)
            
            # Compute bias-corrected first moment estimate
            m_hat = m / (1 - state.beta1 ** (state.step_count + 1))
            
            # Compute bias-corrected second raw moment estimate
            v_hat = v / (1 - state.beta2 ** (state.step_count + 1))
            
            # Update parameter with weight decay (AdamW)
            param_update = state.learning_rate * (m_hat / (np.sqrt(v_hat) + state.epsilon) + state.weight_decay * param)
            updated_param = param + param_update
            
            # Store updated values
            updated_parameters[param_name] = updated_param
            state.momentum[param_name] = m
            state.velocity[param_name] = v
        
        return updated_parameters
    
    def _apply_parameter_bounds(self, parameters: Dict[str, float]) -> Dict[str, float]:
        """Apply bounds to parameters to keep them in valid ranges"""
        bounded_parameters = {}
        
        for param_name, param_value in parameters.items():
            if param_name in self.parameter_bounds:
                min_val, max_val = self.parameter_bounds[param_name]
                bounded_parameters[param_name] = np.clip(param_value, min_val, max_val)
            else:
                bounded_parameters[param_name] = param_value
        
        return bounded_parameters
    
    def _calculate_improvement(self, trade_outcome: TradeOutcome, reward: float) -> float:
        """Calculate performance improvement percentage"""
        # Simple improvement metric based on reward magnitude
        return abs(reward) * 100
    
    async def _track_performance(self, symbol: str, profit_loss: float, parameters: Dict[str, float], improvement_pct: float):
        """Track performance improvement in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO online_learning_performance 
                (symbol, profit_loss, parameter_update, improvement_pct)
                VALUES (?, ?, ?, ?)
            """, (
                symbol,
                profit_loss,
                json.dumps(parameters),
                improvement_pct
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to track performance: {e}")
    
    async def get_optimized_parameters(self, symbol: str) -> Dict[str, float]:
        """Get current optimized parameters for a symbol"""
        if symbol in self.learning_states:
            return self.learning_states[symbol].parameters.copy()
        else:
            return self.default_parameters.copy()
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            'adaptation_count': self.adaptation_count,
            'total_profit_improvement': self.total_profit_improvement,
            'average_improvement': self.total_profit_improvement / max(self.adaptation_count, 1),
            'symbols_optimized': len(self.learning_states),
            'learning_rate_avg': np.mean([state.learning_rate for state in self.learning_states.values()]) if self.learning_states else 0.0
        }
