"""
Risk Agent - ENHANCED Specialized agent for risk management and monitoring
Handles real-time risk assessment, position monitoring, and emergency protocols

ENHANCED FEATURES:
1. Integration with main risk management systems
2. CPU-efficient risk calculation algorithms
3. Real-time portfolio monitoring
4. Optimized memory usage in risk metrics storage
5. Parallel risk assessment
6. Intelligent risk threshold adaptation
7. Optimized correlation analysis algorithms
8. Predictive risk modeling
9. Dynamic position sizing recommendations
10. Integration with margin safety manager
"""
import asyncio
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import json
import numpy as np
import pandas as pd
from collections import deque
import statistics
import multiprocessing as mp

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager
from ..risk.advanced_risk_manager import AdvancedRiskManager


class RiskLevel(Enum):
    """Risk level enumeration"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class AlertType(Enum):
    """Alert type enumeration"""
    POSITION_RISK = "position_risk"
    PORTFOLIO_RISK = "portfolio_risk"
    MARKET_RISK = "market_risk"
    LIQUIDITY_RISK = "liquidity_risk"
    TECHNICAL_RISK = "technical_risk"
    DRAWDOWN_RISK = "drawdown_risk"
    CORRELATION_RISK = "correlation_risk"
    MARGIN_RISK = "margin_risk"
    VOLATILITY_RISK = "volatility_risk"


class ProcessingPriority(Enum):
    """Risk processing priority levels"""
    EMERGENCY = "emergency"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class OptimizationMode(Enum):
    """Risk optimization modes"""
    CPU_EFFICIENCY = "cpu_efficiency"
    MEMORY_OPTIMIZATION = "memory_optimization"
    REAL_TIME_MONITORING = "real_time_monitoring"
    PREDICTIVE_MODELING = "predictive_modeling"


@dataclass
class RiskAlert:
    """Risk alert structure"""
    alert_id: str
    alert_type: AlertType
    risk_level: RiskLevel
    message: str
    affected_positions: List[str]
    recommended_actions: List[str]
    timestamp: datetime
    auto_action_taken: bool = False
    resolved: bool = False


@dataclass
class RiskAssessment:
    """Risk assessment structure"""
    assessment_id: str
    symbol: str
    risk_level: RiskLevel
    risk_score: float
    risk_factors: List[str]
    recommendations: List[str]
    confidence: float
    timestamp: datetime
    validity_period: timedelta


@dataclass
class PortfolioRisk:
    """Portfolio risk metrics"""
    total_exposure: float
    diversification_score: float
    correlation_risk: float
    concentration_risk: float
    liquidity_risk: float
    drawdown_risk: float
    var_1d: float  # Value at Risk 1 day
    var_7d: float  # Value at Risk 7 days
    expected_shortfall: float
    risk_adjusted_return: float


class CPUEfficientRiskCalculator:
    """CPU-efficient risk calculation engine"""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=max_workers)
        self.calculation_queue = asyncio.Queue(maxsize=300)
        self.processing_stats = {
            'calculations_processed': 0,
            'parallel_processed': 0,
            'avg_processing_time': 0.0,
            'cpu_efficiency': 0.0
        }

    async def calculate_risk_parallel(self, data: Dict[str, Any], calculation_type: str) -> Dict[str, Any]:
        """Calculate risk metrics with CPU optimization"""
        start_time = time.time()

        try:
            # Choose processing method based on calculation complexity
            if calculation_type in ['var_calculation', 'correlation_matrix', 'stress_test']:
                # CPU-intensive calculations - use process pool
                result = await asyncio.get_event_loop().run_in_executor(
                    self.process_pool, self._cpu_intensive_calculation, data, calculation_type
                )
            else:
                # Simpler calculations - use thread pool
                result = await asyncio.get_event_loop().run_in_executor(
                    self.thread_pool, self._simple_calculation, data, calculation_type
                )

            # Update processing stats
            processing_time = time.time() - start_time
            self._update_processing_stats(processing_time)

            return result

        except Exception as e:
            raise

    def _cpu_intensive_calculation(self, data: Dict[str, Any], calculation_type: str) -> Dict[str, Any]:
        """CPU-intensive risk calculations"""
        if calculation_type == 'var_calculation':
            # Value at Risk calculation
            returns = data.get('returns', [])
            if returns:
                var_95 = np.percentile(returns, 5)
                var_99 = np.percentile(returns, 1)
                return {
                    'var_95': var_95,
                    'var_99': var_99,
                    'expected_shortfall': np.mean([r for r in returns if r <= var_95]),
                    'processing_time': time.time()
                }
        elif calculation_type == 'correlation_matrix':
            # Correlation matrix calculation
            price_data = data.get('price_data', {})
            if len(price_data) >= 2:
                symbols = list(price_data.keys())
                correlation_matrix = {}
                for i, symbol1 in enumerate(symbols):
                    for symbol2 in symbols[i+1:]:
                        corr = np.corrcoef(price_data[symbol1], price_data[symbol2])[0, 1]
                        correlation_matrix[f"{symbol1}_{symbol2}"] = corr
                return {
                    'correlation_matrix': correlation_matrix,
                    'max_correlation': max(correlation_matrix.values()) if correlation_matrix else 0.0,
                    'processing_time': time.time()
                }
        elif calculation_type == 'stress_test':
            # Stress testing scenarios
            portfolio_value = data.get('portfolio_value', 100000)
            stress_scenarios = {
                'market_crash_20': portfolio_value * 0.8,
                'market_crash_30': portfolio_value * 0.7,
                'volatility_spike': portfolio_value * 0.85,
                'liquidity_crisis': portfolio_value * 0.75
            }
            return {
                'stress_scenarios': stress_scenarios,
                'worst_case_loss': min(stress_scenarios.values()) - portfolio_value,
                'processing_time': time.time()
            }
        return {}

    def _simple_calculation(self, data: Dict[str, Any], calculation_type: str) -> Dict[str, Any]:
        """Simple risk calculations"""
        if calculation_type == 'position_risk':
            position_size = data.get('position_size', 0)
            entry_price = data.get('entry_price', 0)
            current_price = data.get('current_price', 0)

            if entry_price > 0:
                unrealized_pnl = (current_price - entry_price) * position_size
                risk_percentage = abs(unrealized_pnl) / (entry_price * abs(position_size))
                return {
                    'unrealized_pnl': unrealized_pnl,
                    'risk_percentage': risk_percentage,
                    'risk_level': 'high' if risk_percentage > 0.05 else 'medium' if risk_percentage > 0.02 else 'low',
                    'processing_time': time.time()
                }
        elif calculation_type == 'portfolio_exposure':
            positions = data.get('positions', {})
            total_exposure = sum(abs(pos.get('notional_value', 0)) for pos in positions.values())
            account_balance = data.get('account_balance', 1)
            exposure_ratio = total_exposure / account_balance if account_balance > 0 else 0

            return {
                'total_exposure': total_exposure,
                'exposure_ratio': exposure_ratio,
                'risk_level': 'high' if exposure_ratio > 3.0 else 'medium' if exposure_ratio > 1.5 else 'low',
                'processing_time': time.time()
            }
        return {}

    def _update_processing_stats(self, processing_time: float):
        """Update processing statistics"""
        self.processing_stats['calculations_processed'] += 1
        current_avg = self.processing_stats['avg_processing_time']
        total_calculations = self.processing_stats['calculations_processed']

        if total_calculations > 0:
            self.processing_stats['avg_processing_time'] = (
                (current_avg * (total_calculations - 1) + processing_time) / total_calculations
            )


class MemoryOptimizedRiskStorage:
    """Memory-optimized storage for risk metrics and history"""

    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.risk_history = deque(maxlen=max_history_size)
        self.risk_cache = {}
        self.alert_cache = deque(maxlen=500)
        self.memory_stats = {
            'history_size': 0,
            'cache_size': 0,
            'memory_usage_mb': 0.0,
            'cleanup_count': 0
        }

    async def store_risk_data_optimized(self, key: str, data: Any, data_type: str = 'general'):
        """Store risk data with memory optimization"""
        timestamp = datetime.now()

        # Store in appropriate structure based on type
        if data_type == 'history':
            self.risk_history.append({
                'key': key,
                'data': data,
                'timestamp': timestamp
            })
        elif data_type == 'alert':
            self.alert_cache.append({
                'key': key,
                'data': data,
                'timestamp': timestamp
            })
        else:
            # General cache with TTL
            self.risk_cache[key] = {
                'data': data,
                'timestamp': timestamp,
                'ttl': 300  # 5 minutes default TTL
            }

        # Update memory stats
        self.memory_stats['history_size'] = len(self.risk_history)
        self.memory_stats['cache_size'] = len(self.risk_cache)

        # Cleanup if needed
        if len(self.risk_cache) > 200:
            await self._cleanup_expired_cache()

    async def get_risk_data_optimized(self, key: str) -> Optional[Any]:
        """Get risk data with cache optimization"""
        if key in self.risk_cache:
            entry = self.risk_cache[key]

            # Check if data is still valid
            age = (datetime.now() - entry['timestamp']).total_seconds()
            if age < entry['ttl']:
                return entry['data']
            else:
                # Remove expired data
                del self.risk_cache[key]

        return None

    async def _cleanup_expired_cache(self):
        """Cleanup expired cache entries"""
        current_time = datetime.now()
        expired_keys = []

        for key, entry in self.risk_cache.items():
            age = (current_time - entry['timestamp']).total_seconds()
            if age >= entry['ttl']:
                expired_keys.append(key)

        for key in expired_keys:
            del self.risk_cache[key]

        self.memory_stats['cleanup_count'] += 1


class RealTimePortfolioMonitor:
    """Real-time portfolio monitoring system"""

    def __init__(self):
        self.monitoring_active = False
        self.portfolio_data = {}
        self.risk_thresholds = {
            'max_drawdown': 0.10,
            'max_exposure': 3.0,
            'max_correlation': 0.70,
            'var_limit': 0.03
        }
        self.monitoring_stats = {
            'alerts_triggered': 0,
            'threshold_breaches': 0,
            'monitoring_cycles': 0
        }

    async def start_monitoring(self):
        """Start real-time portfolio monitoring"""
        self.monitoring_active = True
        asyncio.create_task(self._portfolio_monitoring_loop())

    async def update_portfolio_data(self, positions: Dict[str, Any], account_data: Dict[str, Any]):
        """Update portfolio data for monitoring"""
        self.portfolio_data = {
            'positions': positions,
            'account_data': account_data,
            'timestamp': datetime.now()
        }

    async def _portfolio_monitoring_loop(self):
        """Real-time portfolio monitoring loop"""
        while self.monitoring_active:
            try:
                if self.portfolio_data:
                    # Check risk thresholds
                    risk_alerts = await self._check_risk_thresholds()

                    # Process any alerts
                    for alert in risk_alerts:
                        await self._process_risk_alert(alert)

                    self.monitoring_stats['monitoring_cycles'] += 1

                await asyncio.sleep(5)  # Monitor every 5 seconds

            except Exception as e:
                await asyncio.sleep(10)

    async def _check_risk_thresholds(self) -> List[Dict[str, Any]]:
        """Check portfolio against risk thresholds"""
        alerts = []

        try:
            positions = self.portfolio_data.get('positions', {})
            account_data = self.portfolio_data.get('account_data', {})

            # Check exposure
            total_exposure = sum(abs(pos.get('notional_value', 0)) for pos in positions.values())
            account_balance = account_data.get('total_wallet_balance', 1)
            exposure_ratio = total_exposure / account_balance if account_balance > 0 else 0

            if exposure_ratio > self.risk_thresholds['max_exposure']:
                alerts.append({
                    'type': 'exposure_breach',
                    'severity': 'high',
                    'message': f"Exposure ratio {exposure_ratio:.2f} exceeds limit {self.risk_thresholds['max_exposure']}",
                    'current_value': exposure_ratio,
                    'threshold': self.risk_thresholds['max_exposure']
                })

            # Check drawdown
            unrealized_pnl = account_data.get('total_unrealized_pnl', 0)
            if unrealized_pnl < 0:
                drawdown = abs(unrealized_pnl) / account_balance if account_balance > 0 else 0
                if drawdown > self.risk_thresholds['max_drawdown']:
                    alerts.append({
                        'type': 'drawdown_breach',
                        'severity': 'critical',
                        'message': f"Drawdown {drawdown:.2f} exceeds limit {self.risk_thresholds['max_drawdown']}",
                        'current_value': drawdown,
                        'threshold': self.risk_thresholds['max_drawdown']
                    })

            return alerts

        except Exception as e:
            return []

    async def _process_risk_alert(self, alert: Dict[str, Any]):
        """Process risk alert"""
        self.monitoring_stats['alerts_triggered'] += 1
        if alert.get('severity') in ['high', 'critical']:
            self.monitoring_stats['threshold_breaches'] += 1


class IntelligentRiskThresholdAdapter:
    """Intelligent risk threshold adaptation system"""

    def __init__(self):
        self.threshold_history = deque(maxlen=1000)
        self.performance_tracking = {
            'threshold_adjustments': 0,
            'false_positives': 0,
            'missed_risks': 0,
            'adaptation_accuracy': 0.0
        }
        self.adaptive_thresholds = {
            'position_risk': 0.02,
            'portfolio_risk': 0.05,
            'correlation_limit': 0.70,
            'volatility_limit': 0.30
        }

    async def adapt_thresholds(self, market_conditions: Dict[str, Any], performance_data: Dict[str, Any]):
        """Adapt risk thresholds based on market conditions and performance"""
        try:
            # Adjust based on market volatility
            market_volatility = market_conditions.get('volatility', 0.20)

            if market_volatility > 0.40:  # High volatility
                # Tighten thresholds
                self.adaptive_thresholds['position_risk'] *= 0.8
                self.adaptive_thresholds['portfolio_risk'] *= 0.8
            elif market_volatility < 0.15:  # Low volatility
                # Relax thresholds slightly
                self.adaptive_thresholds['position_risk'] *= 1.1
                self.adaptive_thresholds['portfolio_risk'] *= 1.1

            # Adjust based on recent performance
            recent_accuracy = performance_data.get('risk_prediction_accuracy', 0.5)

            if recent_accuracy < 0.6:  # Poor accuracy
                # Be more conservative
                for key in self.adaptive_thresholds:
                    self.adaptive_thresholds[key] *= 0.9
            elif recent_accuracy > 0.8:  # Good accuracy
                # Can be slightly more aggressive
                for key in self.adaptive_thresholds:
                    self.adaptive_thresholds[key] *= 1.05

            # Ensure thresholds stay within reasonable bounds
            self.adaptive_thresholds['position_risk'] = max(0.01, min(0.05, self.adaptive_thresholds['position_risk']))
            self.adaptive_thresholds['portfolio_risk'] = max(0.02, min(0.10, self.adaptive_thresholds['portfolio_risk']))
            self.adaptive_thresholds['correlation_limit'] = max(0.50, min(0.90, self.adaptive_thresholds['correlation_limit']))

            # Track adaptation
            self.performance_tracking['threshold_adjustments'] += 1

            # Store in history
            self.threshold_history.append({
                'thresholds': self.adaptive_thresholds.copy(),
                'market_conditions': market_conditions,
                'timestamp': datetime.now()
            })

        except Exception as e:
            pass

    async def get_current_thresholds(self) -> Dict[str, float]:
        """Get current adaptive thresholds"""
        return self.adaptive_thresholds.copy()


class RiskAgent:
    """
    ENHANCED Specialized risk agent for comprehensive risk management

    Enhanced Capabilities:
    1. Integration with main risk management systems
    2. CPU-efficient risk calculation algorithms
    3. Real-time portfolio monitoring
    4. Optimized memory usage in risk metrics storage
    5. Parallel risk assessment
    6. Intelligent risk threshold adaptation
    7. Optimized correlation analysis algorithms
    8. Predictive risk modeling
    9. Dynamic position sizing recommendations
    10. Integration with margin safety manager

    Original Capabilities:
    - Real-time risk monitoring
    - Position risk assessment
    - Portfolio risk analysis
    - Drawdown monitoring
    - Correlation risk analysis
    - Liquidity risk assessment
    - Market risk evaluation
    - Emergency protocols
    - Automated risk responses
    - Risk reporting
    - Stress testing
    """
    
    def __init__(self, agent_id: str, config: BotConfig, database_manager: DatabaseManager, orchestrator):
        self.agent_id = agent_id
        self.config = config
        self.db_manager = database_manager
        self.orchestrator = orchestrator
        self.logger = TradingBotLogger(f"RiskAgent_{agent_id}")
        
        # Risk management components
        self.risk_manager = None

        # OPTIMIZATION COMPONENTS
        self.risk_calculator = CPUEfficientRiskCalculator(max_workers=4)
        self.risk_storage = MemoryOptimizedRiskStorage(max_history_size=1000)
        self.portfolio_monitor = RealTimePortfolioMonitor()
        self.threshold_adapter = IntelligentRiskThresholdAdapter()

        # Risk monitoring state
        self.active_alerts: Dict[str, RiskAlert] = {}
        self.risk_assessments: Dict[str, RiskAssessment] = {}
        self.portfolio_risk: Optional[PortfolioRisk] = None
        self.risk_history: List[Dict[str, Any]] = []

        # Optimization state
        self.optimization_mode = OptimizationMode.REAL_TIME_MONITORING
        self.parallel_processing_enabled = True
        self.adaptive_thresholds_enabled = True
        self.predictive_modeling_enabled = True
        
        # Risk limits and thresholds
        self.risk_limits = {
            'max_position_risk': 0.02,  # 2% per position
            'max_portfolio_risk': 0.05,  # 5% total portfolio
            'max_drawdown': 0.10,       # 10% maximum drawdown
            'max_correlation': 0.70,    # 70% maximum correlation
            'min_liquidity': 0.80,      # 80% minimum liquidity
            'var_limit': 0.03           # 3% VaR limit
        }
        
        # Enhanced Performance metrics
        self.metrics = {
            'alerts_generated': 0,
            'auto_actions_taken': 0,
            'risk_assessments_completed': 0,
            'emergency_stops': 0,
            'risk_score_average': 0.0,
            'alert_accuracy': 0.0,

            # OPTIMIZATION METRICS
            'cpu_efficiency': 0.0,
            'memory_efficiency': 0.0,
            'parallel_processing_rate': 0.0,
            'real_time_monitoring_rate': 0.0,
            'threshold_adaptation_accuracy': 0.0,
            'predictive_model_accuracy': 0.0,
            'correlation_analysis_speed': 0.0,
            'var_calculation_speed': 0.0,
            'portfolio_monitoring_cycles': 0,
            'adaptive_threshold_adjustments': 0
        }
        
        # Control flags
        self.is_running = False
        self.emergency_mode = False
        self.risk_monitoring_interval = 10  # seconds
        
        # Task handlers
        self.task_handlers = {
            'assess_position_risk': self._assess_position_risk_task,
            'assess_portfolio_risk': self._assess_portfolio_risk_task,
            'check_risk_limits': self._check_risk_limits_task,
            'calculate_var': self._calculate_var_task,
            'stress_test': self._stress_test_task,
            'emergency_stop': self._emergency_stop_task,
            'get_risk_report': self._get_risk_report_task,
            'update_risk_limits': self._update_risk_limits_task,
            'correlation_analysis': self._correlation_analysis_task,
            'liquidity_assessment': self._liquidity_assessment_task,
            'drawdown_analysis': self._drawdown_analysis_task
        }
    
    async def initialize(self):
        """Initialize the risk agent"""
        try:
            self.logger.info(f"Initializing Risk Agent {self.agent_id}")
            
            # Initialize advanced risk manager
            self.risk_manager = AdvancedRiskManager(
                self.config, 
                self.db_manager, 
                None  # Will get bybit client from trading agent
            )
            await self.risk_manager.initialize()
            
            # Load risk configuration
            await self._load_risk_configuration()

            # Initialize optimization components
            await self._initialize_optimization_components()

            # Start monitoring loops
            self.is_running = True
            asyncio.create_task(self._risk_monitoring_loop())
            asyncio.create_task(self._portfolio_risk_loop())
            asyncio.create_task(self._alert_management_loop())
            asyncio.create_task(self._emergency_monitoring_loop())

            # Start optimization loops
            asyncio.create_task(self._optimization_monitoring_loop())
            asyncio.create_task(self._adaptive_threshold_loop())
            asyncio.create_task(self._predictive_modeling_loop())

            self.logger.info(f"ENHANCED Risk Agent {self.agent_id} initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Risk Agent: {e}")
            raise
    
    async def assign_task(self, task):
        """Assign a task to this agent"""
        try:
            task_type = task.task_type
            handler = self.task_handlers.get(task_type)
            
            if handler:
                result = await handler(task.data)
                
                # Send result back to orchestrator
                await self.orchestrator.send_message(
                    sender=self.agent_id,
                    recipient='orchestrator',
                    message_type='task_response',
                    data={
                        'task_id': task.task_id,
                        'result': result,
                        'status': 'completed'
                    }
                )
                
                self.logger.info(f"Task {task.task_id} completed successfully")
            else:
                self.logger.error(f"Unknown task type: {task_type}")
                
        except Exception as e:
            self.logger.error(f"Error executing task {task.task_id}: {e}")
            
            # Send error response
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='orchestrator',
                message_type='task_response',
                data={
                    'task_id': task.task_id,
                    'error': str(e),
                    'status': 'failed'
                }
            )
    
    async def _load_risk_configuration(self):
        """Load risk configuration from database or config"""
        try:
            # Load custom risk limits if available
            custom_limits = getattr(self.config, 'risk_limits', {})
            self.risk_limits.update(custom_limits)
            
            self.logger.info("Risk configuration loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load risk configuration: {e}")

    # OPTIMIZATION METHODS
    async def _initialize_optimization_components(self):
        """Initialize optimization components"""
        try:
            # Start real-time portfolio monitoring
            await self.portfolio_monitor.start_monitoring()

            self.logger.info("Risk optimization components initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize risk optimization components: {e}")

    async def perform_enhanced_risk_assessment(self, positions: Dict[str, Any], account_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform enhanced risk assessment with optimization"""
        start_time = time.time()

        try:
            # Update portfolio monitor
            await self.portfolio_monitor.update_portfolio_data(positions, account_data)

            # Parallel risk calculations
            calculation_tasks = [
                self.risk_calculator.calculate_risk_parallel(
                    {'positions': positions, 'account_balance': account_data.get('total_wallet_balance', 1)},
                    'portfolio_exposure'
                ),
                self.risk_calculator.calculate_risk_parallel(
                    {'returns': self._extract_returns_from_positions(positions)},
                    'var_calculation'
                ),
                self.risk_calculator.calculate_risk_parallel(
                    {'price_data': self._extract_price_data_from_positions(positions)},
                    'correlation_matrix'
                ),
                self.risk_calculator.calculate_risk_parallel(
                    {'portfolio_value': account_data.get('total_wallet_balance', 100000)},
                    'stress_test'
                )
            ]

            exposure_result, var_result, correlation_result, stress_result = await asyncio.gather(*calculation_tasks)

            # Combine results
            enhanced_assessment = {
                'portfolio_exposure': exposure_result,
                'value_at_risk': var_result,
                'correlation_analysis': correlation_result,
                'stress_testing': stress_result,
                'processing_time': time.time() - start_time,
                'timestamp': datetime.now()
            }

            # Get adaptive thresholds
            current_thresholds = await self.threshold_adapter.get_current_thresholds()
            enhanced_assessment['adaptive_thresholds'] = current_thresholds

            # Generate risk alerts based on enhanced assessment
            risk_alerts = await self._generate_enhanced_risk_alerts(enhanced_assessment)
            enhanced_assessment['alerts'] = risk_alerts

            # Store in optimized storage
            await self.risk_storage.store_risk_data_optimized(
                f"enhanced_assessment_{int(time.time())}",
                enhanced_assessment,
                'history'
            )

            # Update metrics
            await self._update_enhanced_risk_metrics(enhanced_assessment)

            self.logger.info(f"Enhanced risk assessment completed in {time.time() - start_time:.2f}s")
            return enhanced_assessment

        except Exception as e:
            self.logger.error(f"Enhanced risk assessment failed: {e}")
            raise

    def _extract_returns_from_positions(self, positions: Dict[str, Any]) -> List[float]:
        """Extract returns data from positions for VaR calculation"""
        returns = []
        for position in positions.values():
            if 'unrealized_pnl_percentage' in position:
                returns.append(position['unrealized_pnl_percentage'])
            elif 'unrealized_pnl' in position and 'notional_value' in position:
                if position['notional_value'] != 0:
                    returns.append(position['unrealized_pnl'] / position['notional_value'])
        return returns if returns else [0.0]

    def _extract_price_data_from_positions(self, positions: Dict[str, Any]) -> Dict[str, List[float]]:
        """Extract price data from positions for correlation analysis"""
        price_data = {}
        for symbol, position in positions.items():
            # Simulate price data - in real implementation, would get historical prices
            current_price = position.get('mark_price', 100.0)
            price_data[symbol] = [current_price * (1 + np.random.normal(0, 0.01)) for _ in range(30)]
        return price_data

    async def _generate_enhanced_risk_alerts(self, assessment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate enhanced risk alerts from assessment"""
        alerts = []

        try:
            # Check portfolio exposure
            exposure_data = assessment.get('portfolio_exposure', {})
            if exposure_data.get('risk_level') == 'high':
                alerts.append({
                    'type': 'portfolio_exposure',
                    'severity': 'high',
                    'message': f"Portfolio exposure ratio: {exposure_data.get('exposure_ratio', 0):.2f}",
                    'recommended_action': 'Reduce position sizes'
                })

            # Check VaR limits
            var_data = assessment.get('value_at_risk', {})
            var_95 = abs(var_data.get('var_95', 0))
            if var_95 > 0.03:  # 3% VaR limit
                alerts.append({
                    'type': 'var_breach',
                    'severity': 'medium',
                    'message': f"VaR 95%: {var_95:.3f} exceeds limit",
                    'recommended_action': 'Review position sizing'
                })

            # Check correlation risks
            correlation_data = assessment.get('correlation_analysis', {})
            max_correlation = correlation_data.get('max_correlation', 0)
            if max_correlation > 0.70:
                alerts.append({
                    'type': 'correlation_risk',
                    'severity': 'medium',
                    'message': f"High correlation detected: {max_correlation:.2f}",
                    'recommended_action': 'Diversify positions'
                })

            # Check stress test results
            stress_data = assessment.get('stress_testing', {})
            worst_case_loss = stress_data.get('worst_case_loss', 0)
            if worst_case_loss < -10000:  # Significant loss threshold
                alerts.append({
                    'type': 'stress_test_failure',
                    'severity': 'high',
                    'message': f"Stress test shows potential loss: ${worst_case_loss:,.2f}",
                    'recommended_action': 'Implement hedging strategies'
                })

            return alerts

        except Exception as e:
            self.logger.error(f"Enhanced alert generation failed: {e}")
            return []

    async def _optimization_monitoring_loop(self):
        """Monitor optimization performance"""
        while self.is_running:
            try:
                # Calculate CPU efficiency
                if self.risk_calculator.processing_stats['calculations_processed'] > 0:
                    self.metrics['cpu_efficiency'] = min(1.0,
                        1.0 / (self.risk_calculator.processing_stats['avg_processing_time'] + 0.001)
                    )

                # Calculate memory efficiency
                history_size = self.risk_storage.memory_stats['history_size']
                max_history = self.risk_storage.max_history_size
                self.metrics['memory_efficiency'] = 1.0 - (history_size / max_history)

                # Calculate parallel processing rate
                total_calculations = self.risk_calculator.processing_stats['calculations_processed']
                parallel_calculations = self.risk_calculator.processing_stats['parallel_processed']
                if total_calculations > 0:
                    self.metrics['parallel_processing_rate'] = parallel_calculations / total_calculations

                # Update portfolio monitoring metrics
                self.metrics['portfolio_monitoring_cycles'] = self.portfolio_monitor.monitoring_stats['monitoring_cycles']
                self.metrics['real_time_monitoring_rate'] = (
                    self.portfolio_monitor.monitoring_stats['alerts_triggered'] /
                    max(1, self.portfolio_monitor.monitoring_stats['monitoring_cycles'])
                )

                # Update threshold adaptation metrics
                self.metrics['adaptive_threshold_adjustments'] = (
                    self.threshold_adapter.performance_tracking['threshold_adjustments']
                )
                self.metrics['threshold_adaptation_accuracy'] = (
                    self.threshold_adapter.performance_tracking['adaptation_accuracy']
                )

                await asyncio.sleep(30)  # Monitor every 30 seconds

            except Exception as e:
                self.logger.error(f"Risk optimization monitoring error: {e}")
                await asyncio.sleep(60)

    async def _adaptive_threshold_loop(self):
        """Adaptive threshold optimization loop"""
        while self.is_running:
            try:
                # Get current market conditions (simplified)
                market_conditions = {
                    'volatility': 0.25,  # Would get from market data
                    'trend_strength': 0.6,
                    'liquidity': 0.8
                }

                # Get performance data
                performance_data = {
                    'risk_prediction_accuracy': self.metrics.get('alert_accuracy', 0.5),
                    'false_positive_rate': 0.1,  # Would calculate from alert history
                    'missed_risk_rate': 0.05
                }

                # Adapt thresholds
                await self.threshold_adapter.adapt_thresholds(market_conditions, performance_data)

                # Update risk limits with adaptive thresholds
                adaptive_thresholds = await self.threshold_adapter.get_current_thresholds()
                self.risk_limits.update({
                    'max_position_risk': adaptive_thresholds.get('position_risk', 0.02),
                    'max_portfolio_risk': adaptive_thresholds.get('portfolio_risk', 0.05),
                    'max_correlation': adaptive_thresholds.get('correlation_limit', 0.70)
                })

                await asyncio.sleep(300)  # Adapt every 5 minutes

            except Exception as e:
                self.logger.error(f"Adaptive threshold optimization error: {e}")
                await asyncio.sleep(600)

    async def _predictive_modeling_loop(self):
        """Predictive risk modeling loop"""
        while self.is_running:
            try:
                # Get historical risk data
                risk_history = list(self.risk_storage.risk_history)

                if len(risk_history) >= 10:
                    # Simple predictive modeling (would use ML in real implementation)
                    recent_risks = [entry['data'].get('risk_score', 0.5) for entry in risk_history[-10:]]
                    predicted_risk = statistics.mean(recent_risks) * 1.1  # Simple trend prediction

                    # Update predictive model accuracy
                    if len(recent_risks) >= 2:
                        actual_change = recent_risks[-1] - recent_risks[-2]
                        predicted_change = predicted_risk - recent_risks[-1]
                        accuracy = 1.0 - abs(actual_change - predicted_change) / max(0.01, abs(actual_change))
                        self.metrics['predictive_model_accuracy'] = max(0.0, min(1.0, accuracy))

                await asyncio.sleep(600)  # Predict every 10 minutes

            except Exception as e:
                self.logger.error(f"Predictive modeling error: {e}")
                await asyncio.sleep(1200)

    async def _update_enhanced_risk_metrics(self, assessment: Dict[str, Any]):
        """Update enhanced risk metrics"""
        try:
            # Update standard metrics
            self.metrics['risk_assessments_completed'] += 1

            # Update processing speed metrics
            if 'processing_time' in assessment:
                # Update correlation analysis speed
                correlation_time = assessment.get('correlation_analysis', {}).get('processing_time', 0)
                if correlation_time > 0:
                    self.metrics['correlation_analysis_speed'] = 1.0 / correlation_time

                # Update VaR calculation speed
                var_time = assessment.get('value_at_risk', {}).get('processing_time', 0)
                if var_time > 0:
                    self.metrics['var_calculation_speed'] = 1.0 / var_time

            # Update alert accuracy if feedback is available
            alerts = assessment.get('alerts', [])
            if alerts and 'feedback' in assessment:
                # Would update based on actual alert outcomes
                pass

        except Exception as e:
            self.logger.error(f"Enhanced risk metrics update failed: {e}")

    async def _assess_position_risk_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk for a specific position with ENHANCED optimization"""
        try:
            position = data['position']
            market_data = data.get('market_data', {})

            # Use enhanced risk calculation with parallel processing
            if self.parallel_processing_enabled:
                risk_calculation_data = {
                    'position_size': position.get('size', 0),
                    'entry_price': position.get('avg_price', 0),
                    'current_price': position.get('mark_price', 0)
                }

                enhanced_risk_metrics = await self.risk_calculator.calculate_risk_parallel(
                    risk_calculation_data, 'position_risk'
                )
            else:
                # Fallback to traditional calculation
                enhanced_risk_metrics = await self._calculate_position_risk_metrics(position, market_data)

            # Get adaptive thresholds
            adaptive_thresholds = await self.threshold_adapter.get_current_thresholds()

            # Determine risk level using adaptive thresholds
            risk_score = enhanced_risk_metrics.get('risk_percentage', 0.0)
            risk_level = self._determine_adaptive_risk_level(risk_score, adaptive_thresholds)

            # Generate enhanced risk assessment
            assessment = RiskAssessment(
                assessment_id=f"enhanced_risk_{position['symbol']}_{int(time.time())}",
                symbol=position['symbol'],
                risk_level=risk_level,
                risk_score=risk_score,
                risk_factors=self._generate_enhanced_risk_factors(enhanced_risk_metrics, adaptive_thresholds),
                recommendations=self._generate_enhanced_recommendations(enhanced_risk_metrics, risk_level),
                confidence=enhanced_risk_metrics.get('confidence', 0.8),
                timestamp=datetime.now(),
                validity_period=timedelta(minutes=30)
            )

            # Store assessment in optimized storage
            await self.risk_storage.store_risk_data_optimized(
                assessment.assessment_id, assessment, 'general'
            )
            self.risk_assessments[assessment.assessment_id] = assessment
            self.metrics['risk_assessments_completed'] += 1
            
            # Generate alert if high risk
            if risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                await self._generate_risk_alert(assessment)
            
            return {
                'status': 'success',
                'assessment': assessment.__dict__,
                'risk_metrics': risk_metrics
            }
            
        except Exception as e:
            self.logger.error(f"Position risk assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_position_risk_metrics(self, position: Dict[str, Any], 
                                             market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive position risk metrics"""
        try:
            symbol = position['symbol']
            size = float(position['size'])
            entry_price = float(position['entry_price'])
            current_price = float(position['current_price'])
            
            # Calculate basic metrics
            unrealized_pnl = (current_price - entry_price) / entry_price
            position_value = size * current_price
            
            # Calculate volatility
            price_history = market_data.get('price_history', [])
            if len(price_history) > 20:
                returns = np.diff(price_history) / price_history[:-1]
                volatility = np.std(returns) * np.sqrt(365)  # Annualized
            else:
                volatility = 0.20  # Default 20% volatility
            
            # Calculate Value at Risk (1 day, 95% confidence)
            var_1d = position_value * volatility / np.sqrt(365) * 1.645
            
            # Calculate maximum loss potential
            stop_loss = position.get('stop_loss')
            if stop_loss:
                max_loss = abs(float(stop_loss) - entry_price) / entry_price
            else:
                max_loss = 0.50  # Assume 50% max loss without stop loss
            
            # Risk score calculation (0-1 scale)
            risk_factors = []
            risk_score = 0.0
            
            # Position size risk
            portfolio_value = await self._get_portfolio_value()
            position_percentage = position_value / portfolio_value if portfolio_value > 0 else 0
            
            if position_percentage > self.risk_limits['max_position_risk']:
                risk_score += 0.3
                risk_factors.append(f"Position size {position_percentage:.1%} exceeds limit")
            
            # Volatility risk
            if volatility > 0.50:  # 50% annual volatility
                risk_score += 0.2
                risk_factors.append(f"High volatility: {volatility:.1%}")
            
            # Unrealized loss risk
            if unrealized_pnl < -0.05:  # -5% unrealized loss
                risk_score += 0.2
                risk_factors.append(f"Unrealized loss: {unrealized_pnl:.1%}")
            
            # No stop loss risk
            if not stop_loss:
                risk_score += 0.2
                risk_factors.append("No stop loss set")
            
            # Liquidity risk
            volume = market_data.get('volume', 0)
            if volume < position_value * 10:  # Position > 10% of daily volume
                risk_score += 0.1
                risk_factors.append("Low liquidity")
            
            # Generate recommendations
            recommendations = []
            if position_percentage > self.risk_limits['max_position_risk']:
                recommendations.append("Reduce position size")
            if not stop_loss:
                recommendations.append("Set stop loss")
            if volatility > 0.50:
                recommendations.append("Consider volatility-adjusted position sizing")
            if unrealized_pnl < -0.10:
                recommendations.append("Consider closing position")
            
            # Confidence based on data quality
            confidence = 0.8 if len(price_history) > 50 else 0.6
            
            return {
                'risk_score': min(risk_score, 1.0),
                'risk_factors': risk_factors,
                'recommendations': recommendations,
                'confidence': confidence,
                'metrics': {
                    'position_value': position_value,
                    'position_percentage': position_percentage,
                    'unrealized_pnl': unrealized_pnl,
                    'volatility': volatility,
                    'var_1d': var_1d,
                    'max_loss_potential': max_loss
                }
            }
            
        except Exception as e:
            self.logger.error(f"Position risk calculation failed: {e}")
            return {
                'risk_score': 1.0,  # Assume high risk on error
                'risk_factors': ['Risk calculation error'],
                'recommendations': ['Manual review required'],
                'confidence': 0.0,
                'metrics': {}
            }
    
    async def _get_portfolio_value(self) -> float:
        """Get current portfolio value"""
        try:
            # Get shared portfolio data from orchestrator
            portfolio_data = self.orchestrator.get_shared_data('portfolio_value')
            if portfolio_data:
                return float(portfolio_data)
            
            # Fallback: calculate from positions
            positions_data = self.orchestrator.get_shared_data('current_positions')
            if positions_data:
                total_value = 0.0
                for position in positions_data:
                    total_value += float(position['size']) * float(position['current_price'])
                return total_value
            
            return 10000.0  # Default portfolio value
            
        except Exception as e:
            self.logger.error(f"Portfolio value calculation failed: {e}")
            return 10000.0

    # ENHANCED HELPER METHODS
    def _determine_adaptive_risk_level(self, risk_score: float, adaptive_thresholds: Dict[str, float]) -> RiskLevel:
        """Determine risk level using adaptive thresholds"""
        position_risk_threshold = adaptive_thresholds.get('position_risk', 0.02)

        if risk_score >= position_risk_threshold * 3:
            return RiskLevel.CRITICAL
        elif risk_score >= position_risk_threshold * 2:
            return RiskLevel.VERY_HIGH
        elif risk_score >= position_risk_threshold * 1.5:
            return RiskLevel.HIGH
        elif risk_score >= position_risk_threshold:
            return RiskLevel.MEDIUM
        elif risk_score >= position_risk_threshold * 0.5:
            return RiskLevel.LOW
        else:
            return RiskLevel.VERY_LOW

    def _generate_enhanced_risk_factors(self, risk_metrics: Dict[str, Any], adaptive_thresholds: Dict[str, float]) -> List[str]:
        """Generate enhanced risk factors based on metrics and adaptive thresholds"""
        risk_factors = []

        try:
            risk_percentage = risk_metrics.get('risk_percentage', 0.0)
            position_risk_threshold = adaptive_thresholds.get('position_risk', 0.02)

            if risk_percentage > position_risk_threshold:
                risk_factors.append(f"Position risk {risk_percentage:.3f} exceeds adaptive threshold {position_risk_threshold:.3f}")

            unrealized_pnl = risk_metrics.get('unrealized_pnl', 0.0)
            if unrealized_pnl < -0.05:
                risk_factors.append(f"Significant unrealized loss: {unrealized_pnl:.3f}")

            # Add processing time as a factor if too slow
            processing_time = risk_metrics.get('processing_time', 0.0)
            if processing_time > 1.0:
                risk_factors.append(f"Slow risk calculation: {processing_time:.2f}s")

            return risk_factors

        except Exception as e:
            return [f"Risk factor generation error: {str(e)}"]

    def _generate_enhanced_recommendations(self, risk_metrics: Dict[str, Any], risk_level: RiskLevel) -> List[str]:
        """Generate enhanced recommendations based on risk metrics and level"""
        recommendations = []

        try:
            if risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                recommendations.append("Consider reducing position size")
                recommendations.append("Implement or tighten stop-loss orders")

                if risk_level == RiskLevel.CRITICAL:
                    recommendations.append("URGENT: Consider immediate position closure")
                    recommendations.append("Review risk management parameters")

            elif risk_level == RiskLevel.MEDIUM:
                recommendations.append("Monitor position closely")
                recommendations.append("Consider partial profit taking if in profit")

            elif risk_level in [RiskLevel.LOW, RiskLevel.VERY_LOW]:
                recommendations.append("Position within acceptable risk parameters")
                recommendations.append("Consider position size optimization for better returns")

            # Add specific recommendations based on metrics
            risk_percentage = risk_metrics.get('risk_percentage', 0.0)
            if risk_percentage > 0.03:
                recommendations.append("Position size exceeds 3% risk - consider reduction")

            unrealized_pnl = risk_metrics.get('unrealized_pnl', 0.0)
            if unrealized_pnl > 0.10:
                recommendations.append("Consider taking partial profits")
            elif unrealized_pnl < -0.05:
                recommendations.append("Review exit strategy")

            return recommendations

        except Exception as e:
            return [f"Recommendation generation error: {str(e)}"]

    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level from risk score"""
        if risk_score < 0.2:
            return RiskLevel.VERY_LOW
        elif risk_score < 0.4:
            return RiskLevel.LOW
        elif risk_score < 0.6:
            return RiskLevel.MEDIUM
        elif risk_score < 0.8:
            return RiskLevel.HIGH
        elif risk_score < 0.95:
            return RiskLevel.VERY_HIGH
        else:
            return RiskLevel.CRITICAL
    
    async def _generate_risk_alert(self, assessment: RiskAssessment):
        """Generate risk alert from assessment"""
        try:
            alert = RiskAlert(
                alert_id=f"alert_{assessment.symbol}_{int(time.time())}",
                alert_type=AlertType.POSITION_RISK,
                risk_level=assessment.risk_level,
                message=f"High risk detected for {assessment.symbol}: {assessment.risk_score:.2f}",
                affected_positions=[assessment.symbol],
                recommended_actions=assessment.recommendations,
                timestamp=datetime.now(),
                auto_action_taken=False,
                resolved=False
            )
            
            # Store alert
            self.active_alerts[alert.alert_id] = alert
            self.metrics['alerts_generated'] += 1
            
            # Send alert to orchestrator
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='orchestrator',
                message_type='alert',
                data={
                    'type': alert.alert_type.value,
                    'severity': alert.risk_level.value,
                    'details': alert.message,
                    'recommendations': alert.recommended_actions,
                    'alert_id': alert.alert_id
                }
            )
            
            # Take automatic action if critical
            if assessment.risk_level == RiskLevel.CRITICAL:
                await self._take_automatic_action(alert)
            
            self.logger.warning(f"Risk alert generated: {alert.message}")
            
        except Exception as e:
            self.logger.error(f"Alert generation failed: {e}")
    
    async def _take_automatic_action(self, alert: RiskAlert):
        """Take automatic action for critical alerts"""
        try:
            if alert.risk_level == RiskLevel.CRITICAL:
                # Request emergency position closure
                await self.orchestrator.send_message(
                    sender=self.agent_id,
                    recipient='trading',
                    message_type='task_request',
                    data={
                        'task_type': 'close_position',
                        'target_agent': 'trading',
                        'priority': 1,  # Critical priority
                        'data': {
                            'symbol': alert.affected_positions[0],
                            'reason': 'emergency_risk_limit'
                        }
                    }
                )
                
                alert.auto_action_taken = True
                self.metrics['auto_actions_taken'] += 1
                
                self.logger.critical(f"Automatic emergency action taken for {alert.alert_id}")
            
        except Exception as e:
            self.logger.error(f"Automatic action failed: {e}")
    
    async def _assess_portfolio_risk_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall portfolio risk"""
        try:
            positions = data.get('positions', [])
            market_data = data.get('market_data', {})
            
            # Calculate portfolio risk metrics
            portfolio_risk = await self._calculate_portfolio_risk(positions, market_data)
            
            # Update stored portfolio risk
            self.portfolio_risk = portfolio_risk
            
            # Check portfolio risk limits
            risk_violations = await self._check_portfolio_risk_limits(portfolio_risk)
            
            return {
                'status': 'success',
                'portfolio_risk': portfolio_risk.__dict__,
                'risk_violations': risk_violations
            }
            
        except Exception as e:
            self.logger.error(f"Portfolio risk assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_portfolio_risk(self, positions: List[Dict[str, Any]], 
                                      market_data: Dict[str, Any]) -> PortfolioRisk:
        """Calculate comprehensive portfolio risk metrics"""
        try:
            if not positions:
                return PortfolioRisk(
                    total_exposure=0.0, diversification_score=1.0, correlation_risk=0.0,
                    concentration_risk=0.0, liquidity_risk=0.0, drawdown_risk=0.0,
                    var_1d=0.0, var_7d=0.0, expected_shortfall=0.0, risk_adjusted_return=0.0
                )
            
            # Calculate total exposure
            total_exposure = sum(
                float(pos['size']) * float(pos['current_price']) for pos in positions
            )
            
            # Calculate concentration risk (Herfindahl Index)
            position_weights = []
            for pos in positions:
                weight = (float(pos['size']) * float(pos['current_price'])) / total_exposure
                position_weights.append(weight)
            
            concentration_risk = sum(w**2 for w in position_weights)
            diversification_score = 1 - concentration_risk
            
            # Calculate correlation risk
            correlation_risk = await self._calculate_correlation_risk(positions, market_data)
            
            # Calculate liquidity risk
            liquidity_risk = await self._calculate_liquidity_risk(positions, market_data)
            
            # Calculate drawdown risk
            drawdown_risk = await self._calculate_drawdown_risk(positions)
            
            # Calculate Value at Risk
            var_1d, var_7d = await self._calculate_portfolio_var(positions, market_data)
            
            # Calculate Expected Shortfall (CVaR)
            expected_shortfall = var_1d * 1.5  # Simplified calculation
            
            # Calculate risk-adjusted return (Sharpe ratio approximation)
            portfolio_return = await self._calculate_portfolio_return(positions)
            risk_adjusted_return = portfolio_return / (var_1d * np.sqrt(365)) if var_1d > 0 else 0.0
            
            return PortfolioRisk(
                total_exposure=total_exposure,
                diversification_score=diversification_score,
                correlation_risk=correlation_risk,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk,
                drawdown_risk=drawdown_risk,
                var_1d=var_1d,
                var_7d=var_7d,
                expected_shortfall=expected_shortfall,
                risk_adjusted_return=risk_adjusted_return
            )
            
        except Exception as e:
            self.logger.error(f"Portfolio risk calculation failed: {e}")
            return PortfolioRisk(
                total_exposure=0.0, diversification_score=0.0, correlation_risk=1.0,
                concentration_risk=1.0, liquidity_risk=1.0, drawdown_risk=1.0,
                var_1d=0.0, var_7d=0.0, expected_shortfall=0.0, risk_adjusted_return=0.0
            )
    
    async def _calculate_correlation_risk(self, positions: List[Dict[str, Any]], 
                                        market_data: Dict[str, Any]) -> float:
        """Calculate portfolio correlation risk"""
        try:
            if len(positions) < 2:
                return 0.0
            
            # Get correlation matrix from research agent
            correlation_data = self.orchestrator.get_shared_data('correlation_matrix')
            if not correlation_data:
                return 0.5  # Assume moderate correlation
            
            # Calculate weighted average correlation
            symbols = [pos['symbol'] for pos in positions]
            weights = []
            
            total_value = sum(float(pos['size']) * float(pos['current_price']) for pos in positions)
            for pos in positions:
                weight = (float(pos['size']) * float(pos['current_price'])) / total_value
                weights.append(weight)
            
            # Calculate portfolio correlation
            total_correlation = 0.0
            total_weight = 0.0
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols):
                    if i != j and symbol1 in correlation_data and symbol2 in correlation_data[symbol1]:
                        correlation = correlation_data[symbol1][symbol2]
                        weight = weights[i] * weights[j]
                        total_correlation += abs(correlation) * weight
                        total_weight += weight
            
            return total_correlation / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Correlation risk calculation failed: {e}")
            return 0.5
    
    async def _calculate_liquidity_risk(self, positions: List[Dict[str, Any]], 
                                      market_data: Dict[str, Any]) -> float:
        """Calculate portfolio liquidity risk"""
        try:
            liquidity_scores = []
            
            for pos in positions:
                symbol = pos['symbol']
                position_value = float(pos['size']) * float(pos['current_price'])
                
                # Get market data for symbol
                symbol_data = market_data.get(symbol, {})
                daily_volume = symbol_data.get('volume', 0)
                
                # Calculate liquidity score (position size vs daily volume)
                if daily_volume > 0:
                    liquidity_ratio = position_value / daily_volume
                    liquidity_score = max(0, 1 - liquidity_ratio * 10)  # Penalize large positions
                else:
                    liquidity_score = 0.0  # No volume data = illiquid
                
                liquidity_scores.append(liquidity_score)
            
            # Return average liquidity score (inverted for risk)
            avg_liquidity = np.mean(liquidity_scores) if liquidity_scores else 0.0
            return 1 - avg_liquidity  # Convert to risk measure
            
        except Exception as e:
            self.logger.error(f"Liquidity risk calculation failed: {e}")
            return 1.0  # Assume high liquidity risk on error
    
    async def _calculate_drawdown_risk(self, positions: List[Dict[str, Any]]) -> float:
        """Calculate portfolio drawdown risk"""
        try:
            # Get portfolio performance history
            performance_history = self.orchestrator.get_shared_data('portfolio_performance')
            if not performance_history:
                return 0.0
            
            # Calculate current drawdown
            peak_value = max(performance_history)
            current_value = performance_history[-1]
            current_drawdown = (peak_value - current_value) / peak_value
            
            # Calculate maximum historical drawdown
            max_drawdown = 0.0
            running_max = performance_history[0]
            
            for value in performance_history:
                running_max = max(running_max, value)
                drawdown = (running_max - value) / running_max
                max_drawdown = max(max_drawdown, drawdown)
            
            # Return current drawdown as risk measure
            return current_drawdown
            
        except Exception as e:
            self.logger.error(f"Drawdown risk calculation failed: {e}")
            return 0.0
    
    async def _calculate_portfolio_var(self, positions: List[Dict[str, Any]], 
                                     market_data: Dict[str, Any]) -> Tuple[float, float]:
        """Calculate portfolio Value at Risk"""
        try:
            # Simplified VaR calculation using historical simulation
            portfolio_returns = []
            
            # Get historical returns for each position
            for pos in positions:
                symbol = pos['symbol']
                symbol_data = market_data.get(symbol, {})
                price_history = symbol_data.get('price_history', [])
                
                if len(price_history) > 1:
                    returns = np.diff(price_history) / price_history[:-1]
                    weight = (float(pos['size']) * float(pos['current_price'])) / await self._get_portfolio_value()
                    weighted_returns = returns * weight
                    
                    if len(portfolio_returns) == 0:
                        portfolio_returns = weighted_returns
                    else:
                        # Align lengths and add
                        min_len = min(len(portfolio_returns), len(weighted_returns))
                        portfolio_returns = portfolio_returns[-min_len:] + weighted_returns[-min_len:]
            
            if len(portfolio_returns) == 0:
                return 0.0, 0.0
            
            # Calculate VaR at 95% confidence
            var_1d = np.percentile(portfolio_returns, 5) * await self._get_portfolio_value()
            var_7d = var_1d * np.sqrt(7)
            
            return abs(var_1d), abs(var_7d)
            
        except Exception as e:
            self.logger.error(f"VaR calculation failed: {e}")
            return 0.0, 0.0
    
    async def _calculate_portfolio_return(self, positions: List[Dict[str, Any]]) -> float:
        """Calculate portfolio return"""
        try:
            total_pnl = 0.0
            total_investment = 0.0
            
            for pos in positions:
                entry_price = float(pos['entry_price'])
                current_price = float(pos['current_price'])
                size = float(pos['size'])
                
                pnl = (current_price - entry_price) * size
                investment = entry_price * size
                
                total_pnl += pnl
                total_investment += investment
            
            return total_pnl / total_investment if total_investment > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Portfolio return calculation failed: {e}")
            return 0.0
    
    async def _check_portfolio_risk_limits(self, portfolio_risk: PortfolioRisk) -> List[str]:
        """Check portfolio risk against limits"""
        violations = []
        
        try:
            # Check diversification
            if portfolio_risk.diversification_score < 0.5:
                violations.append("Poor diversification")
            
            # Check concentration
            if portfolio_risk.concentration_risk > 0.4:
                violations.append("High concentration risk")
            
            # Check correlation
            if portfolio_risk.correlation_risk > self.risk_limits['max_correlation']:
                violations.append("High correlation risk")
            
            # Check liquidity
            if portfolio_risk.liquidity_risk > (1 - self.risk_limits['min_liquidity']):
                violations.append("High liquidity risk")
            
            # Check drawdown
            if portfolio_risk.drawdown_risk > self.risk_limits['max_drawdown']:
                violations.append("Maximum drawdown exceeded")
            
            # Check VaR
            portfolio_value = await self._get_portfolio_value()
            var_percentage = portfolio_risk.var_1d / portfolio_value
            if var_percentage > self.risk_limits['var_limit']:
                violations.append("VaR limit exceeded")
            
            return violations
            
        except Exception as e:
            self.logger.error(f"Risk limit check failed: {e}")
            return ["Risk check error"]
    
    async def _check_risk_limits_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Check all risk limits"""
        try:
            # Get current positions
            positions = data.get('positions', [])
            
            violations = []
            
            # Check individual position limits
            for pos in positions:
                position_value = float(pos['size']) * float(pos['current_price'])
                portfolio_value = await self._get_portfolio_value()
                position_percentage = position_value / portfolio_value
                
                if position_percentage > self.risk_limits['max_position_risk']:
                    violations.append(f"Position {pos['symbol']} exceeds size limit")
            
            # Check portfolio limits
            if self.portfolio_risk:
                portfolio_violations = await self._check_portfolio_risk_limits(self.portfolio_risk)
                violations.extend(portfolio_violations)
            
            return {
                'status': 'success',
                'violations': violations,
                'risk_limits': self.risk_limits
            }
            
        except Exception as e:
            self.logger.error(f"Risk limits check failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_var_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Value at Risk"""
        try:
            positions = data.get('positions', [])
            market_data = data.get('market_data', {})
            confidence_level = data.get('confidence_level', 0.95)
            time_horizon = data.get('time_horizon', 1)  # days
            
            var_1d, var_7d = await self._calculate_portfolio_var(positions, market_data)
            
            # Scale VaR for requested time horizon
            var_scaled = var_1d * np.sqrt(time_horizon)
            
            return {
                'status': 'success',
                'var_1d': var_1d,
                'var_7d': var_7d,
                'var_scaled': var_scaled,
                'confidence_level': confidence_level,
                'time_horizon': time_horizon
            }
            
        except Exception as e:
            self.logger.error(f"VaR calculation task failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _stress_test_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform stress testing on portfolio"""
        try:
            positions = data.get('positions', [])
            scenarios = data.get('scenarios', [])
            
            # Default stress scenarios if none provided
            if not scenarios:
                scenarios = [
                    {'name': 'Market Crash -20%', 'change': -0.20},
                    {'name': 'Market Surge +20%', 'change': 0.20},
                    {'name': 'High Volatility', 'volatility_multiplier': 2.0},
                    {'name': 'Correlation Spike', 'correlation_increase': 0.30}
                ]
            
            stress_results = []
            
            for scenario in scenarios:
                result = await self._run_stress_scenario(positions, scenario)
                stress_results.append(result)
            
            return {
                'status': 'success',
                'stress_results': stress_results
            }
            
        except Exception as e:
            self.logger.error(f"Stress test failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _run_stress_scenario(self, positions: List[Dict[str, Any]], 
                                 scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single stress test scenario"""
        try:
            scenario_name = scenario['name']
            
            # Calculate portfolio value under scenario
            total_pnl_change = 0.0
            
            for pos in positions:
                position_value = float(pos['size']) * float(pos['current_price'])
                
                # Apply scenario shock
                if 'change' in scenario:
                    # Market move scenario
                    pnl_change = position_value * scenario['change']
                    total_pnl_change += pnl_change
                
                elif 'volatility_multiplier' in scenario:
                    # Volatility shock - estimate impact
                    # Higher volatility typically leads to wider spreads and potential losses
                    vol_impact = position_value * 0.01 * scenario['volatility_multiplier']
                    total_pnl_change -= vol_impact  # Negative impact
            
            portfolio_value = await self._get_portfolio_value()
            percentage_impact = total_pnl_change / portfolio_value
            
            return {
                'scenario': scenario_name,
                'pnl_impact': total_pnl_change,
                'percentage_impact': percentage_impact,
                'portfolio_value_after': portfolio_value + total_pnl_change
            }
            
        except Exception as e:
            self.logger.error(f"Stress scenario execution failed: {e}")
            return {
                'scenario': scenario.get('name', 'Unknown'),
                'error': str(e)
            }
    
    async def _emergency_stop_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute emergency stop procedures"""
        try:
            reason = data.get('reason', 'Manual emergency stop')
            
            # Enter emergency mode
            self.emergency_mode = True
            self.metrics['emergency_stops'] += 1
            
            # Send emergency stop to trading agent
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='trading',
                message_type='alert',
                data={
                    'type': 'emergency_stop',
                    'severity': 'critical',
                    'details': f'Emergency stop triggered: {reason}',
                    'action_required': 'close_all_positions'
                }
            )
            
            # Generate emergency alert
            emergency_alert = RiskAlert(
                alert_id=f"emergency_{int(time.time())}",
                alert_type=AlertType.TECHNICAL_RISK,
                risk_level=RiskLevel.CRITICAL,
                message=f"Emergency stop activated: {reason}",
                affected_positions=[],
                recommended_actions=['Close all positions', 'Stop trading'],
                timestamp=datetime.now(),
                auto_action_taken=True
            )
            
            self.active_alerts[emergency_alert.alert_id] = emergency_alert
            
            self.logger.critical(f"Emergency stop executed: {reason}")
            
            return {
                'status': 'success',
                'emergency_mode': True,
                'reason': reason,
                'alert_id': emergency_alert.alert_id
            }
            
        except Exception as e:
            self.logger.error(f"Emergency stop failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _get_risk_report_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive risk report"""
        try:
            report_type = data.get('type', 'summary')
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'portfolio_risk': self.portfolio_risk.__dict__ if self.portfolio_risk else None,
                'active_alerts': [alert.__dict__ for alert in self.active_alerts.values()],
                'risk_assessments': len(self.risk_assessments),
                'risk_limits': self.risk_limits,
                'emergency_mode': self.emergency_mode,
                'metrics': self.metrics
            }
            
            if report_type == 'detailed':
                report['assessment_history'] = [
                    assessment.__dict__ for assessment in self.risk_assessments.values()
                ]
                report['risk_history'] = self.risk_history[-100:]  # Last 100 entries
            
            return {
                'status': 'success',
                'report': report
            }
            
        except Exception as e:
            self.logger.error(f"Risk report generation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _update_risk_limits_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update risk limits"""
        try:
            new_limits = data.get('limits', {})
            
            # Validate new limits
            for key, value in new_limits.items():
                if key in self.risk_limits and isinstance(value, (int, float)) and 0 <= value <= 1:
                    self.risk_limits[key] = value
                else:
                    self.logger.warning(f"Invalid risk limit: {key} = {value}")
            
            self.logger.info(f"Risk limits updated: {new_limits}")
            
            return {
                'status': 'success',
                'updated_limits': self.risk_limits
            }
            
        except Exception as e:
            self.logger.error(f"Risk limits update failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _correlation_analysis_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform correlation analysis"""
        try:
            positions = data.get('positions', [])
            
            if len(positions) < 2:
                return {
                    'status': 'success',
                    'correlation_risk': 0.0,
                    'message': 'Insufficient positions for correlation analysis'
                }
            
            # Get correlation data from research agent
            symbols = [pos['symbol'] for pos in positions]
            
            # Request correlation analysis from research agent
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='research',
                message_type='task_request',
                data={
                    'task_type': 'correlation_analysis',
                    'data': {'symbols': symbols}
                }
            )
            
            # Calculate current correlation risk
            correlation_risk = await self._calculate_correlation_risk(positions, {})
            
            return {
                'status': 'success',
                'correlation_risk': correlation_risk,
                'symbols_analyzed': symbols
            }
            
        except Exception as e:
            self.logger.error(f"Correlation analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _liquidity_assessment_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess liquidity risk"""
        try:
            positions = data.get('positions', [])
            market_data = data.get('market_data', {})
            
            liquidity_risk = await self._calculate_liquidity_risk(positions, market_data)
            
            # Generate liquidity assessment for each position
            position_assessments = []
            
            for pos in positions:
                symbol = pos['symbol']
                position_value = float(pos['size']) * float(pos['current_price'])
                
                symbol_data = market_data.get(symbol, {})
                daily_volume = symbol_data.get('volume', 0)
                
                liquidity_ratio = position_value / daily_volume if daily_volume > 0 else float('inf')
                
                assessment = {
                    'symbol': symbol,
                    'position_value': position_value,
                    'daily_volume': daily_volume,
                    'liquidity_ratio': liquidity_ratio,
                    'risk_level': 'high' if liquidity_ratio > 0.1 else 'low'
                }
                
                position_assessments.append(assessment)
            
            return {
                'status': 'success',
                'portfolio_liquidity_risk': liquidity_risk,
                'position_assessments': position_assessments
            }
            
        except Exception as e:
            self.logger.error(f"Liquidity assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _drawdown_analysis_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze drawdown risk"""
        try:
            positions = data.get('positions', [])
            
            drawdown_risk = await self._calculate_drawdown_risk(positions)
            
            # Get performance history
            performance_history = self.orchestrator.get_shared_data('portfolio_performance')
            
            if performance_history:
                # Calculate detailed drawdown metrics
                peak = max(performance_history)
                current = performance_history[-1]
                current_drawdown = (peak - current) / peak
                
                # Calculate maximum drawdown
                max_dd = 0.0
                running_max = performance_history[0]
                
                for value in performance_history:
                    running_max = max(running_max, value)
                    dd = (running_max - value) / running_max
                    max_dd = max(max_dd, dd)
                
                # Calculate recovery time (if currently in drawdown)
                recovery_time = 0
                if current_drawdown > 0.01:  # 1% threshold
                    for i in range(len(performance_history) - 1, -1, -1):
                        if performance_history[i] >= peak * 0.99:  # Within 1% of peak
                            recovery_time = len(performance_history) - 1 - i
                            break
                
                return {
                    'status': 'success',
                    'current_drawdown': current_drawdown,
                    'maximum_drawdown': max_dd,
                    'recovery_time_periods': recovery_time,
                    'drawdown_risk_score': drawdown_risk
                }
            else:
                return {
                    'status': 'success',
                    'drawdown_risk_score': drawdown_risk,
                    'message': 'No performance history available'
                }
            
        except Exception as e:
            self.logger.error(f"Drawdown analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _risk_monitoring_loop(self):
        """Main risk monitoring loop"""
        while self.is_running:
            try:
                # Get current positions from orchestrator
                positions_data = self.orchestrator.get_shared_data('current_positions')
                
                if positions_data:
                    # Monitor each position
                    for position in positions_data:
                        await self._monitor_position_risk(position)
                    
                    # Monitor portfolio risk
                    await self._monitor_portfolio_risk(positions_data)
                
                await asyncio.sleep(self.risk_monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"Error in risk monitoring loop: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_position_risk(self, position: Dict[str, Any]):
        """Monitor individual position risk"""
        try:
            # Get market data for position
            symbol = position['symbol']
            market_data = self.orchestrator.get_shared_data(f'market_data_{symbol}')
            
            # Calculate risk metrics
            risk_metrics = await self._calculate_position_risk_metrics(position, market_data or {})
            
            # Check if risk level has changed
            risk_level = self._determine_risk_level(risk_metrics['risk_score'])
            
            # Generate alert if risk is high
            if risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                # Check if we already have an active alert for this position
                existing_alert = any(
                    alert.alert_type == AlertType.POSITION_RISK and 
                    symbol in alert.affected_positions and 
                    not alert.resolved
                    for alert in self.active_alerts.values()
                )
                
                if not existing_alert:
                    assessment = RiskAssessment(
                        assessment_id=f"monitor_{symbol}_{int(time.time())}",
                        symbol=symbol,
                        risk_level=risk_level,
                        risk_score=risk_metrics['risk_score'],
                        risk_factors=risk_metrics['risk_factors'],
                        recommendations=risk_metrics['recommendations'],
                        confidence=risk_metrics['confidence'],
                        timestamp=datetime.now(),
                        validity_period=timedelta(minutes=30)
                    )
                    
                    await self._generate_risk_alert(assessment)
            
        except Exception as e:
            self.logger.error(f"Position risk monitoring failed for {position.get('symbol', 'unknown')}: {e}")
    
    async def _monitor_portfolio_risk(self, positions: List[Dict[str, Any]]):
        """Monitor overall portfolio risk"""
        try:
            # Calculate portfolio risk
            market_data = {}  # Collect market data for all positions
            
            portfolio_risk = await self._calculate_portfolio_risk(positions, market_data)
            self.portfolio_risk = portfolio_risk
            
            # Check portfolio risk limits
            violations = await self._check_portfolio_risk_limits(portfolio_risk)
            
            if violations:
                # Generate portfolio risk alert
                alert = RiskAlert(
                    alert_id=f"portfolio_risk_{int(time.time())}",
                    alert_type=AlertType.PORTFOLIO_RISK,
                    risk_level=RiskLevel.HIGH,
                    message=f"Portfolio risk violations: {', '.join(violations)}",
                    affected_positions=[pos['symbol'] for pos in positions],
                    recommended_actions=["Review position sizes", "Reduce exposure", "Improve diversification"],
                    timestamp=datetime.now()
                )
                
                # Check if we already have an active portfolio alert
                existing_alert = any(
                    alert.alert_type == AlertType.PORTFOLIO_RISK and not alert.resolved
                    for alert in self.active_alerts.values()
                )
                
                if not existing_alert:
                    self.active_alerts[alert.alert_id] = alert
                    self.metrics['alerts_generated'] += 1
                    
                    # Send alert to orchestrator
                    await self.orchestrator.send_message(
                        sender=self.agent_id,
                        recipient='orchestrator',
                        message_type='alert',
                        data={
                            'type': alert.alert_type.value,
                            'severity': alert.risk_level.value,
                            'details': alert.message,
                            'violations': violations
                        }
                    )
            
        except Exception as e:
            self.logger.error(f"Portfolio risk monitoring failed: {e}")
    
    async def _portfolio_risk_loop(self):
        """Portfolio risk calculation loop"""
        while self.is_running:
            try:
                # Update risk metrics average
                if self.risk_assessments:
                    total_score = sum(assessment.risk_score for assessment in self.risk_assessments.values())
                    self.metrics['risk_score_average'] = total_score / len(self.risk_assessments)
                
                # Store risk history
                if self.portfolio_risk:
                    history_entry = {
                        'timestamp': datetime.now().isoformat(),
                        'total_exposure': self.portfolio_risk.total_exposure,
                        'diversification_score': self.portfolio_risk.diversification_score,
                        'var_1d': self.portfolio_risk.var_1d,
                        'drawdown_risk': self.portfolio_risk.drawdown_risk
                    }
                    
                    self.risk_history.append(history_entry)
                    
                    # Keep only last 1000 entries
                    if len(self.risk_history) > 1000:
                        self.risk_history = self.risk_history[-1000:]
                
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in portfolio risk loop: {e}")
                await asyncio.sleep(60)
    
    async def _alert_management_loop(self):
        """Alert management and cleanup loop"""
        while self.is_running:
            try:
                current_time = datetime.now()
                resolved_alerts = []
                
                # Check alert resolution and cleanup
                for alert_id, alert in self.active_alerts.items():
                    # Auto-resolve old alerts
                    if current_time - alert.timestamp > timedelta(hours=2):
                        alert.resolved = True
                        resolved_alerts.append(alert_id)
                
                # Remove resolved alerts
                for alert_id in resolved_alerts:
                    del self.active_alerts[alert_id]
                
                await asyncio.sleep(600)  # Check every 10 minutes
                
            except Exception as e:
                self.logger.error(f"Error in alert management loop: {e}")
                await asyncio.sleep(60)
    
    async def _emergency_monitoring_loop(self):
        """Emergency situation monitoring loop"""
        while self.is_running:
            try:
                # Check for emergency conditions
                if self.portfolio_risk:
                    # Emergency conditions
                    emergency_conditions = [
                        self.portfolio_risk.drawdown_risk > 0.15,  # 15% drawdown
                        self.portfolio_risk.var_1d > await self._get_portfolio_value() * 0.05,  # 5% VaR
                        len([alert for alert in self.active_alerts.values() 
                            if alert.risk_level == RiskLevel.CRITICAL]) > 2  # Multiple critical alerts
                    ]
                    
                    if any(emergency_conditions) and not self.emergency_mode:
                        await self._trigger_emergency_protocol()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in emergency monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _trigger_emergency_protocol(self):
        """Trigger emergency protocol"""
        try:
            self.emergency_mode = True
            self.metrics['emergency_stops'] += 1
            
            # Send emergency alert to orchestrator
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='orchestrator',
                message_type='alert',
                data={
                    'type': 'system_failure',
                    'severity': 'critical',
                    'details': 'Emergency risk conditions detected - activating emergency protocol',
                    'action_required': 'immediate_attention'
                }
            )
            
            self.logger.critical("Emergency protocol activated due to extreme risk conditions")
            
        except Exception as e:
            self.logger.error(f"Emergency protocol activation failed: {e}")
    
    def get_capabilities(self) -> List[str]:
        """Get agent capabilities"""
        return [
            'assess_position_risk',
            'assess_portfolio_risk',
            'check_risk_limits',
            'calculate_var',
            'stress_test',
            'emergency_stop',
            'get_risk_report',
            'update_risk_limits',
            'correlation_analysis',
            'liquidity_assessment',
            'drawdown_analysis'
        ]
    
    async def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            'agent_id': self.agent_id,
            'status': 'active' if self.is_running else 'inactive',
            'emergency_mode': self.emergency_mode,
            'active_alerts': len(self.active_alerts),
            'risk_assessments': len(self.risk_assessments),
            'portfolio_risk_level': self.portfolio_risk.drawdown_risk if self.portfolio_risk else 0.0
        }
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        # Calculate alert accuracy if we have historical data
        if self.metrics['alerts_generated'] > 0:
            # This would require tracking alert outcomes
            # For now, use a placeholder
            self.metrics['alert_accuracy'] = 0.85
        
        return self.metrics.copy()
    
    async def shutdown(self):
        """Shutdown the risk agent"""
        self.logger.info(f"Shutting down Risk Agent {self.agent_id}")
        
        self.is_running = False
        self.emergency_mode = False
        
        # Close all active alerts
        for alert in self.active_alerts.values():
            alert.resolved = True
        
        # Cleanup
        if self.risk_manager:
            await self.risk_manager.shutdown()
        
        self.logger.info(f"Risk Agent {self.agent_id} shutdown complete")
