#!/usr/bin/env python3
"""
Tier 1 Integration Manager
Coordinates Online Learning, Experience Replay, and Elastic Weight Consolidation
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timezone

from .tier1_online_learning_optimizer import Tier1OnlineLearningOptimizer, TradeOutcome
from .tier1_experience_replay_buffer import Tier1ExperienceReplayBuffer, TradeExperience
from .tier1_elastic_weight_consolidation import Tier1ElasticWeightConsolidation
from .enhanced_multi_timeframe_learner import EnhancedMultiTimeframeLearner, TimeframeType

logger = logging.getLogger(__name__)

@dataclass
class Tier1LearningResult:
    """Result from Tier 1 learning process"""
    optimized_parameters: Dict[str, float]
    experience_replay_insights: Dict[str, Any]
    consolidation_applied: bool
    performance_improvement: float
    learning_confidence: float

class Tier1IntegrationManager:
    """
    Integrates Tier 1 optimizations with existing enhanced learning systems
    Coordinates Online Learning, Experience Replay, and EWC while preserving
    all existing multi-timeframe learning capabilities
    """
    
    def __init__(self, config: Any, db_path: str = "bybit_trading_bot.db"):
        self.config = config
        self.db_path = db_path
        
        # Tier 1 components
        self.online_learning_optimizer = Tier1OnlineLearningOptimizer(config, db_path)
        self.experience_replay_buffer = Tier1ExperienceReplayBuffer(config, db_path)
        self.elastic_weight_consolidation = Tier1ElasticWeightConsolidation(config, db_path)
        
        # Enhanced learning system integration
        self.enhanced_multi_timeframe_learner: Optional[EnhancedMultiTimeframeLearner] = None
        
        # Performance tracking
        self.tier1_activations = 0
        self.total_performance_improvement = 0.0
        self.learning_sessions = 0
        self.integration_success_rate = 0.0
        
        # Learning coordination
        self.learning_queue = asyncio.Queue()
        self.is_learning_active = False
        
        logger.info("Tier 1 Integration Manager initialized")
    
    async def initialize(self):
        """Initialize all Tier 1 components"""
        try:
            # Initialize Tier 1 components
            online_success = await self.online_learning_optimizer.initialize()
            replay_success = await self.experience_replay_buffer.initialize()
            ewc_success = await self.elastic_weight_consolidation.initialize()
            
            if not all([online_success, replay_success, ewc_success]):
                logger.error("Failed to initialize some Tier 1 components")
                return False
            
            # Start background learning process
            asyncio.create_task(self._background_learning_process())
            
            logger.info("Tier 1 Integration Manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Tier 1 Integration Manager: {e}")
            return False
    
    def set_enhanced_multi_timeframe_learner(self, learner: EnhancedMultiTimeframeLearner):
        """Set the enhanced multi-timeframe learner for integration"""
        self.enhanced_multi_timeframe_learner = learner
        logger.info("Enhanced multi-timeframe learner integrated with Tier 1 system")
    
    async def process_trade_outcome(self, trade_data: Dict[str, Any]) -> Tier1LearningResult:
        """
        Process trade outcome through Tier 1 learning pipeline
        Integrates with existing enhanced learning systems
        """
        try:
            # Convert trade data to standardized format
            trade_outcome = self._convert_to_trade_outcome(trade_data)
            trade_experience = self._convert_to_trade_experience(trade_data)
            
            # 1. Online Learning Optimization
            optimized_parameters = await self.online_learning_optimizer.learn_from_trade_outcome(trade_outcome)
            
            # 2. Add to Experience Replay Buffer
            await self.experience_replay_buffer.add_experience(trade_experience)
            
            # 3. Apply Elastic Weight Consolidation if strategy is profitable
            consolidation_applied = False
            if trade_outcome.profit_loss > 0:
                strategy_name = trade_data.get('strategy', 'default')
                performance_score = self._calculate_performance_score(trade_outcome)
                
                if performance_score > 0.7:  # High performance threshold
                    consolidation_applied = await self.elastic_weight_consolidation.consolidate_strategy(
                        strategy_name, optimized_parameters, performance_score, 1
                    )
            
            # 4. Experience Replay Learning (periodic)
            replay_insights = await self._perform_experience_replay_learning(trade_outcome.symbol)
            
            # 5. Integrate with Enhanced Multi-Timeframe Learning
            if self.enhanced_multi_timeframe_learner:
                await self._integrate_with_enhanced_learning(trade_data, optimized_parameters)
            
            # Calculate performance improvement
            performance_improvement = self._calculate_performance_improvement(trade_outcome, optimized_parameters)
            learning_confidence = self._calculate_learning_confidence(trade_outcome, replay_insights)
            
            # Update statistics
            self.tier1_activations += 1
            self.total_performance_improvement += performance_improvement
            self.learning_sessions += 1
            
            result = Tier1LearningResult(
                optimized_parameters=optimized_parameters,
                experience_replay_insights=replay_insights,
                consolidation_applied=consolidation_applied,
                performance_improvement=performance_improvement,
                learning_confidence=learning_confidence
            )
            
            logger.info(f"Tier 1 learning completed: improvement={performance_improvement:.2f}%, confidence={learning_confidence:.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process trade outcome through Tier 1 pipeline: {e}")
            return Tier1LearningResult(
                optimized_parameters={},
                experience_replay_insights={},
                consolidation_applied=False,
                performance_improvement=0.0,
                learning_confidence=0.0
            )
    
    def _convert_to_trade_outcome(self, trade_data: Dict[str, Any]) -> TradeOutcome:
        """Convert trade data to TradeOutcome format"""
        return TradeOutcome(
            symbol=trade_data.get('symbol', 'UNKNOWN'),
            side=trade_data.get('side', 'buy'),
            entry_price=float(trade_data.get('price', 0)),
            exit_price=float(trade_data.get('exit_price', trade_data.get('price', 0))),
            quantity=float(trade_data.get('quantity', 0)),
            profit_loss=float(trade_data.get('profit_loss', 0)),
            timestamp=datetime.now(timezone.utc),
            market_conditions=trade_data.get('market_conditions', {}),
            strategy_parameters=trade_data.get('strategy_parameters', {})
        )
    
    def _convert_to_trade_experience(self, trade_data: Dict[str, Any]) -> TradeExperience:
        """Convert trade data to TradeExperience format"""
        profit_loss = float(trade_data.get('profit_loss', 0))
        entry_price = float(trade_data.get('price', 1))
        profit_pct = (profit_loss / (entry_price * float(trade_data.get('quantity', 1)))) * 100
        
        return TradeExperience(
            trade_id=f"{trade_data.get('symbol', 'UNKNOWN')}_{int(time.time())}",
            symbol=trade_data.get('symbol', 'UNKNOWN'),
            side=trade_data.get('side', 'buy'),
            entry_price=entry_price,
            exit_price=float(trade_data.get('exit_price', entry_price)),
            quantity=float(trade_data.get('quantity', 0)),
            profit_loss=profit_loss,
            profit_pct=profit_pct,
            timestamp=datetime.now(timezone.utc),
            market_conditions=trade_data.get('market_conditions', {}),
            strategy_parameters=trade_data.get('strategy_parameters', {}),
            environmental_context=trade_data.get('environmental_context', {}),
            success_score=0.0,  # Will be calculated by buffer
            priority=0.0        # Will be calculated by buffer
        )
    
    async def _perform_experience_replay_learning(self, symbol: str) -> Dict[str, Any]:
        """Perform experience replay learning for continuous improvement"""
        try:
            # Sample batch of experiences
            batch = await self.experience_replay_buffer.sample_batch(batch_size=16, symbol=symbol)
            
            if batch.batch_size == 0:
                return {'status': 'no_experiences', 'insights': {}}
            
            # Analyze successful patterns
            successful_experiences = [exp for exp in batch.experiences if exp.success_score > 0.7]
            
            insights = {
                'status': 'completed',
                'batch_size': batch.batch_size,
                'successful_count': len(successful_experiences),
                'avg_success_score': batch.avg_success_score,
                'total_profit': batch.total_profit,
                'insights': {}
            }
            
            if successful_experiences:
                # Extract common patterns from successful trades
                insights['insights'] = self._extract_success_patterns(successful_experiences)
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to perform experience replay learning: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _extract_success_patterns(self, successful_experiences: List[TradeExperience]) -> Dict[str, Any]:
        """Extract common patterns from successful trading experiences"""
        patterns = {
            'optimal_position_sizes': [],
            'profitable_market_conditions': [],
            'successful_strategies': [],
            'timing_patterns': []
        }
        
        for exp in successful_experiences:
            # Position size patterns
            position_value = exp.entry_price * exp.quantity
            patterns['optimal_position_sizes'].append(position_value)
            
            # Market condition patterns
            if exp.market_conditions:
                patterns['profitable_market_conditions'].append(exp.market_conditions)
            
            # Strategy patterns
            if exp.strategy_parameters:
                patterns['successful_strategies'].append(exp.strategy_parameters)
            
            # Timing patterns (hour of day, day of week)
            patterns['timing_patterns'].append({
                'hour': exp.timestamp.hour,
                'day_of_week': exp.timestamp.weekday()
            })
        
        return patterns
    
    async def _integrate_with_enhanced_learning(self, trade_data: Dict[str, Any], optimized_parameters: Dict[str, float]):
        """Integrate Tier 1 results with enhanced multi-timeframe learning"""
        try:
            if not self.enhanced_multi_timeframe_learner:
                return
            
            # Create environmental context for enhanced learning
            environmental_context = trade_data.get('environmental_context', {})
            
            # Learn from trade with Tier 1 optimized parameters
            await self.enhanced_multi_timeframe_learner.learn_from_trade_with_environment(
                symbol=trade_data.get('symbol', 'UNKNOWN'),
                profit_loss=float(trade_data.get('profit_loss', 0)),
                environmental_context=environmental_context,
                strategy_parameters=optimized_parameters
            )
            
            # Learn from timeframe P&L if available
            for timeframe in [TimeframeType.HOURLY, TimeframeType.FOUR_HOUR, TimeframeType.EIGHT_HOUR, TimeframeType.DAILY]:
                timeframe_pnl = trade_data.get(f'{timeframe.value}_pnl')
                if timeframe_pnl is not None:
                    await self.enhanced_multi_timeframe_learner.learn_from_timeframe_pnl(
                        timeframe=timeframe,
                        pnl=float(timeframe_pnl),
                        environmental_context=environmental_context
                    )
            
            logger.debug("Successfully integrated Tier 1 results with enhanced learning")
            
        except Exception as e:
            logger.error(f"Failed to integrate with enhanced learning: {e}")
    
    def _calculate_performance_score(self, trade_outcome: TradeOutcome) -> float:
        """Calculate performance score for consolidation decision"""
        # Normalize profit by position size
        position_value = trade_outcome.entry_price * trade_outcome.quantity
        normalized_profit = trade_outcome.profit_loss / position_value
        
        # Convert to 0-1 score
        score = max(0.0, min(1.0, (normalized_profit + 0.1) / 0.2))  # -10% to +10% maps to 0-1
        
        return score
    
    def _calculate_performance_improvement(self, trade_outcome: TradeOutcome, optimized_parameters: Dict[str, float]) -> float:
        """Calculate estimated performance improvement from optimization"""
        # Simple heuristic based on parameter adjustments and trade outcome
        if trade_outcome.profit_loss > 0:
            # Profitable trade - estimate improvement based on parameter optimization
            return min(5.0, abs(trade_outcome.profit_loss) * 0.1)  # Max 5% improvement
        else:
            # Losing trade - estimate loss reduction
            return min(2.0, abs(trade_outcome.profit_loss) * 0.05)  # Max 2% loss reduction
    
    def _calculate_learning_confidence(self, trade_outcome: TradeOutcome, replay_insights: Dict[str, Any]) -> float:
        """Calculate confidence in learning results"""
        confidence = 0.5  # Base confidence
        
        # Increase confidence based on trade outcome
        if trade_outcome.profit_loss > 0:
            confidence += 0.2
        
        # Increase confidence based on replay insights
        if replay_insights.get('status') == 'completed':
            batch_size = replay_insights.get('batch_size', 0)
            if batch_size > 10:
                confidence += 0.2
            
            avg_success = replay_insights.get('avg_success_score', 0)
            confidence += avg_success * 0.1
        
        return min(1.0, confidence)
    
    async def _background_learning_process(self):
        """Background process for continuous learning and optimization"""
        self.is_learning_active = True
        
        while self.is_learning_active:
            try:
                # Periodic experience replay learning
                await asyncio.sleep(300)  # Every 5 minutes
                
                # Get buffer statistics
                buffer_stats = await self.experience_replay_buffer.get_buffer_stats()
                
                if buffer_stats['total_experiences'] > 50:
                    # Perform batch learning from best experiences
                    best_experiences = await self.experience_replay_buffer.get_best_experiences(count=20)
                    
                    if best_experiences:
                        logger.info(f"Background learning from {len(best_experiences)} best experiences")
                        # Additional learning logic can be added here
                
            except Exception as e:
                logger.error(f"Background learning process error: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def get_tier1_stats(self) -> Dict[str, Any]:
        """Get comprehensive Tier 1 statistics"""
        online_stats = await self.online_learning_optimizer.get_performance_stats()
        buffer_stats = await self.experience_replay_buffer.get_buffer_stats()
        ewc_stats = await self.elastic_weight_consolidation.get_consolidation_stats()
        
        return {
            'tier1_activations': self.tier1_activations,
            'total_performance_improvement': self.total_performance_improvement,
            'avg_performance_improvement': self.total_performance_improvement / max(self.learning_sessions, 1),
            'learning_sessions': self.learning_sessions,
            'online_learning': online_stats,
            'experience_replay': buffer_stats,
            'elastic_weight_consolidation': ewc_stats,
            'integration_active': self.enhanced_multi_timeframe_learner is not None
        }
    
    async def shutdown(self):
        """Shutdown Tier 1 integration manager"""
        self.is_learning_active = False
        logger.info("Tier 1 Integration Manager shutdown completed")
