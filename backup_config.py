#!/usr/bin/env python3
"""
Backup Configuration and Management System
Implements a 10-backup limit to prevent workspace clutter
"""

import os
import re
import glob
from pathlib import Path
from typing import List, Dict, Tuple
import time

# BACKUP CONFIGURATION
MAX_BACKUPS = 10
BACKUP_PATTERN = r'(.+)\.backup_(\d+)$'
AUTO_CLEANUP = True
CLEANUP_FREQUENCY_DAYS = 7

class BackupManager:
    """Manages backup files with automatic cleanup"""
    
    def __init__(self, max_backups: int = MAX_BACKUPS):
        self.max_backups = max_backups
        self.backup_pattern = re.compile(BACKUP_PATTERN)
        
    def create_backup(self, file_path: str) -> str:
        """Create a backup file with automatic cleanup"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Generate timestamp
        timestamp = int(time.time())
        backup_path = f"{file_path}.backup_{timestamp}"
        
        # Create the backup
        import shutil
        shutil.copy2(file_path, backup_path)
        
        # Clean up old backups
        self.cleanup_old_backups(file_path)
        
        return backup_path
    
    def cleanup_old_backups(self, original_file: str) -> int:
        """Clean up old backups for a specific file"""
        # Find all backups for this file
        backup_files = []
        pattern = f"{original_file}.backup_*"
        
        for backup_file in glob.glob(pattern):
            match = self.backup_pattern.match(backup_file)
            if match:
                timestamp = int(match.group(2))
                backup_files.append((backup_file, timestamp))
        
        # Sort by timestamp (newest first)
        backup_files.sort(key=lambda x: x[1], reverse=True)
        
        # Remove old backups beyond the limit
        removed_count = 0
        for i, (backup_file, timestamp) in enumerate(backup_files):
            if i >= self.max_backups:
                try:
                    os.remove(backup_file)
                    removed_count += 1
                except Exception as e:
                    print(f"Warning: Could not remove backup {backup_file}: {e}")
        
        return removed_count
    
    def cleanup_all_backups(self, directory: str = ".") -> Dict[str, int]:
        """Clean up all backup files in directory systematically"""
        backup_groups = {}

        # Enhanced backup patterns to catch all variations
        backup_patterns = [
            re.compile(r'(.+)\.backup_(\d+)$'),  # Standard pattern
            re.compile(r'(.+)_backup_(\d+)$'),   # Underscore pattern
            re.compile(r'(.+)\.bak_(\d+)$'),     # .bak pattern
            re.compile(r'(.+)_(\d{10,})\.py$'),  # Timestamp pattern
        ]

        # Find all backup files with multiple patterns
        for root, dirs, files in os.walk(directory):
            for file in files:
                for pattern in backup_patterns:
                    match = pattern.match(file)
                    if match:
                        original_name = match.group(1)
                        timestamp = int(match.group(2))
                        full_path = os.path.join(root, file)

                        # Create unique key for grouping
                        group_key = f"{root}/{original_name}"

                        if group_key not in backup_groups:
                            backup_groups[group_key] = []

                        backup_groups[group_key].append((full_path, timestamp))
                        break  # Only match first pattern

        total_removed = 0
        total_kept = 0

        for group_key, backups in backup_groups.items():
            # Sort by timestamp (newest first)
            backups.sort(key=lambda x: x[1], reverse=True)

            # Keep only the newest max_backups
            for i, (backup_path, timestamp) in enumerate(backups):
                if i < self.max_backups:
                    total_kept += 1
                else:
                    try:
                        os.remove(backup_path)
                        total_removed += 1
                        print(f"Removed old backup: {backup_path}")
                    except Exception as e:
                        print(f"Warning: Could not remove {backup_path}: {e}")

        return {
            "removed": total_removed,
            "kept": total_kept,
            "total": total_removed + total_kept,
            "groups": len(backup_groups)
        }

# Global backup manager instance
backup_manager = BackupManager(MAX_BACKUPS)

def create_backup(file_path: str) -> str:
    """Create a backup with automatic cleanup"""
    return backup_manager.create_backup(file_path)

def cleanup_backups(directory: str = ".") -> Dict[str, int]:
    """Clean up all backups in directory"""
    return backup_manager.cleanup_all_backups(directory)

def get_backup_info() -> Dict[str, any]:
    """Get backup configuration info"""
    return {
        "max_backups": MAX_BACKUPS,
        "auto_cleanup": AUTO_CLEANUP,
        "cleanup_frequency_days": CLEANUP_FREQUENCY_DAYS,
        "backup_pattern": BACKUP_PATTERN
    }

if __name__ == "__main__":
    print("BACKUP MANAGEMENT SYSTEM")
    print("=" * 40)
    print(f"Maximum backups per file: {MAX_BACKUPS}")
    print(f"Auto cleanup enabled: {AUTO_CLEANUP}")
    print(f"Cleanup frequency: {CLEANUP_FREQUENCY_DAYS} days")
    print("=" * 40)
    
    # Run cleanup
    results = cleanup_backups(".")
    
    print(f"\nCleanup Results:")
    print(f"  Total backup files: {results['total']}")
    print(f"  Files kept: {results['kept']}")
    print(f"  Files removed: {results['removed']}")
    
    if results['removed'] > 0:
        print(f"\n✅ Successfully cleaned up {results['removed']} old backup files!")
    else:
        print(f"\n✅ All backup files are within the {MAX_BACKUPS}-backup limit!")
