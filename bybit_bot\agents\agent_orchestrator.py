"""
Agent Orchestrator - Central coordinator for multi-agent trading system
Manages communication, task distribution, and coordination between specialized agents
"""
import asyncio
import time
import statistics
import threading
import multiprocessing
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple, Callable, Deque
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, ProcessPoolExecutor
from collections import deque
import heapq
import json

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager
from ..ai.memory_manager import PersistentMemoryManager
from .trading_agent import TradingAgent
from .research_agent import ResearchAgent
from .risk_agent import RiskAgent
from .learning_agent import LearningAgent


class AgentStatus(Enum):
    """Agent status states"""
    IDLE = "idle"
    BUSY = "busy" 
    ERROR = "error"
    OFFLINE = "offline"
    INITIALIZING = "initializing"


class TaskPriority(Enum):
    """Task priority levels"""
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4


class MessageType(Enum):
    """Inter-agent message types"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    DATA_SHARE = "data_share"
    ALERT = "alert"
    STATUS_UPDATE = "status_update"
    COORDINATION = "coordination"


@dataclass
class AgentTask:
    """Task structure for agent coordination"""
    task_id: str
    agent_type: str
    task_type: str
    priority: TaskPriority
    data: Dict[str, Any]
    created_at: datetime
    deadline: Optional[datetime] = None
    dependencies: List[str] = field(default_factory=list)  # Ensuring type is fully defined
    callback: Optional[str] = None


@dataclass
class AgentMessage:
    """Message structure for inter-agent communication"""
    message_id: str
    sender: str
    recipient: str
    message_type: MessageType
    data: Dict[str, Any]
    timestamp: datetime
    priority: TaskPriority = TaskPriority.MEDIUM


@dataclass
class AgentInfo:
    """Agent information structure"""
    agent_id: str
    agent_type: str
    status: AgentStatus
    capabilities: List[str]
    current_tasks: List[str]
    performance_metrics: Dict[str, float]
    last_heartbeat: datetime


class SchedulingStrategy(Enum):
    """Task scheduling strategies for CPU optimization"""
    ROUND_ROBIN = "round_robin"
    LOAD_BALANCED = "load_balanced"
    PRIORITY_BASED = "priority_based"
    PERFORMANCE_WEIGHTED = "performance_weighted"
    CPU_OPTIMIZED = "cpu_optimized"


class LoadBalancingMode(Enum):
    """Load balancing modes for agent coordination"""
    STATIC = "static"
    DYNAMIC = "dynamic"
    ADAPTIVE = "adaptive"
    PREDICTIVE = "predictive"


class CPUEfficientTaskScheduler:
    """CPU-efficient task scheduling engine"""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=max_workers)
        self.task_priority_queue = []  # Min-heap for priority scheduling
        self.scheduling_stats = {
            'tasks_scheduled': 0,
            'avg_scheduling_time': 0.0,
            'cpu_efficiency': 0.0,
            'load_balance_score': 0.0
        }

    async def schedule_task_optimized(self, task: Any, agent_loads: Dict[str, int],
                                    agent_performance: Dict[str, float]) -> Optional[str]:
        """Schedule task with CPU optimization"""
        start_time = time.time()

        try:
            # Calculate optimal agent assignment
            optimal_agent = await self._calculate_optimal_assignment(task, agent_loads, agent_performance)

            # Update scheduling stats
            scheduling_time = time.time() - start_time
            self._update_scheduling_stats(scheduling_time)

            return optimal_agent

        except Exception as e:
            return None

    async def _calculate_optimal_assignment(self, task: Any, agent_loads: Dict[str, int],
                                          agent_performance: Dict[str, float]) -> Optional[str]:
        """Calculate optimal agent assignment using CPU-efficient algorithms"""
        try:
            # Get task requirements
            task_type = getattr(task, 'task_type', 'general')
            task_priority = getattr(task, 'priority', 3)

            # Calculate agent scores
            agent_scores = {}
            for agent_type in agent_loads.keys():
                # Load factor (lower is better)
                load_factor = 1.0 / (agent_loads[agent_type] + 1)

                # Performance factor (higher is better)
                performance_factor = agent_performance.get(agent_type, 0.5)

                # Task compatibility factor
                compatibility_factor = self._calculate_task_compatibility(task_type, agent_type)

                # Combined score
                agent_scores[agent_type] = (
                    load_factor * 0.4 +
                    performance_factor * 0.4 +
                    compatibility_factor * 0.2
                )

            # Return agent with highest score
            if agent_scores:
                return max(agent_scores.keys(), key=lambda x: agent_scores[x])

            return None

        except Exception as e:
            return None

    def _calculate_task_compatibility(self, task_type: str, agent_type: str) -> float:
        """Calculate task-agent compatibility score"""
        compatibility_matrix = {
            'trading': {'trading': 1.0, 'risk': 0.8, 'research': 0.3, 'learning': 0.5},
            'analysis': {'research': 1.0, 'learning': 0.9, 'trading': 0.4, 'risk': 0.6},
            'risk_assessment': {'risk': 1.0, 'trading': 0.7, 'learning': 0.6, 'research': 0.3},
            'learning': {'learning': 1.0, 'research': 0.7, 'trading': 0.5, 'risk': 0.4}
        }

        return compatibility_matrix.get(task_type, {}).get(agent_type, 0.5)

    def _update_scheduling_stats(self, scheduling_time: float):
        """Update scheduling statistics"""
        self.scheduling_stats['tasks_scheduled'] += 1
        current_avg = self.scheduling_stats['avg_scheduling_time']
        total_tasks = self.scheduling_stats['tasks_scheduled']

        if total_tasks > 0:
            self.scheduling_stats['avg_scheduling_time'] = (
                (current_avg * (total_tasks - 1) + scheduling_time) / total_tasks
            )

        # Update CPU efficiency (inverse of scheduling time)
        self.scheduling_stats['cpu_efficiency'] = min(1.0, 1.0 / (scheduling_time + 0.001))


class IntelligentLoadBalancer:
    """Intelligent load balancing for agent coordination"""

    def __init__(self):
        self.load_history: Dict[str, Deque[float]] = {}
        self.performance_history: Dict[str, Deque[float]] = {}
        self.balancing_mode = LoadBalancingMode.ADAPTIVE
        self.balancing_stats = {
            'rebalancing_cycles': 0,
            'load_variance_reduction': 0.0,
            'performance_improvement': 0.0
        }

    async def balance_load_intelligent(self, agent_loads: Dict[str, int],
                                     agent_performance: Dict[str, float]) -> Dict[str, Any]:
        """Perform intelligent load balancing"""
        try:
            # Update load history
            self._update_load_history(agent_loads)
            self._update_performance_history(agent_performance)

            # Calculate load variance
            load_variance = self._calculate_load_variance(agent_loads)

            # Determine if rebalancing is needed
            rebalancing_needed = load_variance > 0.3  # Threshold for rebalancing

            if rebalancing_needed:
                # Calculate optimal load distribution
                optimal_distribution = await self._calculate_optimal_distribution(agent_loads, agent_performance)

                # Generate rebalancing recommendations
                recommendations = self._generate_rebalancing_recommendations(agent_loads, optimal_distribution)

                self.balancing_stats['rebalancing_cycles'] += 1

                return {
                    'rebalancing_needed': True,
                    'recommendations': recommendations,
                    'load_variance': load_variance,
                    'optimal_distribution': optimal_distribution
                }

            return {
                'rebalancing_needed': False,
                'load_variance': load_variance,
                'status': 'balanced'
            }

        except Exception as e:
            return {'error': str(e), 'rebalancing_needed': False}

    def _update_load_history(self, agent_loads: Dict[str, int]):
        """Update load history for trend analysis"""
        for agent_type, load in agent_loads.items():
            if agent_type not in self.load_history:
                self.load_history[agent_type] = deque(maxlen=50)
            self.load_history[agent_type].append(float(load))

    def _update_performance_history(self, agent_performance: Dict[str, float]):
        """Update performance history for trend analysis"""
        for agent_type, performance in agent_performance.items():
            if agent_type not in self.performance_history:
                self.performance_history[agent_type] = deque(maxlen=50)
            self.performance_history[agent_type].append(performance)

    def _calculate_load_variance(self, agent_loads: Dict[str, int]) -> float:
        """Calculate load variance across agents"""
        if not agent_loads:
            return 0.0

        loads = list(agent_loads.values())
        if len(loads) <= 1:
            return 0.0

        mean_load = statistics.mean(loads)
        variance = statistics.variance(loads, mean_load)

        # Normalize variance
        return min(1.0, variance / (mean_load + 1))

    async def _calculate_optimal_distribution(self, agent_loads: Dict[str, int],
                                            agent_performance: Dict[str, float]) -> Dict[str, float]:
        """Calculate optimal load distribution"""
        try:
            total_load = sum(agent_loads.values())
            if total_load == 0:
                return {agent: 0.0 for agent in agent_loads.keys()}

            # Weight by performance (higher performance agents get more load)
            performance_weights = {}
            total_performance = sum(agent_performance.values())

            for agent_type in agent_loads.keys():
                performance = agent_performance.get(agent_type, 0.5)
                performance_weights[agent_type] = performance / total_performance if total_performance > 0 else 1.0 / len(agent_loads)

            # Calculate optimal distribution
            optimal_distribution = {}
            for agent_type, weight in performance_weights.items():
                optimal_distribution[agent_type] = total_load * weight

            return optimal_distribution

        except Exception as e:
            return {agent: float(load) for agent, load in agent_loads.items()}

    def _generate_rebalancing_recommendations(self, current_loads: Dict[str, int],
                                            optimal_loads: Dict[str, float]) -> List[Dict[str, Any]]:
        """Generate specific rebalancing recommendations"""
        recommendations = []

        for agent_type in current_loads.keys():
            current = current_loads[agent_type]
            optimal = optimal_loads.get(agent_type, current)
            difference = optimal - current

            if abs(difference) > 1:  # Significant difference
                recommendations.append({
                    'agent_type': agent_type,
                    'current_load': current,
                    'optimal_load': optimal,
                    'adjustment': difference,
                    'action': 'increase_load' if difference > 0 else 'decrease_load'
                })

        return recommendations


class MemoryEfficientMessageQueue:
    """Memory-efficient message queue with TTL and compression"""

    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.message_queue: Deque[Dict[str, Any]] = deque(maxlen=max_size)
        self.message_index: Dict[str, int] = {}
        self.queue_stats = {
            'messages_processed': 0,
            'messages_expired': 0,
            'memory_usage_mb': 0.0,
            'compression_ratio': 0.0
        }

    async def enqueue_message_optimized(self, message: Any) -> bool:
        """Enqueue message with memory optimization"""
        try:
            # Convert message to optimized format
            optimized_message = await self._optimize_message(message)

            # Add timestamp for TTL
            optimized_message['enqueued_at'] = time.time()

            # Add to queue
            self.message_queue.append(optimized_message)

            # Update index
            message_id = optimized_message.get('message_id', str(time.time()))
            self.message_index[message_id] = len(self.message_queue) - 1

            # Clean expired messages
            await self._clean_expired_messages()

            return True

        except Exception as e:
            return False

    async def dequeue_message_optimized(self) -> Optional[Dict[str, Any]]:
        """Dequeue message with optimization"""
        try:
            if not self.message_queue:
                return None

            # Get oldest message
            message = self.message_queue.popleft()

            # Check if expired
            if self._is_message_expired(message):
                self.queue_stats['messages_expired'] += 1
                return await self.dequeue_message_optimized()  # Try next message

            # Decompress if needed
            decompressed_message = await self._decompress_message(message)

            self.queue_stats['messages_processed'] += 1
            return decompressed_message

        except Exception as e:
            return None

    async def _optimize_message(self, message: Any) -> Dict[str, Any]:
        """Optimize message for storage"""
        try:
            # Convert to dict if needed
            if hasattr(message, '__dict__'):
                message_dict = message.__dict__
            else:
                message_dict = dict(message) if isinstance(message, dict) else {'data': message}

            # Compress large messages
            message_str = json.dumps(message_dict)
            if len(message_str) > 1000:  # Compress messages larger than 1KB
                compressed_data = self._compress_data(message_str)
                return {
                    'compressed': True,
                    'data': compressed_data,
                    'original_size': len(message_str)
                }
            else:
                return {
                    'compressed': False,
                    'data': message_dict,
                    'original_size': len(message_str)
                }

        except Exception as e:
            return {'error': str(e), 'compressed': False}

    def _compress_data(self, data: str) -> bytes:
        """Compress data for storage"""
        try:
            import gzip
            return gzip.compress(data.encode('utf-8'))
        except:
            return data.encode('utf-8')

    async def _decompress_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Decompress message if needed"""
        try:
            if message.get('compressed', False):
                compressed_data = message['data']
                try:
                    import gzip
                    decompressed_str = gzip.decompress(compressed_data).decode('utf-8')
                    return json.loads(decompressed_str)
                except:
                    return {'error': 'Decompression failed'}
            else:
                return message['data']

        except Exception as e:
            return {'error': str(e)}

    def _is_message_expired(self, message: Dict[str, Any]) -> bool:
        """Check if message has expired"""
        enqueued_at = message.get('enqueued_at', 0)
        return (time.time() - enqueued_at) > self.ttl_seconds

    async def _clean_expired_messages(self):
        """Remove expired messages from queue"""
        current_time = time.time()
        expired_count = 0

        # Remove expired messages from the front
        while (self.message_queue and
               self._is_message_expired(self.message_queue[0])):
            self.message_queue.popleft()
            expired_count += 1

        self.queue_stats['messages_expired'] += expired_count


class AgentOrchestrator:
    """
    ENHANCED Central orchestrator for multi-agent trading system

    Enhanced CPU Efficiency Features:
    1. CPU-efficient task scheduling algorithms
    2. Intelligent agent load balancing
    3. Optimized inter-agent communication protocols
    4. Memory-efficient message queuing
    5. Parallel agent coordination
    6. Optimized agent health monitoring
    7. Adaptive task prioritization
    8. Efficient agent resource allocation
    9. Optimized coordination loop performance
    10. Intelligent agent scaling

    Original Features:
    - Agent lifecycle management
    - Task distribution and scheduling
    - Inter-agent communication
    - Performance monitoring
    - Conflict resolution
    - Resource allocation
    - Emergency coordination
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db_manager = database_manager
        self.logger = TradingBotLogger("AgentOrchestrator")
        
        # Agent management
        self.agents: Dict[str, Any] = {}
        self.agent_info: Dict[str, AgentInfo] = {}
        self.task_queue: List[AgentTask] = []
        self.completed_tasks: Dict[str, AgentTask] = {}
        self.message_queue: List[AgentMessage] = []
        
        # Communication and coordination
        self.message_handlers: Dict[MessageType, Callable[..., Any]] = {}  # Ensuring type is fully defined
        self.task_handlers: Dict[str, Callable[..., Any]] = {}  # Ensuring type is fully defined
        self.shared_memory: Dict[str, Any] = {}
        
        # Performance tracking
        self.performance_metrics: Dict[str, Dict[str, float]] = {}
        self.coordination_history: List[Dict[str, Any]] = []
        
        # Control flags
        self.is_running = False
        self.emergency_mode = False
        self.coordination_interval = 5  # seconds
        
        # Thread pool for concurrent operations
        self.executor = ThreadPoolExecutor(max_workers=10)

        # CPU EFFICIENCY OPTIMIZATION COMPONENTS
        self.task_scheduler = CPUEfficientTaskScheduler(max_workers=4)
        self.load_balancer = IntelligentLoadBalancer()
        self.optimized_message_queue = MemoryEfficientMessageQueue(max_size=1000)

        # Optimization state
        self.scheduling_strategy = SchedulingStrategy.CPU_OPTIMIZED
        self.load_balancing_mode = LoadBalancingMode.ADAPTIVE
        self.optimization_metrics = {
            'cpu_scheduling_efficiency': 0.0,
            'load_balance_score': 0.0,
            'message_processing_rate': 0.0,
            'coordination_loop_efficiency': 0.0,
            'agent_scaling_efficiency': 0.0,
            'resource_allocation_optimization': 0.0,
            'parallel_coordination_cycles': 0,
            'adaptive_prioritization_adjustments': 0,
            'intelligent_scaling_operations': 0,
            'health_monitoring_optimizations': 0
        }

        # Initialize message handlers
        self._setup_message_handlers()
        
    async def initialize(self):
        """Initialize the agent orchestrator and all agents"""
        try:
            self.logger.info("Initializing Agent Orchestrator...")
            
            # Initialize memory manager for shared state
            self.memory_manager = PersistentMemoryManager(self.config, self.db_manager)
            await self.memory_manager.initialize()
            
            # Create and initialize agents
            await self._create_agents()
            await self._initialize_agents()
            
            # Initialize CPU optimization components
            await self._initialize_optimization_components()

            # Start coordination loop
            self.is_running = True
            asyncio.create_task(self._coordination_loop())
            asyncio.create_task(self._health_monitor_loop())
            asyncio.create_task(self._message_processing_loop())

            # Start CPU optimization loops
            asyncio.create_task(self._cpu_optimization_loop())
            asyncio.create_task(self._load_balancing_loop())
            asyncio.create_task(self._adaptive_prioritization_loop())
            asyncio.create_task(self._intelligent_scaling_loop())

            self.logger.info("ENHANCED Agent Orchestrator initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Agent Orchestrator: {e}")
            raise

    async def start_orchestration(self):
        """Start the agent orchestration system"""
        try:
            await self.initialize()
            self.logger.info("Agent Orchestrator started - multi-agent coordination operational")

            # Start continuous orchestration loop
            while True:
                await asyncio.sleep(self.coordination_interval)
                await self._coordinate_agents_implementation()

        except Exception as e:
            self.logger.error(f"Error in agent orchestration: {e}")
            raise

    async def _create_agents(self):
        """Create all specialized agents"""
        try:
            # Trading Agent
            self.agents['trading'] = TradingAgent(
                agent_id='trading_001',
                config=self.config,
                database_manager=self.db_manager,
                orchestrator=self
            )
            
            # Research Agent  
            self.agents['research'] = ResearchAgent(
                agent_id='research_001',
                config=self.config,
                database_manager=self.db_manager,
                orchestrator=self
            )
            
            # Risk Agent
            self.agents['risk'] = RiskAgent(
                agent_id='risk_001',
                config=self.config,
                database_manager=self.db_manager,
                orchestrator=self
            )
            
            # Learning Agent
            self.agents['learning'] = LearningAgent(
                agent_id='learning_001',
                config=self.config,
                database_manager=self.db_manager,
                orchestrator=self
            )
            
            # Initialize agent info
            for agent_type, agent in self.agents.items():
                self.agent_info[agent_type] = AgentInfo(
                    agent_id=agent.agent_id,
                    agent_type=agent_type,
                    status=AgentStatus.INITIALIZING,
                    capabilities=agent.get_capabilities(),
                    current_tasks=[],
                    performance_metrics={},
                    last_heartbeat=datetime.now()
                )
                
        except Exception as e:
            self.logger.error(f"Failed to create agents: {e}")
            raise
    
    async def _initialize_agents(self):
        """Initialize all agents"""
        for agent_type, agent in self.agents.items():
            try:
                await agent.initialize()
                
                # Set orchestrator references for AI systems in learning agent
                if agent_type == 'learning' and hasattr(agent, 'meta_cognition_engine'):
                    if agent.meta_cognition_engine:
                        agent.meta_cognition_engine.orchestrator = self
                    if agent.code_evolution_system:
                        agent.code_evolution_system.orchestrator = self
                    if agent.recursive_improvement_system:
                        agent.recursive_improvement_system.orchestrator = self
                
                self.agent_info[agent_type].status = AgentStatus.IDLE
                self.logger.info(f"Agent {agent_type} initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize agent {agent_type}: {e}")
                self.agent_info[agent_type].status = AgentStatus.ERROR
    
    def _setup_message_handlers(self):
        """Setup message handlers for different message types"""
        self.message_handlers = {
            MessageType.TASK_REQUEST: self._handle_task_request,
            MessageType.TASK_RESPONSE: self._handle_task_response,
            MessageType.DATA_SHARE: self._handle_data_share,
            MessageType.ALERT: self._handle_alert,
            MessageType.STATUS_UPDATE: self._handle_status_update,
            MessageType.COORDINATION: self._handle_coordination
        }
    
    async def _coordination_loop(self):
        """Main coordination loop"""
        while self.is_running:
            try:
                # Process pending tasks
                await self._process_task_queue()
                
                # Update agent statuses
                await self._update_agent_statuses()
                
                # Optimize resource allocation
                await self._optimize_resource_allocation()
                
                # Handle emergency situations
                if self.emergency_mode:
                    await self._handle_emergency()
                
                # Sleep until next cycle
                await asyncio.sleep(self.coordination_interval)
                
            except Exception as e:
                self.logger.error(f"Error in coordination loop: {e}")
                await asyncio.sleep(1)
    
    async def _health_monitor_loop(self):
        """Monitor agent health and performance"""
        while self.is_running:
            try:
                for agent_type, agent in self.agents.items():
                    # Check agent heartbeat
                    if hasattr(agent, 'get_status'):
                        await agent.get_status()
                        self.agent_info[agent_type].last_heartbeat = datetime.now()
                        
                        # Update performance metrics
                        if hasattr(agent, 'get_performance_metrics'):
                            metrics = await agent.get_performance_metrics()
                            self.agent_info[agent_type].performance_metrics = metrics
                
                # Check for stale agents
                await self._check_stale_agents()
                
                await asyncio.sleep(30)  # Health check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in health monitor: {e}")
                await asyncio.sleep(5)
    
    async def _message_processing_loop(self):
        """Process inter-agent messages"""
        while self.is_running:
            try:
                if self.message_queue:
                    message = self.message_queue.pop(0)
                    await self._process_message(message)
                
                await asyncio.sleep(0.1)  # Process messages frequently
                
            except Exception as e:
                self.logger.error(f"Error processing messages: {e}")
                await asyncio.sleep(1)
    
    async def _process_task_queue(self):
        """Process pending tasks and assign to appropriate agents"""
        if not self.task_queue:
            return
        
        # Sort tasks by priority and deadline
        self.task_queue.sort(key=lambda t: (t.priority.value, t.created_at))
        
        for task in self.task_queue[:]:
            # Find capable and available agent
            agent_type = self._find_suitable_agent(task)

            if agent_type:
                try:
                    # Assign task to agent
                    agent = self.agents[agent_type]
                    await agent.assign_task(task)

                    # Update tracking
                    self.agent_info[agent_type].current_tasks.append(task.task_id)
                    self.task_queue.remove(task)

                    self.logger.info(f"Assigned task {task.task_id} to agent {agent_type}")

                except Exception as e:
                    self.logger.error(f"Failed to assign task {task.task_id}: {e}")
    
    async def _find_best_agent_for_task(self, task: AgentTask) -> Optional[str]:
        """Find the best agent to handle a specific task"""
        suitable_agents: List[Tuple[str, AgentInfo]] = []  # Ensuring type is fully defined
        
        for agent_type, info in self.agent_info.items():
            # Check if agent can handle this task type
            if task.task_type in info.capabilities:
                # Check if agent is available
                if info.status == AgentStatus.IDLE or len(info.current_tasks) < 3:
                    suitable_agents.append((agent_type, info))
        
        if not suitable_agents:
            return None
        
        # Select best agent based on performance and load
        best_agent: Tuple[str, AgentInfo] = min(
            suitable_agents,
            key=lambda x: (
                len(x[1].current_tasks),  # Prefer less loaded agents
                -x[1].performance_metrics.get('success_rate', 0.5)  # Prefer better performing agents
            )
        )  # Ensuring type is fully defined
        
        return best_agent[0]
    
    async def _process_message(self, message: AgentMessage):
        """Process a single inter-agent message"""
        try:
            handler = self.message_handlers.get(message.message_type)
            if handler:
                await handler(message)
            else:
                self.logger.warning(f"No handler for message type: {message.message_type}")
                
        except Exception as e:
            self.logger.error(f"Error processing message {message.message_id}: {e}")
    
    async def _handle_task_request(self, message: AgentMessage):
        """Handle task request from an agent"""
        task_data = message.data
        task = AgentTask(
            task_id=f"task_{int(time.time() * 1000)}",
            agent_type=task_data.get('target_agent', 'any'),
            task_type=task_data['task_type'],
            priority=TaskPriority(task_data.get('priority', 3)),
            data=task_data['data'],
            created_at=datetime.now(),
            deadline=task_data.get('deadline'),
            callback=message.sender
        )
        
        self.task_queue.append(task)
        self.logger.info(f"Received task request from {message.sender}: {task.task_type}")
    
    async def _handle_task_response(self, message: AgentMessage):
        """Handle task completion response"""
        task_id = message.data['task_id']
                # Update agent task list
        for _, info in self.agent_info.items():
            if task_id in info.current_tasks:
                info.current_tasks.remove(task_id)
                break
        
        # Store completed task
        if task_id in [t.task_id for t in self.task_queue]:
            task = next(t for t in self.task_queue if t.task_id == task_id)
            self.completed_tasks[task_id] = task
        
        self.logger.info(f"Task {task_id} completed by {message.sender}")
    
    async def _handle_data_share(self, message: AgentMessage):
        """Handle data sharing between agents"""
        data_key = message.data['key']
        data_value = message.data['value']
        
        self.shared_memory[data_key] = {
            'value': data_value,
            'timestamp': datetime.now(),
            'source': message.sender
        }
        
        self.logger.debug(f"Data shared by {message.sender}: {data_key}")
    
    async def _handle_alert(self, message: AgentMessage):
        """Handle alerts from agents"""
        alert_type = message.data['type']
        severity = message.data['severity']
        details = message.data['details']
        
        self.logger.warning(f"Alert from {message.sender}: {alert_type} ({severity}) - {details}")
        
        # Handle critical alerts
        if severity == 'critical':
            await self._handle_critical_alert(message)
    
    async def _handle_status_update(self, message: AgentMessage):
        """Handle status updates from agents"""
        agent_type = message.sender
        new_status = AgentStatus(message.data['status'])
        
        if agent_type in self.agent_info:
            self.agent_info[agent_type].status = new_status
            self.agent_info[agent_type].last_heartbeat = datetime.now()
    
    async def _handle_coordination(self, message: AgentMessage):
        """Handle coordination messages"""
        coordination_type = message.data['type']
        
        if coordination_type == 'strategy_consensus':
            await self._handle_strategy_consensus(message)
        elif coordination_type == 'risk_assessment':
            await self._handle_risk_coordination(message)
        elif coordination_type == 'market_analysis':
            await self._handle_market_analysis_coordination(message)
    
    async def _handle_critical_alert(self, message: AgentMessage):
        """Handle critical alerts that may require emergency measures"""
        alert_type = message.data['type']
        
        if alert_type == 'system_failure':
            self.emergency_mode = True
            await self._emergency_shutdown_sequence()
        elif alert_type == 'major_loss':
            await self._emergency_risk_reduction()
        elif alert_type == 'market_crash':
            await self._emergency_position_closure()
    
    async def _handle_emergency(self):
        """Placeholder for handling emergencies."""
        self.logger.warning("Handling emergency situation...")
    
    async def _handle_risk_coordination(self, message: AgentMessage):
        """Placeholder for handling risk coordination."""
        self.logger.info(f"Coordinating risk for message: {message.message_id}")
    
    async def _update_agent_statuses(self):
        """Update and validate agent statuses"""
        for agent_type, info in self.agent_info.items():
            # Check for stale heartbeat
            time_since_heartbeat = datetime.now() - info.last_heartbeat
            if time_since_heartbeat > timedelta(minutes=2):
                info.status = AgentStatus.OFFLINE
                self.logger.warning(f"Agent {agent_type} appears offline")
    
    async def _optimize_resource_allocation(self):
        """Optimize resource allocation across agents"""
        # Analyze current workload distribution
        workloads = {agent_type: len(info.current_tasks) 
                    for agent_type, info in self.agent_info.items()}
        
        # Rebalance if needed
        max_load = max(workloads.values()) if workloads.values() else 0
        min_load = min(workloads.values()) if workloads.values() else 0
        
        if max_load - min_load > 2:  # Significant imbalance
            await self._rebalance_workload()
    
    async def _rebalance_workload(self):
        """Rebalance workload across agents"""
        # Implementation for workload rebalancing
        self.logger.info("Rebalancing workload across agents")
    
    async def _check_stale_agents(self):
        """Check for and handle stale agents"""
        current_time = datetime.now()
        
        for agent_type, info in self.agent_info.items():
            time_since_heartbeat = current_time - info.last_heartbeat

            if time_since_heartbeat > timedelta(minutes=5):
                self.logger.error(f"Agent {agent_type} is stale, attempting restart")
                await self._restart_agent(agent_type)
    
    async def _restart_agent(self, agent_type: str):
        """Restart a stale or failed agent"""
        try:
            agent = self.agents[agent_type]
            await agent.shutdown()
            await agent.initialize()
            
            self.agent_info[agent_type].status = AgentStatus.IDLE
            self.agent_info[agent_type].last_heartbeat = datetime.now()
            
            self.logger.info(f"Successfully restarted agent {agent_type}")
            
        except Exception as e:
            self.logger.error(f"Failed to restart agent {agent_type}: {e}")

    async def _coordinate_agents_implementation(self):
        """Implementation of agent coordination logic"""
        try:
            # Process task queue
            await self._process_task_queue()

            # Update agent statuses
            await self._update_agent_statuses()

            # Handle emergency situations
            if self.emergency_mode:
                await self._handle_emergency()

        except Exception as e:
            self.logger.error(f"Error in agent coordination: {e}")

    def _find_suitable_agent(self, task) -> Optional[str]:
        """Find the most suitable agent for a given task"""
        try:
            # Map task types to agent types
            task_type_mapping = {
                'trading': 'trading_agent',
                'analysis': 'learning_agent',
                'research': 'research_agent',
                'risk': 'risk_agent',
                'learning': 'learning_agent'
            }

            # Get task type from task object
            task_type = getattr(task, 'task_type', 'trading')

            # Find suitable agent type
            agent_type = task_type_mapping.get(task_type, 'trading_agent')

            # Check if agent exists and is available
            if agent_type in self.agents and agent_type in self.agent_info:
                agent_info = self.agent_info[agent_type]
                if agent_info.status in [AgentStatus.IDLE, AgentStatus.BUSY]:
                    return agent_type

            # Fallback to any available agent
            for agent_type, agent_info in self.agent_info.items():
                if agent_info.status in [AgentStatus.IDLE, AgentStatus.BUSY]:
                    return agent_type

            return None

        except Exception as e:
            self.logger.error(f"Error finding suitable agent: {e}")
            return None

    async def _emergency_shutdown_sequence(self):
        """Placeholder for emergency shutdown sequence."""
        self.logger.warning("Executing emergency shutdown sequence...")
    
    async def _handle_strategy_consensus(self, message: AgentMessage):
        """Placeholder for handling strategy consensus."""
        self.logger.info(f"Handling strategy consensus for message: {message.message_id}")
    
    async def _handle_market_analysis_coordination(self, message: AgentMessage):
        """Placeholder for handling market analysis coordination."""
        self.logger.info(f"Coordinating market analysis for message: {message.message_id}")

    async def _emergency_risk_reduction(self):
        """Placeholder for emergency risk reduction."""
        self.logger.warning("Executing emergency risk reduction...")

    async def _emergency_position_closure(self):
        """Placeholder for emergency position closure."""
        self.logger.warning("Executing emergency position closure...")
    
    # Public interface methods
    
    async def send_message(self, sender: str, recipient: str, message_type: MessageType, 
                          data: Dict[str, Any], priority: TaskPriority = TaskPriority.MEDIUM):
        """Send a message between agents"""
        message = AgentMessage(
            message_id=f"msg_{int(time.time() * 1000)}",
            sender=sender,
            recipient=recipient,
            message_type=message_type,
            data=data,
            timestamp=datetime.now(),
            priority=priority
        )
        
        self.message_queue.append(message)
    
    async def create_task(self, task_type: str, agent_type: str, data: Dict[str, Any],
                         priority: TaskPriority = TaskPriority.MEDIUM) -> str:
        """Create a new task for an agent"""
        task = AgentTask(
            task_id=f"task_{int(time.time() * 1000)}",
            agent_type=agent_type,
            task_type=task_type,
            priority=priority,
            data=data,
            created_at=datetime.now(timezone.utc)
        )
        
        self.task_queue.append(task)
        return task.task_id
    
    def get_shared_data(self, key: str) -> Any:
        """Get shared data by key"""
        return self.shared_memory.get(key, {}).get('value')
    
    def set_shared_data(self, key: str, value: Any, source: str = 'orchestrator'):
        """Set shared data"""
        self.shared_memory[key] = {
            'value': value,
            'timestamp': datetime.now(timezone.utc),
            'source': source
        }
    
    def get_agent_status(self, agent_type: str) -> Optional[AgentStatus]:
        """Get status of specific agent"""
        info = self.agent_info.get(agent_type)
        return info.status if info else None
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            'orchestrator_running': self.is_running,
            'emergency_mode': self.emergency_mode,
            'total_agents': len(self.agents),
            'active_agents': sum(1 for info in self.agent_info.values() 
                               if info.status in [AgentStatus.IDLE, AgentStatus.BUSY]),
            'pending_tasks': len(self.task_queue),
            'completed_tasks': len(self.completed_tasks),
            'agent_statuses': {agent_type: info.status.value 
                             for agent_type, info in self.agent_info.items()}
        }
    
    # CPU EFFICIENCY OPTIMIZATION METHODS
    async def _initialize_optimization_components(self):
        """Initialize CPU optimization components"""
        try:
            # Initialize task scheduler
            self.task_scheduler = CPUEfficientTaskScheduler(max_workers=4)

            # Initialize load balancer
            self.load_balancer = IntelligentLoadBalancer()

            # Initialize optimized message queue
            self.optimized_message_queue = MemoryEfficientMessageQueue(max_size=1000)

            self.logger.info("CPU optimization components initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize optimization components: {e}")

    async def perform_enhanced_task_scheduling(self, task: Any) -> Optional[str]:
        """Perform enhanced task scheduling with CPU optimization"""
        try:
            # Get current agent loads
            agent_loads = {agent_type: len(info.current_tasks)
                          for agent_type, info in self.agent_info.items()}

            # Get agent performance metrics
            agent_performance = {agent_type: info.performance_metrics.get('success_rate', 0.5)
                               for agent_type, info in self.agent_info.items()}

            # Use CPU-optimized scheduler
            optimal_agent = await self.task_scheduler.schedule_task_optimized(
                task, agent_loads, agent_performance
            )

            # Update optimization metrics
            self.optimization_metrics['cpu_scheduling_efficiency'] = (
                self.task_scheduler.scheduling_stats['cpu_efficiency']
            )

            return optimal_agent

        except Exception as e:
            self.logger.error(f"Enhanced task scheduling failed: {e}")
            return None

    async def perform_intelligent_load_balancing(self) -> Dict[str, Any]:
        """Perform intelligent load balancing across agents"""
        try:
            # Get current loads and performance
            agent_loads = {agent_type: len(info.current_tasks)
                          for agent_type, info in self.agent_info.items()}
            agent_performance = {agent_type: info.performance_metrics.get('success_rate', 0.5)
                               for agent_type, info in self.agent_info.items()}

            # Perform intelligent load balancing
            balancing_result = await self.load_balancer.balance_load_intelligent(
                agent_loads, agent_performance
            )

            # Update optimization metrics
            if balancing_result.get('rebalancing_needed', False):
                self.optimization_metrics['load_balance_score'] = (
                    1.0 - balancing_result.get('load_variance', 1.0)
                )

            return balancing_result

        except Exception as e:
            self.logger.error(f"Intelligent load balancing failed: {e}")
            return {'error': str(e)}

    async def _cpu_optimization_loop(self):
        """CPU optimization monitoring loop"""
        while self.is_running:
            try:
                # Monitor CPU efficiency
                cpu_metrics = await self._monitor_cpu_efficiency()

                # Optimize if needed
                if cpu_metrics.get('optimization_needed', False):
                    await self._optimize_cpu_performance(cpu_metrics)

                await asyncio.sleep(300)  # Optimize every 5 minutes

            except Exception as e:
                self.logger.error(f"CPU optimization loop error: {e}")
                await asyncio.sleep(600)

    async def _load_balancing_loop(self):
        """Load balancing optimization loop"""
        while self.is_running:
            try:
                # Perform load balancing check
                balancing_result = await self.perform_intelligent_load_balancing()

                # Apply recommendations if needed
                if balancing_result.get('rebalancing_needed', False):
                    await self._apply_load_balancing_recommendations(
                        balancing_result.get('recommendations', [])
                    )

                await asyncio.sleep(180)  # Balance every 3 minutes

            except Exception as e:
                self.logger.error(f"Load balancing loop error: {e}")
                await asyncio.sleep(360)

    async def _adaptive_prioritization_loop(self):
        """Adaptive task prioritization loop"""
        while self.is_running:
            try:
                # Analyze task priorities
                priority_analysis = await self._analyze_task_priorities()

                # Adjust priorities if needed
                if priority_analysis.get('adjustment_needed', False):
                    await self._adjust_task_priorities(priority_analysis)
                    self.optimization_metrics['adaptive_prioritization_adjustments'] += 1

                await asyncio.sleep(240)  # Adjust every 4 minutes

            except Exception as e:
                self.logger.error(f"Adaptive prioritization loop error: {e}")
                await asyncio.sleep(480)

    async def _intelligent_scaling_loop(self):
        """Intelligent agent scaling loop"""
        while self.is_running:
            try:
                # Analyze scaling needs
                scaling_analysis = await self._analyze_scaling_needs()

                # Perform scaling if needed
                if scaling_analysis.get('scaling_needed', False):
                    await self._perform_intelligent_scaling(scaling_analysis)
                    self.optimization_metrics['intelligent_scaling_operations'] += 1

                await asyncio.sleep(600)  # Scale every 10 minutes

            except Exception as e:
                self.logger.error(f"Intelligent scaling loop error: {e}")
                await asyncio.sleep(1200)

    async def _monitor_cpu_efficiency(self) -> Dict[str, Any]:
        """Monitor CPU efficiency across the system"""
        try:
            # Get scheduling efficiency
            scheduling_efficiency = self.task_scheduler.scheduling_stats['cpu_efficiency']

            # Get load balance score
            load_balance_score = self.optimization_metrics['load_balance_score']

            # Calculate overall CPU efficiency
            overall_efficiency = (scheduling_efficiency + load_balance_score) / 2

            return {
                'scheduling_efficiency': scheduling_efficiency,
                'load_balance_score': load_balance_score,
                'overall_efficiency': overall_efficiency,
                'optimization_needed': overall_efficiency < 0.7
            }

        except Exception as e:
            return {'error': str(e), 'optimization_needed': False}

    async def _optimize_cpu_performance(self, cpu_metrics: Dict[str, Any]):
        """Optimize CPU performance based on metrics"""
        try:
            # Adjust scheduling strategy if needed
            if cpu_metrics.get('scheduling_efficiency', 0) < 0.6:
                self.scheduling_strategy = SchedulingStrategy.CPU_OPTIMIZED

            # Adjust load balancing mode if needed
            if cpu_metrics.get('load_balance_score', 0) < 0.6:
                self.load_balancing_mode = LoadBalancingMode.ADAPTIVE

            self.logger.info("CPU performance optimization applied")

        except Exception as e:
            self.logger.error(f"CPU performance optimization failed: {e}")

    async def _apply_load_balancing_recommendations(self, recommendations: List[Dict[str, Any]]):
        """Apply load balancing recommendations"""
        try:
            for recommendation in recommendations:
                agent_type = recommendation['agent_type']
                action = recommendation['action']

                if action == 'decrease_load' and agent_type in self.agent_info:
                    # Temporarily reduce task assignment to this agent
                    self.agent_info[agent_type].performance_metrics['load_factor'] = 0.5
                elif action == 'increase_load' and agent_type in self.agent_info:
                    # Increase task assignment to this agent
                    self.agent_info[agent_type].performance_metrics['load_factor'] = 1.5

            self.logger.info(f"Applied {len(recommendations)} load balancing recommendations")

        except Exception as e:
            self.logger.error(f"Load balancing recommendation application failed: {e}")

    async def _analyze_task_priorities(self) -> Dict[str, Any]:
        """Analyze task priorities for optimization"""
        try:
            if not self.task_queue:
                return {'adjustment_needed': False}

            # Analyze priority distribution
            priority_counts = {}
            for task in self.task_queue:
                priority = getattr(task, 'priority', 3)
                priority_counts[priority] = priority_counts.get(priority, 0) + 1

            # Check if adjustment is needed
            total_tasks = len(self.task_queue)
            high_priority_ratio = priority_counts.get(1, 0) / total_tasks if total_tasks > 0 else 0

            return {
                'adjustment_needed': high_priority_ratio > 0.5,  # Too many high priority tasks
                'priority_distribution': priority_counts,
                'high_priority_ratio': high_priority_ratio
            }

        except Exception as e:
            return {'error': str(e), 'adjustment_needed': False}

    async def _adjust_task_priorities(self, priority_analysis: Dict[str, Any]):
        """Adjust task priorities based on analysis"""
        try:
            # Implement priority adjustment logic
            high_priority_ratio = priority_analysis.get('high_priority_ratio', 0)

            if high_priority_ratio > 0.5:
                # Reduce some high priority tasks to medium priority
                adjusted_count = 0
                for task in self.task_queue:
                    if getattr(task, 'priority', 3) == 1 and adjusted_count < 3:
                        task.priority = 2  # Reduce to medium priority
                        adjusted_count += 1

                self.logger.info(f"Adjusted {adjusted_count} task priorities")

        except Exception as e:
            self.logger.error(f"Task priority adjustment failed: {e}")

    async def _analyze_scaling_needs(self) -> Dict[str, Any]:
        """Analyze agent scaling needs"""
        try:
            # Calculate average load per agent
            total_tasks = sum(len(info.current_tasks) for info in self.agent_info.values())
            agent_count = len(self.agent_info)
            avg_load = total_tasks / agent_count if agent_count > 0 else 0

            # Check if scaling is needed
            scaling_needed = avg_load > 5  # Scale if average load > 5 tasks per agent

            return {
                'scaling_needed': scaling_needed,
                'average_load': avg_load,
                'total_tasks': total_tasks,
                'agent_count': agent_count,
                'recommendation': 'scale_up' if scaling_needed else 'maintain'
            }

        except Exception as e:
            return {'error': str(e), 'scaling_needed': False}

    async def _perform_intelligent_scaling(self, scaling_analysis: Dict[str, Any]):
        """Perform intelligent agent scaling"""
        try:
            recommendation = scaling_analysis.get('recommendation', 'maintain')

            if recommendation == 'scale_up':
                # Implement scaling up logic (would create additional agent instances)
                self.logger.info("Intelligent scaling up recommended")
                # In a real implementation, this would create new agent instances

            elif recommendation == 'scale_down':
                # Implement scaling down logic
                self.logger.info("Intelligent scaling down recommended")
                # In a real implementation, this would safely shutdown excess agents

        except Exception as e:
            self.logger.error(f"Intelligent scaling failed: {e}")

    async def shutdown(self):
        """Gracefully shutdown the orchestrator and all agents"""
        self.logger.info("Shutting down ENHANCED Agent Orchestrator...")

        self.is_running = False
        
        # Shutdown all agents
        for agent_type, agent in self.agents.items():
            try:
                await agent.shutdown()
                self.logger.info(f"Agent {agent_type} shutdown complete")
            except Exception as e:
                self.logger.error(f"Error shutting down agent {agent_type}: {e}")

        # Fixing indentation and undefined variables in the shutdown method.

        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        self.logger.info("Agent Orchestrator shutdown complete")
