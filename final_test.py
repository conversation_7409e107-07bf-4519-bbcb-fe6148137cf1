#!/usr/bin/env python3
"""
Final test to verify all errors are resolved
"""
import sys
import os
import asyncio
import traceback
from datetime import datetime

def main():
    print(f"FINAL SYSTEM TEST - {datetime.now()}")
    print("=" * 60)
    
    # Set up environment
    sys.path.append('.')
    
    try:
        print("STEP 1: Testing main system import...")
        from bybit_bot.main import BybitTradingBotSystem
        print("✓ Main system imported successfully")
        
        print("STEP 2: Testing bot instantiation...")
        bot = BybitTradingBotSystem()
        print("✓ Bot instantiated successfully")
        
        print("STEP 3: Testing critical methods exist...")
        critical_methods = ['initialize_all_systems', 'run', '_run_mpc_security_monitor', 'cleanup']
        for method in critical_methods:
            if hasattr(bot, method) and callable(getattr(bot, method)):
                print(f"✓ {method} method exists and callable")
            else:
                print(f"✗ {method} method missing or not callable")
                return False
        
        print("STEP 4: Testing async initialization (quick test)...")
        
        async def quick_init_test():
            try:
                # Just test if the method can be called
                print("  Starting initialization test...")
                result = await bot.initialize_all_systems()
                print(f"  Initialization completed: {result}")
                return True
            except Exception as init_error:
                print(f"  Initialization error: {init_error}")
                # Print first few lines of traceback for debugging
                tb_lines = traceback.format_exc().split('\n')
                for line in tb_lines[:10]:  # First 10 lines
                    print(f"    {line}")
                return False
        
        # Run async test with timeout
        try:
            result = asyncio.wait_for(quick_init_test(), timeout=60.0)
            init_success = asyncio.run(result)
        except asyncio.TimeoutError:
            print("  Initialization test timed out (60s)")
            init_success = False
        except Exception as e:
            print(f"  Async test error: {e}")
            init_success = False
        
        if init_success:
            print("✓ Async initialization test passed")
        else:
            print("✗ Async initialization test failed")
        
        print("=" * 60)
        print("FINAL TEST COMPLETED")
        
        # Test cleanup
        print("STEP 5: Testing cleanup...")
        try:
            cleanup_result = asyncio.run(bot.cleanup())
            print(f"✓ Cleanup completed: {cleanup_result}")
        except Exception as cleanup_error:
            print(f"✗ Cleanup error: {cleanup_error}")
        
        return init_success
        
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        print("FULL TRACEBACK:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nFINAL RESULT: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
