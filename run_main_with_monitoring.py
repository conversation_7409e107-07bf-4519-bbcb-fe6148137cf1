#!/usr/bin/env python3
"""
RUN MAIN.PY WITH COMPREHENSIVE MONITORING
Capture all output, warnings, errors, and exceptions
"""

import sys
import os
import subprocess
import time
import threading
from datetime import datetime, timezone

print("🔍 RUNNING MAIN.PY WITH COMPREHENSIVE MONITORING")
print("=" * 80)

class MainPyMonitor:
    def __init__(self):
        self.output_lines = []
        self.error_lines = []
        self.warning_lines = []
        self.process = None
        self.start_time = time.time()
        
    def run_main_py_with_timeout(self, timeout_seconds=60):
        """Run main.py with timeout and capture all output"""
        print(f"🚀 Starting main.py with {timeout_seconds}s timeout...")
        print(f"⏰ Start time: {datetime.now(timezone.utc)}")
        print("-" * 60)
        
        try:
            # Change to correct directory and run main.py
            cmd = [sys.executable, "bybit_bot/main.py"]
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd="."
            )
            
            # Monitor output in real-time
            def monitor_stdout():
                for line in iter(self.process.stdout.readline, ''):
                    if line:
                        line = line.strip()
                        self.output_lines.append(line)
                        print(f"📤 OUT: {line}")
                        
                        # Check for specific patterns
                        if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed', 'critical']):
                            self.error_lines.append(line)
                            print(f"❌ ERROR DETECTED: {line}")
                            
                        if any(keyword in line.lower() for keyword in ['warning', 'warn', 'deprecated']):
                            self.warning_lines.append(line)
                            print(f"⚠️  WARNING DETECTED: {line}")
                            
            def monitor_stderr():
                for line in iter(self.process.stderr.readline, ''):
                    if line:
                        line = line.strip()
                        self.error_lines.append(line)
                        print(f"📥 ERR: {line}")
                        
            # Start monitoring threads
            stdout_thread = threading.Thread(target=monitor_stdout)
            stderr_thread = threading.Thread(target=monitor_stderr)
            
            stdout_thread.daemon = True
            stderr_thread.daemon = True
            
            stdout_thread.start()
            stderr_thread.start()
            
            # Wait for timeout or completion
            try:
                return_code = self.process.wait(timeout=timeout_seconds)
                print(f"\n✅ Process completed with return code: {return_code}")
                
            except subprocess.TimeoutExpired:
                print(f"\n⏰ Process timeout after {timeout_seconds} seconds")
                print("🛑 Terminating process...")
                self.process.terminate()
                
                # Wait a bit for graceful termination
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print("🔪 Force killing process...")
                    self.process.kill()
                    
                return_code = -1
                
            # Wait for monitoring threads to finish
            stdout_thread.join(timeout=2)
            stderr_thread.join(timeout=2)
            
            return return_code
            
        except Exception as e:
            print(f"❌ Error running main.py: {e}")
            return -2
            
    def analyze_output(self):
        """Analyze captured output for issues"""
        print("\n" + "=" * 80)
        print("📊 OUTPUT ANALYSIS")
        print("=" * 80)
        
        runtime = time.time() - self.start_time
        print(f"⏱️  Runtime: {runtime:.2f} seconds")
        print(f"📝 Total output lines: {len(self.output_lines)}")
        print(f"❌ Error lines: {len(self.error_lines)}")
        print(f"⚠️  Warning lines: {len(self.warning_lines)}")
        
        # Show first few output lines
        if self.output_lines:
            print(f"\n📤 FIRST 10 OUTPUT LINES:")
            for i, line in enumerate(self.output_lines[:10]):
                print(f"  {i+1:2d}: {line}")
                
        # Show last few output lines
        if len(self.output_lines) > 10:
            print(f"\n📤 LAST 10 OUTPUT LINES:")
            for i, line in enumerate(self.output_lines[-10:]):
                print(f"  {len(self.output_lines)-10+i+1:2d}: {line}")
                
        # Show all errors
        if self.error_lines:
            print(f"\n❌ ALL ERRORS:")
            for i, error in enumerate(self.error_lines):
                print(f"  {i+1}: {error}")
                
        # Show all warnings
        if self.warning_lines:
            print(f"\n⚠️  ALL WARNINGS:")
            for i, warning in enumerate(self.warning_lines):
                print(f"  {i+1}: {warning}")
                
        # Pattern analysis
        print(f"\n🔍 PATTERN ANALYSIS:")
        
        # Check for successful initialization
        init_patterns = [
            'initialized', 'starting', 'loaded', 'connected', 'ready'
        ]
        
        init_found = []
        for pattern in init_patterns:
            matching_lines = [line for line in self.output_lines if pattern.lower() in line.lower()]
            if matching_lines:
                init_found.extend(matching_lines)
                
        if init_found:
            print(f"✅ INITIALIZATION PATTERNS FOUND:")
            for line in init_found[:5]:  # Show first 5
                print(f"  • {line}")
        else:
            print(f"⚠️  NO INITIALIZATION PATTERNS FOUND")
            
        # Check for trading activity
        trading_patterns = [
            'trade', 'order', 'buy', 'sell', 'profit', 'balance'
        ]
        
        trading_found = []
        for pattern in trading_patterns:
            matching_lines = [line for line in self.output_lines if pattern.lower() in line.lower()]
            if matching_lines:
                trading_found.extend(matching_lines)
                
        if trading_found:
            print(f"✅ TRADING ACTIVITY PATTERNS FOUND:")
            for line in trading_found[:3]:  # Show first 3
                print(f"  • {line}")
        else:
            print(f"ℹ️  NO TRADING ACTIVITY DETECTED (may be normal for short run)")
            
    def generate_health_report(self):
        """Generate final health report"""
        print("\n" + "=" * 80)
        print("🏥 MAIN.PY HEALTH REPORT")
        print("=" * 80)
        
        # Determine overall health
        critical_errors = len([e for e in self.error_lines if any(keyword in e.lower() for keyword in ['critical', 'fatal', 'exception'])])
        import_errors = len([e for e in self.error_lines if 'import' in e.lower() or 'module' in e.lower()])
        
        if critical_errors == 0 and import_errors == 0 and len(self.error_lines) == 0:
            health_status = "EXCELLENT"
            health_emoji = "🟢"
        elif critical_errors == 0 and import_errors == 0 and len(self.error_lines) < 3:
            health_status = "GOOD"
            health_emoji = "🟡"
        elif critical_errors == 0 and import_errors == 0:
            health_status = "FAIR"
            health_emoji = "🟠"
        else:
            health_status = "NEEDS ATTENTION"
            health_emoji = "🔴"
            
        print(f"{health_emoji} OVERALL HEALTH: {health_status}")
        print(f"❌ Critical errors: {critical_errors}")
        print(f"📦 Import errors: {import_errors}")
        print(f"⚠️  Total warnings: {len(self.warning_lines)}")
        print(f"📝 Total output lines: {len(self.output_lines)}")
        
        # Recommendations
        print(f"\n📋 RECOMMENDATIONS:")
        
        if health_status == "EXCELLENT":
            print("✅ Main.py is running perfectly")
            print("✅ No issues detected")
            print("✅ System ready for production use")
        elif health_status == "GOOD":
            print("✅ Main.py is running well with minor issues")
            print("• Monitor warnings for potential improvements")
        elif health_status == "FAIR":
            print("⚠️  Main.py has some issues but is functional")
            print("• Review error messages for optimization opportunities")
        else:
            print("🚨 Main.py has significant issues")
            print("• Address critical errors before production use")
            print("• Check import dependencies")
            
        return health_status in ["EXCELLENT", "GOOD"]

if __name__ == "__main__":
    monitor = MainPyMonitor()
    
    # Run main.py with monitoring
    return_code = monitor.run_main_py_with_timeout(timeout_seconds=45)
    
    # Analyze results
    monitor.analyze_output()
    success = monitor.generate_health_report()
    
    print(f"\n🎯 FINAL RESULT: {'SUCCESS' if success else 'NEEDS ATTENTION'}")
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)
