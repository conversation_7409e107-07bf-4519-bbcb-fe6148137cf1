#!/usr/bin/env python3
"""
Main.py Error Testing Script
Tests for syntax errors, import errors, and runtime issues
"""

import ast
import sys
import os
import traceback

# Add current directory to Python path
sys.path.insert(0, '.')

def test_syntax():
    """Test main.py for syntax errors"""
    print("1. SYNTAX CHECK")
    print("-" * 30)
    
    try:
        with open('bybit_bot/main.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # Parse the source code
        ast.parse(source_code, filename='bybit_bot/main.py')
        print("   ✓ No syntax errors found")
        print(f"   ✓ File size: {len(source_code):,} characters")
        print(f"   ✓ Lines: {source_code.count(chr(10)):,}")
        return True
        
    except SyntaxError as e:
        print(f"   ✗ SYNTAX ERROR:")
        print(f"     Line {e.lineno}: {e.msg}")
        print(f"     Text: {e.text}")
        return False
    except Exception as e:
        print(f"   ✗ ERROR: {e}")
        return False

def test_imports():
    """Test critical imports"""
    print("\n2. IMPORT CHECK")
    print("-" * 30)
    
    imports_to_test = [
        ("Main System", "bybit_bot.main", "BybitTradingBotSystem"),
        ("Optimization Manager", "bybit_bot.core.optimization_manager", "OptimizationComponentsManager"),
        ("Optimization Config", "bybit_bot.core.optimization_manager", "OptimizationConfig"),
    ]
    
    success_count = 0
    
    for name, module_path, class_name in imports_to_test:
        try:
            module = __import__(module_path, fromlist=[class_name])
            if hasattr(module, class_name):
                print(f"   ✓ {name}: {class_name}")
                success_count += 1
            else:
                print(f"   ✗ {name}: {class_name} not found")
        except ImportError as e:
            print(f"   ✗ {name}: Import failed - {e}")
        except Exception as e:
            print(f"   ✗ {name}: Error - {e}")
    
    print(f"   Summary: {success_count}/{len(imports_to_test)} imports successful")
    return success_count == len(imports_to_test)

def test_system_creation():
    """Test system creation"""
    print("\n3. SYSTEM CREATION")
    print("-" * 30)
    
    try:
        from bybit_bot.main import BybitTradingBotSystem
        
        print("   Creating system instance...")
        system = BybitTradingBotSystem()
        print("   ✓ System created successfully")
        
        # Check for optimization attributes
        optimization_attrs = [
            'optimization_manager', 'optimization_active'
        ]
        
        missing_attrs = []
        for attr in optimization_attrs:
            if hasattr(system, attr):
                print(f"   ✓ Has attribute: {attr}")
            else:
                print(f"   ✗ Missing attribute: {attr}")
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"   ⚠ Missing attributes: {missing_attrs}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ✗ System creation failed: {e}")
        traceback.print_exc()
        return False

def test_optimization_manager():
    """Test optimization manager creation"""
    print("\n4. OPTIMIZATION MANAGER")
    print("-" * 30)
    
    try:
        from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
        
        print("   Creating optimization config...")
        config = OptimizationConfig()
        print("   ✓ Config created")
        
        print("   Creating optimization manager...")
        manager = OptimizationComponentsManager(config)
        print("   ✓ Manager created")
        
        # Check manager attributes
        required_attrs = ['config', 'components', 'component_status']
        for attr in required_attrs:
            if hasattr(manager, attr):
                print(f"   ✓ Manager has: {attr}")
            else:
                print(f"   ✗ Manager missing: {attr}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ✗ Optimization manager test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all error tests"""
    print("MAIN.PY ERROR TESTING")
    print("=" * 50)
    print("Testing for syntax errors, import issues, and runtime problems")
    print("=" * 50)
    
    tests = [
        ("Syntax Check", test_syntax),
        ("Import Check", test_imports),
        ("System Creation", test_system_creation),
        ("Optimization Manager", test_optimization_manager)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"\n   ✗ {test_name} failed with exception: {e}")
            traceback.print_exc()
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("ERROR TESTING SUMMARY")
    print("=" * 50)
    
    for i, (test_name, _) in enumerate(tests):
        status = "PASS" if results[i] else "FAIL"
        print(f"{status:4} | {test_name}")
    
    print("-" * 50)
    print(f"RESULT: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("\n🎉 SUCCESS: NO ERRORS FOUND!")
        print("✅ main.py syntax is correct")
        print("✅ All imports work properly")
        print("✅ System creation works")
        print("✅ Optimization integration works")
        print("✅ MAIN.PY IS READY TO RUN!")
        return True
    else:
        print(f"\n❌ ERRORS FOUND: {total-passed} test(s) failed")
        print("❌ Fix errors before running main.py")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTesting interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nTesting failed: {e}")
        traceback.print_exc()
        sys.exit(1)
