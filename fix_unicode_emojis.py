#!/usr/bin/env python3
"""
Fix Unicode/Emoji issues in all Python files
Replace all emojis with plain text equivalents
"""

import os
import re
from pathlib import Path

def fix_unicode_emojis():
    """Fix all Unicode/emoji issues in Python files"""
    
    # Emoji to text mappings
    emoji_replacements = {
        '✓': 'SUCCESS',
        '✅': 'SUCCESS',
        '❌': 'ERROR',
        '✗': 'ERROR',
        '⚠️': 'WARNING',
        '🚀': 'STARTING',
        '🔍': 'CHECKING',
        '📥': 'DOWNLOAD',
        '📱': 'MOBILE',
        '📏': 'SIZE',
        '📋': 'COPIED',
        '💰': 'PROFIT',
        '📊': 'DATA',
        '🔧': 'CONFIGURING',
        '⭐': 'STAR',
        '🎯': 'TARGET',
        '🔥': 'HOT',
        '💡': 'IDEA',
        '🎉': 'CELEBRATION',
        '🎊': 'PARTY',
        '🏆': 'TROPHY',
        '🥇': 'FIRST_PLACE',
        '🥈': 'SECOND_PLACE',
        '🥉': 'THIRD_PLACE',
        '🎖️': 'MEDAL',
        '🏅': 'MEDAL',
        '🎗️': 'RIBBON',
        '🎀': 'BOW',
        '🎁': 'GIFT',
        '🎂': 'CAKE',
        '🍰': 'CAKE',
        '🧁': 'CUPCAKE',
        '🍪': 'COOKIE',
        '🍫': 'CHOCOLATE',
        '🍬': 'CANDY',
        '🍭': 'LOLLIPOP',
        '🍯': 'HONEY',
        '🍎': 'APPLE',
        '🍊': 'ORANGE',
        '🍋': 'LEMON',
        '🍌': 'BANANA',
        '🍉': 'WATERMELON',
        '🍇': 'GRAPES',
        '🍓': 'STRAWBERRY',
        '🫐': 'BLUEBERRY',
        '🍈': 'MELON',
        '🍒': 'CHERRY',
        '🍑': 'PEACH',
        '🥭': 'MANGO',
        '🍍': 'PINEAPPLE',
        '🥥': 'COCONUT',
        '🥝': 'KIWI',
        '🍅': 'TOMATO',
        '🍆': 'EGGPLANT',
        '🥑': 'AVOCADO',
        '🥦': 'BROCCOLI',
        '🥬': 'LETTUCE',
        '🥒': 'CUCUMBER',
        '🌶️': 'PEPPER',
        '🫑': 'BELL_PEPPER',
        '🌽': 'CORN',
        '🥕': 'CARROT',
        '🫒': 'OLIVE',
        '🧄': 'GARLIC',
        '🧅': 'ONION',
        '🥔': 'POTATO',
        '🍠': 'SWEET_POTATO',
        '🥐': 'CROISSANT',
        '🥖': 'BAGUETTE',
        '🫓': 'FLATBREAD',
        '🥨': 'PRETZEL',
        '🥯': 'BAGEL',
        '🧇': 'WAFFLE',
        '🥞': 'PANCAKES',
        '🧈': 'BUTTER',
        '🍳': 'COOKING',
        '🥚': 'EGG',
        '🧀': 'CHEESE',
        '🥓': 'BACON',
        '🥩': 'MEAT',
        '🍗': 'CHICKEN',
        '🍖': 'MEAT',
        '🌭': 'HOT_DOG',
        '🍔': 'BURGER',
        '🍟': 'FRIES',
        '🍕': 'PIZZA',
        '🥪': 'SANDWICH',
        '🌮': 'TACO',
        '🌯': 'BURRITO',
        '🫔': 'TAMALE',
        '🥙': 'STUFFED_FLATBREAD',
        '🧆': 'FALAFEL',
        '🥘': 'PAELLA',
        '🍝': 'PASTA',
        '🍜': 'NOODLES',
        '🍲': 'STEW',
        '🍛': 'CURRY',
        '🍣': 'SUSHI',
        '🍱': 'BENTO',
        '🥟': 'DUMPLING',
        '🦪': 'OYSTER',
        '🍤': 'SHRIMP',
        '🍙': 'RICE_BALL',
        '🍘': 'RICE_CRACKER',
        '🍥': 'FISH_CAKE',
        '🥠': 'FORTUNE_COOKIE',
        '🥮': 'MOON_CAKE',
        '🍢': 'ODEN',
        '🍡': 'DANGO',
        '🍧': 'SHAVED_ICE',
        '🍨': 'ICE_CREAM',
        '🍦': 'SOFT_ICE_CREAM',
        '🥧': 'PIE',
        '🧁': 'CUPCAKE',
        '🍰': 'CAKE',
        '🎂': 'BIRTHDAY_CAKE',
        '🍮': 'CUSTARD',
        '🍭': 'LOLLIPOP',
        '🍬': 'CANDY',
        '🍫': 'CHOCOLATE_BAR',
        '🍿': 'POPCORN',
        '🍩': 'DONUT',
        '🍪': 'COOKIE',
        '🌰': 'CHESTNUT',
        '🥜': 'PEANUTS',
        '🍯': 'HONEY_POT',
        '🥛': 'MILK',
        '🍼': 'BABY_BOTTLE',
        '☕': 'COFFEE',
        '🫖': 'TEAPOT',
        '🍵': 'TEA',
        '🧃': 'BEVERAGE_BOX',
        '🥤': 'CUP_WITH_STRAW',
        '🧋': 'BUBBLE_TEA',
        '🍶': 'SAKE',
        '🍾': 'CHAMPAGNE',
        '🍷': 'WINE',
        '🍸': 'COCKTAIL',
        '🍹': 'TROPICAL_DRINK',
        '🍺': 'BEER',
        '🍻': 'BEERS',
        '🥂': 'CLINKING_GLASSES',
        '🥃': 'TUMBLER_GLASS',
        '🫗': 'POURING_LIQUID',
        '🥄': 'SPOON',
        '🍴': 'FORK_AND_KNIFE',
        '🍽️': 'PLATE',
        '🥣': 'BOWL',
        '🥡': 'TAKEOUT_BOX',
        '🥢': 'CHOPSTICKS',
        '🧂': 'SALT'
    }
    
    # Files to process
    python_files = []
    
    # Find all Python files
    for root, dirs, files in os.walk('.'):
        # Skip certain directories
        skip_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    print(f"Found {len(python_files)} Python files to process")
    
    fixed_files = 0
    total_replacements = 0
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_replacements = 0
            
            # Replace emojis
            for emoji, replacement in emoji_replacements.items():
                if emoji in content:
                    content = content.replace(emoji, replacement)
                    file_replacements += 1
            
            # Additional Unicode character fixes
            # Replace common Unicode symbols
            unicode_replacements = {
                '→': '->',
                '←': '<-',
                '↑': '^',
                '↓': 'v',
                '↔': '<->',
                '⇒': '=>',
                '⇐': '<=',
                '⇔': '<=>',
                '∞': 'infinity',
                '±': '+/-',
                '×': 'x',
                '÷': '/',
                '≤': '<=',
                '≥': '>=',
                '≠': '!=',
                '≈': '~=',
                '°': 'deg',
                '™': 'TM',
                '®': '(R)',
                '©': '(C)',
                '§': 'section',
                '¶': 'paragraph',
                '†': 'dagger',
                '‡': 'double_dagger',
                '•': '*',
                '‰': 'per_mille',
                '′': "'",
                '″': '"',
                '‴': "'''",
                '‹': '<',
                '›': '>',
                '«': '<<',
                '»': '>>',
                '"': '"',
                '"': '"',
                ''': "'",
                ''': "'",
                '–': '-',
                '—': '--',
                '…': '...',
                '‚': ',',
                '„': ',,',
                '‰': 'per_mille',
                '‱': 'per_ten_thousand'
            }
            
            for unicode_char, replacement in unicode_replacements.items():
                if unicode_char in content:
                    content = content.replace(unicode_char, replacement)
                    file_replacements += 1
            
            # Write back if changes were made
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files += 1
                total_replacements += file_replacements
                print(f"FIXED: {file_path} ({file_replacements} replacements)")
        
        except Exception as e:
            print(f"ERROR processing {file_path}: {e}")
    
    print(f"\nSUCCESS: Fixed {fixed_files} files with {total_replacements} total replacements")
    return fixed_files > 0

if __name__ == "__main__":
    fix_unicode_emojis()
