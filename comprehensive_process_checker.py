#!/usr/bin/env python3
"""
COMPREHENSIVE PROCESS CHECKER
Deep analysis of all processes with focus on time issues
"""

import sys
import os
import time
import asyncio
import importlib.util
import traceback
from datetime import datetime, timezone
from pathlib import Path

print("🔍 COMPREHENSIVE PROCESS CHECKER - STARTING")
print("=" * 80)

class ProcessChecker:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.time_issues = []
        
    def log_error(self, msg):
        self.errors.append(msg)
        print(f"❌ ERROR: {msg}")
        
    def log_warning(self, msg):
        self.warnings.append(msg)
        print(f"⚠️  WARNING: {msg}")
        
    def log_info(self, msg):
        print(f"ℹ️  INFO: {msg}")

    def check_main_system_imports(self):
        """Check if main system can be imported without errors"""
        print("\n📦 CHECKING MAIN SYSTEM IMPORTS...")
        
        try:
            # Add current directory to path
            sys.path.insert(0, '.')
            
            # Try importing main system
            from bybit_bot.main import BybitTradingBotSystem
            self.log_info("✅ Main system imports successfully")
            
            # Try creating instance
            system = BybitTradingBotSystem()
            self.log_info("✅ Main system instance created successfully")
            
            # Check critical attributes
            critical_attrs = [
                'is_running', 'start_time', 'total_profit', 'total_trades',
                'session_stats', 'profit_targets', 'hourly_stats'
            ]
            
            for attr in critical_attrs:
                if hasattr(system, attr):
                    self.log_info(f"✅ Attribute {attr} exists")
                else:
                    self.log_error(f"Missing critical attribute: {attr}")
                    
        except Exception as e:
            self.log_error(f"Main system import failed: {e}")
            traceback.print_exc()

    def check_time_consistency(self):
        """Check time usage consistency across the system"""
        print("\n⏰ CHECKING TIME CONSISTENCY...")
        
        # Check main.py for time usage patterns
        main_py = Path("bybit_bot/main.py")
        if main_py.exists():
            with open(main_py, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Count different time patterns
            datetime_utc_count = content.count('datetime.now(timezone.utc)')
            time_time_count = content.count('time.time()')
            
            self.log_info(f"datetime.now(timezone.utc) usage: {datetime_utc_count}")
            self.log_info(f"time.time() usage: {time_time_count}")
            
            if datetime_utc_count > 0 and time_time_count > 0:
                self.log_info("✅ Mixed time usage is acceptable (UTC for datetime, timestamp for time)")
            
            # Check for problematic patterns
            if 'datetime.now()' in content and 'datetime.now(timezone.utc)' not in content:
                self.log_warning("Found datetime.now() without timezone - potential timezone issue")
                
        # Test time precision
        start = time.time()
        time.sleep(0.001)
        end = time.time()
        precision = end - start
        
        if 0.0005 <= precision <= 0.002:
            self.log_info(f"✅ Time precision OK: {precision:.6f}s")
        else:
            self.log_warning(f"Time precision issue: {precision:.6f}s (expected ~0.001s)")

    def check_optimization_components(self):
        """Check if optimization components can be imported"""
        print("\n⚡ CHECKING OPTIMIZATION COMPONENTS...")
        
        optimization_modules = [
            'bybit_bot.core.optimization_manager',
            'bybit_bot.streaming.advanced_kafka_flink_processor',
            'bybit_bot.streaming.time_series_compression',
            'bybit_bot.ai.multi_agent_rl_system',
            'bybit_bot.ai.graph_neural_networks',
            'bybit_bot.edge.edge_computing_engine',
            'bybit_bot.quantum.quantum_ml_engine',
            'bybit_bot.features.advanced_feature_pipeline',
            'bybit_bot.ai.adaptive_learning_system'
        ]
        
        for module_name in optimization_modules:
            try:
                module = importlib.import_module(module_name)
                self.log_info(f"✅ {module_name}")
            except Exception as e:
                self.log_error(f"Failed to import {module_name}: {e}")

    def check_mcp_components(self):
        """Check MPC/MCP components"""
        print("\n🔐 CHECKING MPC COMPONENTS...")
        
        mcp_modules = [
            'bybit_bot.mcp.mpc_engine',
            'bybit_bot.mcp.bybit_mpc_wallet',
            'bybit_bot.mcp.mpc_security_layer',
            'bybit_bot.mcp.threshold_signatures'
        ]
        
        for module_name in mcp_modules:
            try:
                module = importlib.import_module(module_name)
                self.log_info(f"✅ {module_name}")
            except Exception as e:
                self.log_error(f"Failed to import {module_name}: {e}")

    def check_database_integrity(self):
        """Check database file integrity"""
        print("\n🗄️  CHECKING DATABASE INTEGRITY...")
        
        db_path = Path("bybit_bot/bybit_trading_bot.db")
        
        if db_path.exists():
            size_mb = db_path.stat().st_size / 1024 / 1024
            self.log_info(f"Database size: {size_mb:.2f} MB")
            
            try:
                import sqlite3
                conn = sqlite3.connect(str(db_path), timeout=5.0)
                cursor = conn.cursor()
                
                # Check database integrity
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                
                if result[0] == 'ok':
                    self.log_info("✅ Database integrity OK")
                else:
                    self.log_error(f"Database integrity issue: {result[0]}")
                    
                # Check table count
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                self.log_info(f"Database tables: {table_count}")
                
                conn.close()
                
            except Exception as e:
                self.log_error(f"Database check failed: {e}")
        else:
            self.log_warning("Database file not found")

    def check_async_compatibility(self):
        """Check async/await compatibility"""
        print("\n🔄 CHECKING ASYNC COMPATIBILITY...")
        
        try:
            # Test basic async functionality
            async def test_async():
                await asyncio.sleep(0.001)
                return "async_test_passed"
                
            # Run the test
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(test_async())
            loop.close()
            
            if result == "async_test_passed":
                self.log_info("✅ Async functionality working")
            else:
                self.log_error("Async test failed")
                
        except Exception as e:
            self.log_error(f"Async compatibility issue: {e}")

    def check_critical_methods(self):
        """Check if critical methods exist in main system"""
        print("\n🔧 CHECKING CRITICAL METHODS...")
        
        try:
            from bybit_bot.main import BybitTradingBotSystem
            system = BybitTradingBotSystem()
            
            critical_methods = [
                'start_all_engines',
                'run',
                'cleanup',
                'initialize_components',
                'generate_hourly_profit_summary',
                'generate_4hour_profit_report',
                '_run_mpc_security_monitor'
            ]
            
            for method_name in critical_methods:
                if hasattr(system, method_name):
                    method = getattr(system, method_name)
                    if callable(method):
                        self.log_info(f"✅ Method {method_name} exists and callable")
                    else:
                        self.log_error(f"Method {method_name} exists but not callable")
                else:
                    self.log_error(f"Missing critical method: {method_name}")
                    
        except Exception as e:
            self.log_error(f"Critical methods check failed: {e}")

    def run_comprehensive_check(self):
        """Run all comprehensive checks"""
        print("🚀 STARTING COMPREHENSIVE PROCESS CHECK...")
        
        self.check_main_system_imports()
        self.check_time_consistency()
        self.check_optimization_components()
        self.check_mcp_components()
        self.check_database_integrity()
        self.check_async_compatibility()
        self.check_critical_methods()
        
        # Final summary
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE CHECK SUMMARY")
        print("=" * 80)
        
        print(f"❌ ERRORS: {len(self.errors)}")
        for error in self.errors:
            print(f"  • {error}")
            
        print(f"\n⚠️  WARNINGS: {len(self.warnings)}")
        for warning in self.warnings:
            print(f"  • {warning}")
            
        if len(self.errors) == 0:
            print("\n✅ ALL CRITICAL CHECKS PASSED")
            print("🎉 SYSTEM IS READY FOR OPERATION")
        else:
            print(f"\n🚨 {len(self.errors)} CRITICAL ERRORS FOUND")
            print("⚠️  SYSTEM NEEDS ATTENTION BEFORE OPERATION")
            
        return len(self.errors) == 0

if __name__ == "__main__":
    checker = ProcessChecker()
    success = checker.run_comprehensive_check()
    
    if not success:
        print("\n❌ COMPREHENSIVE CHECK FAILED")
        sys.exit(1)
    else:
        print("\n✅ COMPREHENSIVE CHECK SUCCESSFUL")
        sys.exit(0)
