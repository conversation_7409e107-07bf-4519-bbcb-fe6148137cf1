#!/usr/bin/env python3
"""
Simple import test to check if main.py can be imported without errors
"""

import sys
import os

# Change to correct directory
os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
sys.path.insert(0, '.')

print("SIMPLE IMPORT TEST")
print("=" * 30)

try:
    print("Testing basic imports...")
    import asyncio
    import logging
    import time
    from datetime import datetime, timezone
    print("✅ Basic imports successful")
    
    print("Testing websockets compatibility...")
    from websockets_compatibility import create_comprehensive_websockets_compatibility
    create_comprehensive_websockets_compatibility()
    print("✅ WebSockets compatibility successful")
    
    print("Testing bybit_bot package...")
    import bybit_bot
    print("✅ bybit_bot package import successful")
    
    print("Testing main module import...")
    from bybit_bot.main import BybitTradingBotSystem
    print("✅ Main module import successful")
    
    print("Testing instance creation...")
    system = BybitTradingBotSystem()
    print("✅ Instance creation successful")
    
    print(f"System attributes:")
    print(f"  is_running: {system.is_running}")
    print(f"  total_profit: {system.total_profit}")
    print(f"  start_time: {system.start_time}")
    
    print("\n✅ ALL TESTS PASSED")
    print("✅ Main.py can be imported and instantiated without errors")
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    import traceback
    traceback.print_exc()
    
print("\nTest complete")
