#!/usr/bin/env python3
"""
TEST MAIN.PY STARTUP
Monitor for warnings, errors, and issues during startup
"""

import sys
import os
import traceback
import warnings
import logging
from io import StringIO

print("🚀 TESTING MAIN.PY STARTUP")
print("=" * 60)

# Capture all warnings
warnings.simplefilter("always")
warning_buffer = StringIO()
warning_handler = logging.StreamHandler(warning_buffer)
warning_handler.setLevel(logging.WARNING)

# Capture all logging
log_buffer = StringIO()
log_handler = logging.StreamHandler(log_buffer)
log_handler.setLevel(logging.DEBUG)

# Add to root logger
root_logger = logging.getLogger()
root_logger.addHandler(warning_handler)
root_logger.addHandler(log_handler)
root_logger.setLevel(logging.DEBUG)

class StartupMonitor:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.imports_successful = []
        self.imports_failed = []
        
    def test_imports(self):
        """Test all critical imports"""
        print("\n📦 TESTING IMPORTS...")
        
        # Add current directory to path
        sys.path.insert(0, '.')
        
        critical_imports = [
            'bybit_bot.main',
            'bybit_bot.core.optimization_manager',
            'bybit_bot.streaming.advanced_kafka_flink_processor',
            'bybit_bot.ai.multi_agent_rl_system',
            'bybit_bot.mcp.mpc_engine'
        ]
        
        for module_name in critical_imports:
            try:
                module = __import__(module_name, fromlist=[''])
                self.imports_successful.append(module_name)
                print(f"✅ {module_name}")
            except Exception as e:
                self.imports_failed.append((module_name, str(e)))
                print(f"❌ {module_name}: {e}")
                
    def test_main_system_creation(self):
        """Test creating main system instance"""
        print("\n🏗️  TESTING MAIN SYSTEM CREATION...")
        
        try:
            from bybit_bot.main import BybitTradingBotSystem
            
            # Create instance
            system = BybitTradingBotSystem()
            print("✅ Main system instance created successfully")
            
            # Test critical attributes
            critical_attrs = [
                'is_running', 'start_time', 'total_profit', 'total_trades',
                'session_stats', 'profit_targets', 'hourly_stats'
            ]
            
            missing_attrs = []
            for attr in critical_attrs:
                if hasattr(system, attr):
                    print(f"✅ Attribute {attr}: OK")
                else:
                    missing_attrs.append(attr)
                    print(f"❌ Attribute {attr}: MISSING")
                    
            if missing_attrs:
                self.errors.append(f"Missing attributes: {missing_attrs}")
                
            # Test critical methods
            critical_methods = [
                'start_all_engines', 'run', 'cleanup', 'initialize_components'
            ]
            
            missing_methods = []
            for method in critical_methods:
                if hasattr(system, method) and callable(getattr(system, method)):
                    print(f"✅ Method {method}: OK")
                else:
                    missing_methods.append(method)
                    print(f"❌ Method {method}: MISSING OR NOT CALLABLE")
                    
            if missing_methods:
                self.errors.append(f"Missing methods: {missing_methods}")
                
        except Exception as e:
            self.errors.append(f"Main system creation failed: {e}")
            print(f"❌ Main system creation failed: {e}")
            traceback.print_exc()
            
    def test_optimization_manager(self):
        """Test optimization manager initialization"""
        print("\n⚡ TESTING OPTIMIZATION MANAGER...")
        
        try:
            from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
            
            # Create config
            config = OptimizationConfig(
                enable_streaming=True,
                enable_compression=True,
                enable_multi_agent_rl=True,
                enable_graph_nn=True,
                enable_edge_computing=True,
                enable_quantum=True,
                enable_feature_engineering=True,
                enable_adaptive_learning=True
            )
            
            # Create manager
            manager = OptimizationComponentsManager(config)
            print("✅ Optimization manager created successfully")
            
            # Check component status
            print(f"Component status: {manager.component_status}")
            
        except Exception as e:
            self.errors.append(f"Optimization manager test failed: {e}")
            print(f"❌ Optimization manager test failed: {e}")
            traceback.print_exc()
            
    def check_captured_warnings(self):
        """Check for any captured warnings"""
        print("\n⚠️  CHECKING FOR WARNINGS...")
        
        warning_content = warning_buffer.getvalue()
        log_content = log_buffer.getvalue()
        
        if warning_content.strip():
            print("⚠️  WARNINGS FOUND:")
            for line in warning_content.strip().split('\n'):
                if line.strip():
                    print(f"  • {line}")
                    self.warnings.append(line)
        else:
            print("✅ No warnings captured")
            
        if "ERROR" in log_content or "CRITICAL" in log_content:
            print("❌ ERRORS FOUND IN LOGS:")
            for line in log_content.split('\n'):
                if "ERROR" in line or "CRITICAL" in line:
                    print(f"  • {line}")
                    self.errors.append(line)
        else:
            print("✅ No errors in logs")
            
    def run_startup_test(self):
        """Run complete startup test"""
        print("🚀 STARTING MAIN.PY STARTUP TEST...")
        
        self.test_imports()
        self.test_main_system_creation()
        self.test_optimization_manager()
        self.check_captured_warnings()
        
        # Final summary
        print("\n" + "=" * 60)
        print("📊 STARTUP TEST SUMMARY")
        print("=" * 60)
        
        print(f"✅ Successful imports: {len(self.imports_successful)}")
        for imp in self.imports_successful:
            print(f"  • {imp}")
            
        if self.imports_failed:
            print(f"\n❌ Failed imports: {len(self.imports_failed)}")
            for imp, error in self.imports_failed:
                print(f"  • {imp}: {error}")
                
        if self.errors:
            print(f"\n❌ ERRORS: {len(self.errors)}")
            for error in self.errors:
                print(f"  • {error}")
                
        if self.warnings:
            print(f"\n⚠️  WARNINGS: {len(self.warnings)}")
            for warning in self.warnings:
                print(f"  • {warning}")
                
        # Overall assessment
        if len(self.errors) == 0 and len(self.imports_failed) == 0:
            print("\n🎉 STARTUP TEST: PASSED")
            print("✅ Main.py is ready to run without issues")
            return True
        else:
            print(f"\n🚨 STARTUP TEST: FAILED")
            print(f"❌ Found {len(self.errors)} errors and {len(self.imports_failed)} import failures")
            return False

if __name__ == "__main__":
    monitor = StartupMonitor()
    success = monitor.run_startup_test()
    
    if success:
        print("\n✅ MAIN.PY STARTUP TEST SUCCESSFUL")
        sys.exit(0)
    else:
        print("\n❌ MAIN.PY STARTUP TEST FAILED")
        sys.exit(1)
