#!/usr/bin/env python3
"""
Final comprehensive test of main.py
Direct execution with error capture and time sync check
"""

import os
import sys
import time
import subprocess
from datetime import datetime, timezone
from pathlib import Path

def check_time_synchronization():
    """Check system time synchronization"""
    print("TIME SYNCHRONIZATION CHECK")
    print("-" * 30)

    try:
        # Get system time
        system_time = datetime.now(timezone.utc)
        print(f"System UTC time: {system_time}")

        # Check if time seems reasonable (not too far in past/future)
        current_year = datetime.now().year
        if system_time.year == current_year:
            print("SUCCESS: System time appears synchronized")
            return True
        else:
            print(f"WARNING: System time may be incorrect (year: {system_time.year})")
            return False

    except Exception as e:
        print(f"ERROR: Time check error: {e}")
        return False

def direct_main_test():
    """Direct test of main.py execution"""
    print("\nDIRECT MAIN.PY TEST")
    print("=" * 50)

    # Setup environment
    target_dir = r'E:\The_real_deal_copy\Bybit_Bot\BOT'

    if not os.path.exists(target_dir):
        print(f"ERROR: Directory not found: {target_dir}")
        return False

    os.chdir(target_dir)
    print(f"SUCCESS: Working directory: {os.getcwd()}")

    # Check main.py exists
    main_py = Path("bybit_bot/main.py")
    if not main_py.exists():
        print(f"ERROR: main.py not found: {main_py}")
        return False

    print(f"SUCCESS: main.py found: {main_py}")

    # Test 1: Syntax check
    print("\nTest 1: Syntax Check")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'py_compile', 'bybit_bot/main.py'
        ], capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print("SUCCESS: Syntax check PASSED")
        else:
            print("ERROR: Syntax check FAILED")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"ERROR: Syntax check error: {e}")
        return False
    
    # Test 2: Import test
    print("\nTest 2: Import Test")
    import_test = '''
import sys
import os
sys.path.insert(0, '.')

print("Starting import test...")

try:
    print("Testing basic imports...")
    import asyncio
    import logging
    from pathlib import Path
    print("SUCCESS: Basic imports OK")

    print("Testing websockets compatibility...")
    try:
        from websockets_compatibility import create_comprehensive_websockets_compatibility
        print("SUCCESS: websockets_compatibility found")
    except ImportError:
        print("WARNING: websockets_compatibility not found, using fallback")

    print("Testing bybit_bot package...")
    import bybit_bot
    print("SUCCESS: bybit_bot package OK")

    print("IMPORT_TEST_SUCCESS")

except Exception as e:
    print(f"ERROR: Import error: {e}")
    import traceback
    traceback.print_exc()
'''
    
    try:
        result = subprocess.run([
            sys.executable, '-c', import_test
        ], capture_output=True, text=True, timeout=60)
        
        print("Import test output:")
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        if "IMPORT_TEST_SUCCESS" in result.stdout:
            print("SUCCESS: Import test PASSED")
        else:
            print("ERROR: Import test FAILED")
            return False

    except Exception as e:
        print(f"ERROR: Import test error: {e}")
        return False

    # Test 3: Quick execution test
    print("\nTest 3: Quick Execution Test")
    try:
        # Try to run main.py with --test flag for 30 seconds
        process = subprocess.Popen([
            sys.executable, 'bybit_bot/main.py', '--test'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for 30 seconds or until process completes
        try:
            stdout, stderr = process.communicate(timeout=30)
        except subprocess.TimeoutExpired:
            process.terminate()
            stdout, stderr = process.communicate()
            print("WARNING: Process terminated after 30 seconds (normal for test)")

        print("Execution output:")
        if stdout:
            lines = stdout.split('\n')
            for line in lines[:20]:  # Show first 20 lines
                if line.strip():
                    print(f"  {line}")
            if len(lines) > 20:
                print(f"  ... and {len(lines) - 20} more lines")

        if stderr:
            print("Execution errors:")
            error_lines = stderr.split('\n')
            for line in error_lines[:10]:  # Show first 10 error lines
                if line.strip():
                    print(f"  ERROR: {line}")
            if len(error_lines) > 10:
                print(f"  ... and {len(error_lines) - 10} more error lines")

        # Analyze output for issues
        error_count = len([line for line in stderr.split('\n') if line.strip() and 'error' in line.lower()])
        warning_count = len([line for line in stdout.split('\n') if line.strip() and 'warning' in line.lower()])

        print(f"\nEXECUTION ANALYSIS:")
        print(f"  Exit code: {process.returncode}")
        print(f"  Error lines: {error_count}")
        print(f"  Warning lines: {warning_count}")

        if error_count == 0:
            print("SUCCESS: Execution test PASSED (no critical errors)")
            return True
        else:
            print(f"WARNING: Execution test completed with {error_count} errors")
            return True  # Still consider success if it ran

    except Exception as e:
        print(f"ERROR: Execution test error: {e}")
        return False

def main():
    """Main test function"""
    print("FINAL COMPREHENSIVE MAIN.PY TEST")
    print("=" * 50)
    print(f"Test started at: {datetime.now()}")
    
    # Run all tests
    time_ok = check_time_synchronization()
    main_ok = direct_main_test()
    
    # Final assessment
    print(f"\nFINAL TEST RESULTS:")
    print(f"  Time sync: {'SUCCESS' if time_ok else 'ERROR'}")
    print(f"  Main.py: {'SUCCESS' if main_ok else 'ERROR'}")

    if time_ok and main_ok:
        print("\nALL TESTS PASSED")
        print("SUCCESS: main.py appears to be working correctly")
        print("SUCCESS: No critical errors detected")
        return True
    else:
        print("\nSOME ISSUES DETECTED")
        if not time_ok:
            print("ERROR: Time synchronization issues")
        if not main_ok:
            print("ERROR: Main.py execution issues")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nTest completed at: {datetime.now()}")
    if success:
        print("COMPREHENSIVE TEST SUCCESSFUL")
    else:
        print("COMPREHENSIVE TEST FAILED")
