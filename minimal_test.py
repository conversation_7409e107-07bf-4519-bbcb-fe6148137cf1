#!/usr/bin/env python3
"""
Minimal test - NO UNICODE
"""

import os
import sys

print("MINIMAL MAIN.PY TEST")
print("=" * 25)

# Change to correct directory
os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
print(f"Directory: {os.getcwd()}")

# Add to path
sys.path.insert(0, '.')

# Test 1: Basic import
print("\n1. Testing basic import...")
try:
    import bybit_bot
    print("SUCCESS: bybit_bot imported")
except Exception as e:
    print(f"FAILED: {e}")
    sys.exit(1)

# Test 2: Main module import
print("\n2. Testing main module...")
try:
    from bybit_bot import main
    print("SUCCESS: main module imported")
except Exception as e:
    print(f"FAILED: {e}")
    sys.exit(1)

# Test 3: Class import
print("\n3. Testing class import...")
try:
    from bybit_bot.main import BybitTradingBotSystem
    print("SUCCESS: BybitTradingBotSystem imported")
except Exception as e:
    print(f"FAILED: {e}")
    sys.exit(1)

# Test 4: Instance creation
print("\n4. Testing instance creation...")
try:
    system = BybitTradingBotSystem()
    print("SUCCESS: Instance created")
    print(f"  is_running: {system.is_running}")
    print(f"  start_time: {system.start_time}")
    print(f"  total_profit: {system.total_profit}")
except Exception as e:
    print(f"FAILED: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\nALL TESTS PASSED")
print("main.py basic functionality works")
