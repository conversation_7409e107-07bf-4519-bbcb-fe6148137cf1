#!/usr/bin/env python3
"""
Quick test of main.py - NO UNICODE
"""

import os
import sys
import glob
from datetime import datetime

def quick_cleanup():
    """Quick cleanup of backup files"""
    print("QUICK BACKUP CLEANUP")
    print("=" * 30)
    
    # Clean main.py backups
    main_backups = glob.glob("bybit_bot/main.py.backup_*")
    if len(main_backups) > 5:
        main_backups.sort()
        to_remove = main_backups[:-3]  # Keep only 3 most recent
        for f in to_remove[:20]:  # Remove max 20 at a time
            try:
                os.remove(f)
                print(f"Removed: {f}")
            except:
                pass
        print(f"Cleaned {len(to_remove[:20])} main.py backups")
    
    # Clean autonomous_sync_manager backups
    sync_backups = glob.glob("autonomous_sync_manager.py.backup_*")
    if len(sync_backups) > 5:
        sync_backups.sort()
        to_remove = sync_backups[:-2]  # Keep only 2 most recent
        for f in to_remove[:30]:  # Remove max 30 at a time
            try:
                os.remove(f)
                print(f"Removed: {f}")
            except:
                pass
        print(f"Cleaned {len(to_remove[:30])} sync manager backups")

def quick_test():
    """Quick test of main.py"""
    print("QUICK MAIN.PY TEST")
    print("=" * 30)
    print(f"Started: {datetime.now()}")
    
    # Change to correct directory
    os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
    print(f"Directory: {os.getcwd()}")
    
    # Quick cleanup
    quick_cleanup()
    
    # Test basic import
    print("\nTesting basic import...")
    try:
        sys.path.insert(0, '.')
        import bybit_bot
        print("SUCCESS: bybit_bot package imported")
    except Exception as e:
        print(f"ERROR: Import failed - {e}")
        return False
    
    # Test main module import
    print("\nTesting main module import...")
    try:
        from bybit_bot import main
        print("SUCCESS: main module imported")
    except Exception as e:
        print(f"ERROR: Main module import failed - {e}")
        return False
    
    # Test syntax compilation
    print("\nTesting syntax compilation...")
    try:
        import py_compile
        py_compile.compile('bybit_bot/main.py', doraise=True)
        print("SUCCESS: Syntax compilation passed")
    except Exception as e:
        print(f"ERROR: Syntax compilation failed - {e}")
        return False
    
    # Try to create system instance
    print("\nTesting system instantiation...")
    try:
        # Import the main class
        from bybit_bot.main import BybitTradingBotSystem
        
        # Create instance
        system = BybitTradingBotSystem()
        print("SUCCESS: System instance created")
        
        # Check basic attributes
        if hasattr(system, 'is_running'):
            print("SUCCESS: is_running attribute exists")
        else:
            print("WARNING: is_running attribute missing")
        
        if hasattr(system, 'start_time'):
            print("SUCCESS: start_time attribute exists")
        else:
            print("WARNING: start_time attribute missing")
        
        return True
        
    except Exception as e:
        print(f"ERROR: System instantiation failed - {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    success = quick_test()
    
    print(f"\nCompleted: {datetime.now()}")
    print(f"RESULT: {'SUCCESS' if success else 'FAILED'}")
    
    if success:
        print("main.py basic functionality works")
    else:
        print("main.py has issues")
    
    return success

if __name__ == "__main__":
    result = main()
    if result:
        print("\nSUCCESS: Quick test passed")
    else:
        print("\nFAILED: Quick test failed")
