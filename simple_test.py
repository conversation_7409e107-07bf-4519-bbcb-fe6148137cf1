#!/usr/bin/env python3
import sys
import os
sys.path.append('.')

print("TESTING MAIN.PY FOR ERRORS")
print("=" * 50)

# Test 1: Syntax Check
print("1. Syntax Check...")
try:
    import ast
    with open('bybit_bot/main.py', 'r') as f:
        source = f.read()
    ast.parse(source)
    print("   ✓ No syntax errors")
except SyntaxError as e:
    print(f"   ✗ Syntax error: Line {e.lineno}: {e.msg}")
    sys.exit(1)
except Exception as e:
    print(f"   ✗ Error: {e}")
    sys.exit(1)

# Test 2: Import Test
print("2. Import Test...")
try:
    from bybit_bot.main import BybitTradingBotSystem
    print("   ✓ Main system imported")
except Exception as e:
    print(f"   ✗ Import error: {e}")
    sys.exit(1)

# Test 3: System Creation
print("3. System Creation...")
try:
    system = BybitTradingBotSystem()
    print("   ✓ System created")
except Exception as e:
    print(f"   ✗ Creation error: {e}")
    sys.exit(1)

# Test 4: Optimization Manager
print("4. Optimization Manager...")
try:
    from bybit_bot.core.optimization_manager import OptimizationComponentsManager, OptimizationConfig
    config = OptimizationConfig()
    manager = OptimizationComponentsManager(config)
    print("   ✓ Optimization manager created")
except Exception as e:
    print(f"   ✗ Optimization error: {e}")
    sys.exit(1)

print("\n✅ ALL TESTS PASSED - NO ERRORS FOUND!")
print("✅ main.py is ready for execution")
