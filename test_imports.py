#!/usr/bin/env python3
"""
Test script to identify which imports are failing
"""
import sys
import traceback

def test_import(module_name, description=""):
    """Test a single import and report result"""
    try:
        if "." in module_name:
            # Handle from X import Y syntax
            parts = module_name.split(".")
            if len(parts) >= 2:
                module_path = ".".join(parts[:-1])
                class_name = parts[-1]
                exec(f"from {module_path} import {class_name}")
        else:
            exec(f"import {module_name}")
        print(f"✓ {module_name} - {description}")
        return True
    except Exception as e:
        print(f"✗ {module_name} - {description} - ERROR: {e}")
        return False

def main():
    print("IMPORT TESTING - IDENTIFYING FAILING MODULES")
    print("=" * 60)
    
    # Add current directory to path
    sys.path.append('.')
    
    failed_imports = []
    
    # Test critical imports from main.py
    imports_to_test = [
        ("bybit_bot.core.config.EnhancedBotConfig", "Enhanced Bot Configuration"),
        ("bybit_bot.exchange.enhanced_bybit_client.EnhancedBybitClient", "Enhanced Bybit Client"),
        ("bybit_bot.exchange.bybit_client.BybitClient", "Basic Bybit Client"),
        ("bybit_bot.database.connection.DatabaseManager", "Database Manager"),
        ("bybit_bot.ai.enhanced_multi_timeframe_learner.EnhancedMultiTimeframeLearner", "Enhanced Multi-Timeframe Learner"),
        ("bybit_bot.ai.tier1_integration_manager.Tier1IntegrationManager", "Tier 1 Integration Manager"),
        ("bybit_bot.ai.advanced_data_integration_engine.AdvancedDataIntegrationEngine", "Advanced Data Integration Engine"),
        ("bybit_bot.ai.adaptive_learning_engine.AdaptiveLearningEngine", "Adaptive Learning Engine"),
        ("bybit_bot.ai.ai_folder_activation_manager.activate_ai_folder", "AI Folder Activation"),
        ("bybit_bot.core.enhanced_time_manager.EnhancedTimeManager", "Enhanced Time Manager"),
        ("bybit_bot.agents.agent_orchestrator.AgentOrchestrator", "Agent Orchestrator"),
        ("bybit_bot.ai.intelligent_ml_system.IntelligentMLSystem", "Intelligent ML System"),
        ("bybit_bot.mcp.mpc_engine.MPCCryptographicEngine", "MPC Cryptographic Engine"),
        ("bybit_bot.mcp.threshold_signatures.ThresholdSignatureManager", "Threshold Signature Manager"),
        ("bybit_bot.mcp.bybit_mpc_wallet.BybitMPCWallet", "Bybit MPC Wallet"),
        ("bybit_bot.mcp.mpc_security_layer.MPCSecurityLayer", "MPC Security Layer"),
    ]
    
    print("Testing critical imports...")
    for module_name, description in imports_to_test:
        if not test_import(module_name, description):
            failed_imports.append((module_name, description))
    
    print("\n" + "=" * 60)
    if failed_imports:
        print(f"FAILED IMPORTS ({len(failed_imports)}):")
        for module_name, description in failed_imports:
            print(f"  ✗ {module_name} - {description}")
        
        print("\nTesting first failed import in detail...")
        if failed_imports:
            module_name = failed_imports[0][0]
            try:
                if "." in module_name:
                    parts = module_name.split(".")
                    module_path = ".".join(parts[:-1])
                    class_name = parts[-1]
                    exec(f"from {module_path} import {class_name}")
            except Exception as e:
                print(f"DETAILED ERROR for {module_name}:")
                traceback.print_exc()
        
        return False
    else:
        print("ALL IMPORTS SUCCESSFUL!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
