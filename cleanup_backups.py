#!/usr/bin/env python3
"""
Cleanup backup files - ASCII only
"""

import os
import glob
from datetime import datetime

def cleanup_backups():
    print("BACKUP CLEANUP")
    print("=" * 20)
    print(f"Started: {datetime.now()}")
    
    # Change to correct directory
    os.chdir(r'E:\The_real_deal_copy\Bybit_Bot\BOT')
    print(f"Directory: {os.getcwd()}")
    
    # Find backup files
    patterns = [
        "**/*.backup_*",
        "**/main.py.backup_*",
        "**/autonomous_sync_manager.py.backup_*"
    ]
    
    total_removed = 0
    
    for pattern in patterns:
        backup_files = glob.glob(pattern, recursive=True)
        print(f"\nFound {len(backup_files)} files matching {pattern}")
        
        if len(backup_files) > 5:
            # Sort by modification time, keep 3 most recent
            backup_files.sort(key=lambda x: os.path.getmtime(x))
            to_remove = backup_files[:-3]  # Keep 3 most recent
            
            # Remove max 50 at a time to avoid overwhelming
            for f in to_remove[:50]:
                try:
                    os.remove(f)
                    print(f"Removed: {f}")
                    total_removed += 1
                except Exception as e:
                    print(f"Failed to remove {f}: {e}")
    
    print(f"\nTotal files removed: {total_removed}")
    print("Cleanup completed")
    return total_removed

if __name__ == "__main__":
    cleanup_backups()

# Execute when imported
cleanup_backups()
